
csv['draw_card'] = {
	[1] = {
		id = 1,
		drawType = 'gold1',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[220] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[221] = 1, __size = 1},
		lotteryWeight2 = 25,
		lotteryType3 = {[222] = 1, __size = 1},
		lotteryWeight3 = 30
	},
	[2] = {
		id = 2,
		drawType = 'gold10',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[220] = 4, [221] = 3, [222] = 3, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[220] = 3, [221] = 3, [222] = 4, __size = 3},
		lotteryWeight2 = 80,
		lotteryType3 = {[220] = 5, [221] = 3, [222] = 2, __size = 3},
		lotteryWeight3 = 120,
		lotteryType4 = {[220] = 5, [221] = 2, [222] = 3, __size = 3},
		lotteryWeight4 = 120,
		lotteryType5 = {[220] = 4, [221] = 2, [222] = 4, __size = 3},
		lotteryWeight5 = 100,
		lotteryType6 = {[220] = 5, [221] = 2, [222] = 3, __size = 3},
		lotteryWeight6 = 120
	},
	[11] = {
		id = 11,
		drawType = 'rmb1',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[203] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[12] = {
		id = 12,
		drawType = 'rmb1',
		drawTriggerType = 4,
		drawTriggerTimes = 5,
		lotteryType1 = {[207] = 1, __size = 1},
		lotteryWeight1 = 50
	},
	[13] = {
		id = 13,
		drawType = 'rmb1',
		drawTriggerTimes = 6,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.02,
		probLimit = 0.3,
		probMiniTimes = 15,
		probEffectInterval = 20,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 80
	},
	[14] = {
		id = 14,
		drawType = 'rmb1',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 100,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 100
	},
	[15] = {
		id = 15,
		drawType = 'rmb1',
		drawTriggerType = 4,
		drawTriggerTimes = 2,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[16] = {
		id = 16,
		drawType = 'rmb1',
		drawTriggerTimes = 6,
		startCount = 30,
		probInit = 0.02,
		probStep = 0.006,
		probLimit = 0.5,
		probEffectInterval = 80,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 80
	},
	[21] = {
		id = 21,
		drawType = 'rmb10',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[205] = 3, [206] = 6, [207] = 1, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[205] = 4, [206] = 5, [207] = 1, __size = 3},
		lotteryWeight2 = 100
	},
	[22] = {
		id = 22,
		drawType = 'rmb10',
		drawTriggerTimes = 2,
		effectLimit = 1,
		startCount = 4,
		probInit = 0.5,
		probStep = 0.5,
		probLimit = 1,
		probMiniTimes = 2,
		probEffectInterval = 999999,
		lotteryType1 = {[201] = 1, [206] = 6, [205] = 3, __size = 3},
		lotteryType2 = {[201] = 1, [206] = 5, [205] = 4, __size = 3},
		lotteryType3 = {[201] = 1, [206] = 4, [205] = 5, __size = 3},
		lotteryType4 = {[201] = 2, [206] = 5, [205] = 3, __size = 3},
		lotteryWeight4 = 20,
		lotteryType5 = {[201] = 2, [206] = 4, [205] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[201] = 2, [206] = 3, [205] = 5, __size = 3},
		lotteryWeight6 = 20,
		lotteryType7 = {[201] = 3, [206] = 5, [205] = 2, __size = 3},
		lotteryType8 = {[201] = 3, [206] = 4, [205] = 3, __size = 3},
		lotteryType9 = {[201] = 3, [206] = 3, [205] = 4, __size = 3}
	},
	[23] = {
		id = 23,
		drawType = 'rmb10',
		drawTriggerTimes = 2,
		effectLimit = 1,
		startCount = 10,
		probInit = 0.01,
		probStep = 0.005,
		probLimit = 0.5,
		probMiniTimes = 40,
		lotteryType1 = {[201] = 1, [206] = 6, [205] = 3, __size = 3},
		lotteryType2 = {[201] = 1, [206] = 5, [205] = 4, __size = 3},
		lotteryType3 = {[201] = 1, [206] = 4, [205] = 5, __size = 3},
		lotteryType4 = {[201] = 2, [206] = 5, [205] = 3, __size = 3},
		lotteryType5 = {[201] = 2, [206] = 4, [205] = 4, __size = 3},
		lotteryType6 = {[201] = 2, [206] = 3, [205] = 5, __size = 3},
		lotteryType7 = {[201] = 3, [206] = 5, [205] = 2, __size = 3},
		lotteryWeight7 = 20,
		lotteryType8 = {[201] = 3, [206] = 4, [205] = 3, __size = 3},
		lotteryWeight8 = 20,
		lotteryType9 = {[201] = 3, [206] = 3, [205] = 4, __size = 3},
		lotteryWeight9 = 20
	},
	[24] = {
		id = 24,
		drawType = 'rmb10',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[201] = 1, [206] = 6, [205] = 3, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[201] = 1, [206] = 5, [205] = 4, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[201] = 1, [206] = 4, [205] = 5, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[201] = 2, [206] = 5, [205] = 3, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[201] = 2, [206] = 4, [205] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[201] = 2, [206] = 3, [205] = 5, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[201] = 3, [206] = 5, [205] = 2, __size = 3},
		lotteryType8 = {[201] = 3, [206] = 4, [205] = 3, __size = 3},
		lotteryType9 = {[201] = 3, [206] = 3, [205] = 4, __size = 3}
	},
	[51] = {
		id = 51,
		drawType = 'limit_up_rmb1_81',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[52] = {
		id = 52,
		drawType = 'limit_up_rmb1_81',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[53] = {
		id = 53,
		drawType = 'limit_up_rmb1_81',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[54] = {
		id = 54,
		drawType = 'limit_up_rmb1_81',
		drawTriggerTimes = 6,
		effectLimit = 10,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[55] = {
		id = 55,
		drawType = 'limit_up_rmb1_81',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[61] = {
		id = 61,
		drawType = 'limit_up_rmb10_81',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[1204] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[1204] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100
	},
	[62] = {
		id = 62,
		drawType = 'limit_up_rmb10_81',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		effectLimit = 20,
		lotteryType1 = {[1204] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1204] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1204] = 1, [205] = 5, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1204] = 2, [205] = 3, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1204] = 2, [205] = 4, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1204] = 2, [205] = 5, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1204] = 3, [205] = 2, [206] = 5, __size = 3},
		lotteryType8 = {[1204] = 3, [205] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[1204] = 3, [205] = 4, [206] = 3, __size = 3}
	},
	[63] = {
		id = 63,
		drawType = 'limit_up_rmb10_81',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[1205] = 1, [206] = 6, [205] = 3, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1205] = 1, [206] = 5, [205] = 4, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1205] = 1, [206] = 4, [205] = 5, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1205] = 2, [206] = 5, [205] = 3, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1205] = 2, [206] = 4, [205] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1205] = 2, [206] = 3, [205] = 5, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1205] = 3, [206] = 5, [205] = 2, __size = 3},
		lotteryType8 = {[1205] = 3, [206] = 4, [205] = 3, __size = 3},
		lotteryType9 = {[1205] = 3, [206] = 3, [205] = 4, __size = 3}
	},
	[101] = {
		id = 101,
		drawType = 'limit_up_rmb1_111',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[102] = {
		id = 102,
		drawType = 'limit_up_rmb1_111',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[103] = {
		id = 103,
		drawType = 'limit_up_rmb1_111',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1206] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[104] = {
		id = 104,
		drawType = 'limit_up_rmb1_111',
		drawTriggerTimes = 6,
		effectLimit = 10,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1206] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[105] = {
		id = 105,
		drawType = 'limit_up_rmb1_111',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1206] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[111] = {
		id = 111,
		drawType = 'limit_up_rmb10_111',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[205] = 3, [1206] = 1, [206] = 6, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[205] = 4, [1206] = 1, [206] = 5, __size = 3},
		lotteryWeight2 = 100
	},
	[112] = {
		id = 112,
		drawType = 'limit_up_rmb10_111',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		effectLimit = 20,
		lotteryType1 = {[205] = 3, [1206] = 1, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [1206] = 1, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [1206] = 1, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [1206] = 2, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [1206] = 2, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [1206] = 2, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [1206] = 3, [206] = 5, __size = 3},
		lotteryType8 = {[205] = 3, [1206] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[205] = 4, [1206] = 3, [206] = 3, __size = 3}
	},
	[113] = {
		id = 113,
		drawType = 'limit_up_rmb10_111',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[205] = 3, [206] = 6, [1207] = 1, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [206] = 5, [1207] = 1, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [206] = 4, [1207] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [206] = 5, [1207] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [206] = 4, [1207] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [206] = 3, [1207] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [206] = 5, [1207] = 3, __size = 3},
		lotteryType8 = {[205] = 3, [206] = 4, [1207] = 3, __size = 3},
		lotteryType9 = {[205] = 4, [206] = 3, [1207] = 3, __size = 3}
	},
	[121] = {
		id = 121,
		drawType = 'limit_up_rmb1_116',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[122] = {
		id = 122,
		drawType = 'limit_up_rmb1_116',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[123] = {
		id = 123,
		drawType = 'limit_up_rmb1_116',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1208] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[124] = {
		id = 124,
		drawType = 'limit_up_rmb1_116',
		drawTriggerTimes = 6,
		effectLimit = 10,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1208] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[125] = {
		id = 125,
		drawType = 'limit_up_rmb1_116',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1208] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[131] = {
		id = 131,
		drawType = 'limit_up_rmb10_116',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[1208] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[1208] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100
	},
	[132] = {
		id = 132,
		drawType = 'limit_up_rmb10_116',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		effectLimit = 20,
		lotteryType1 = {[1208] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1208] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1208] = 1, [205] = 5, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1208] = 2, [205] = 3, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1208] = 2, [205] = 4, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1208] = 2, [205] = 5, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1208] = 3, [205] = 2, [206] = 5, __size = 3},
		lotteryType8 = {[1208] = 3, [205] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[1208] = 3, [205] = 4, [206] = 3, __size = 3}
	},
	[133] = {
		id = 133,
		drawType = 'limit_up_rmb10_116',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[205] = 3, [206] = 6, [1209] = 1, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [206] = 5, [1209] = 1, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [206] = 4, [1209] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [206] = 5, [1209] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [206] = 4, [1209] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [206] = 3, [1209] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [206] = 5, [1209] = 3, __size = 3},
		lotteryType8 = {[205] = 3, [206] = 4, [1209] = 3, __size = 3},
		lotteryType9 = {[205] = 4, [206] = 3, [1209] = 3, __size = 3}
	},
	[141] = {
		id = 141,
		drawType = 'limit_up_rmb1_121',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[142] = {
		id = 142,
		drawType = 'limit_up_rmb1_121',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[143] = {
		id = 143,
		drawType = 'limit_up_rmb1_121',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1210] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[144] = {
		id = 144,
		drawType = 'limit_up_rmb1_121',
		drawTriggerTimes = 6,
		effectLimit = 10,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1210] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[145] = {
		id = 145,
		drawType = 'limit_up_rmb1_121',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1210] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[151] = {
		id = 151,
		drawType = 'limit_up_rmb10_121',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[205] = 3, [1210] = 1, [206] = 6, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[205] = 4, [1210] = 1, [206] = 5, __size = 3},
		lotteryWeight2 = 100
	},
	[152] = {
		id = 152,
		drawType = 'limit_up_rmb10_121',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		effectLimit = 20,
		lotteryType1 = {[205] = 3, [1210] = 1, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [1210] = 1, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [1210] = 1, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [1210] = 2, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [1210] = 2, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [1210] = 2, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [1210] = 3, [206] = 5, __size = 3},
		lotteryType8 = {[205] = 3, [1210] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[205] = 4, [1210] = 3, [206] = 3, __size = 3}
	},
	[153] = {
		id = 153,
		drawType = 'limit_up_rmb10_121',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[205] = 3, [206] = 6, [1211] = 1, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [206] = 5, [1211] = 1, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [206] = 4, [1211] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [206] = 5, [1211] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [206] = 4, [1211] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [206] = 3, [1211] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [206] = 5, [1211] = 3, __size = 3},
		lotteryType8 = {[205] = 3, [206] = 4, [1211] = 3, __size = 3},
		lotteryType9 = {[205] = 4, [206] = 3, [1211] = 3, __size = 3}
	},
	[170] = {
		id = 170,
		drawType = 'limit_rmb1_100012',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1111] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[171] = {
		id = 171,
		drawType = 'limit_rmb1_100012',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[172] = {
		id = 172,
		drawType = 'limit_rmb1_100012',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1113] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[173] = {
		id = 173,
		drawType = 'limit_rmb1_100012',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1113] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[180] = {
		id = 180,
		drawType = 'limit_rmb1_100022',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1151] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[181] = {
		id = 181,
		drawType = 'limit_rmb1_100022',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[182] = {
		id = 182,
		drawType = 'limit_rmb1_100022',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1150] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[183] = {
		id = 183,
		drawType = 'limit_rmb1_100022',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1150] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[201] = {
		id = 201,
		drawType = 'limit_box_rmb1_100032',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[3002] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[202] = {
		id = 202,
		drawType = 'limit_box_rmb1_100032',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[3001] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[203] = {
		id = 203,
		drawType = 'limit_box_rmb10_100032',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[3001] = 1, [3002] = 4, [3003] = 5, __size = 3},
		lotteryWeight1 = 100
	},
	[300] = {
		id = 300,
		drawType = 'limit_up_rmb1_100037',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[30] = 1, __size = 1},
		lotteryWeight1 = 5,
		lotteryType2 = {[31] = 1, __size = 1},
		lotteryWeight2 = 95
	},
	[301] = {
		id = 301,
		drawType = 'limit_up_rmb10_100037',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[30] = 1, [31] = 9, __size = 2},
		lotteryWeight1 = 100
	},
	[310] = {
		id = 310,
		drawType = 'limit_up_rmb1_100038',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[32] = 1, __size = 1},
		lotteryWeight1 = 5,
		lotteryType2 = {[33] = 1, __size = 1},
		lotteryWeight2 = 95
	},
	[311] = {
		id = 311,
		drawType = 'limit_up_rmb10_100038',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[32] = 1, [33] = 9, __size = 2},
		lotteryWeight1 = 100
	},
	[320] = {
		id = 320,
		drawType = 'limit_up_rmb1_100039',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[34] = 1, __size = 1},
		lotteryWeight1 = 5,
		lotteryType2 = {[35] = 1, __size = 1},
		lotteryWeight2 = 95
	},
	[321] = {
		id = 321,
		drawType = 'limit_up_rmb10_100039',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[34] = 1, [35] = 9, __size = 2},
		lotteryWeight1 = 100
	},
	[1001] = {
		id = 1001,
		drawType = 'limit_box_rmb1_1031',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50
	},
	[1002] = {
		id = 1002,
		drawType = 'limit_box_rmb1_1031',
		drawTriggerTimes = 2,
		startCount = 2,
		probInit = 0.05,
		probStep = 0.02,
		probLimit = 0.3,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1003] = {
		id = 1003,
		drawType = 'limit_box_rmb1_1031',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[205] = 1, __size = 1},
		lotteryWeight2 = 250,
		lotteryType3 = {[1101] = 1, __size = 1},
		lotteryWeight3 = 200,
		lotteryType4 = {[1103] = 1, __size = 1},
		lotteryWeight4 = 400,
		lotteryType5 = {[1111] = 1, __size = 1},
		lotteryWeight5 = 100,
		lotteryType6 = {[1112] = 1, __size = 1},
		lotteryWeight6 = 100
	},
	[1012] = {
		id = 1012,
		drawType = 'limit_box_rmb10_1031',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[1112] = 1, [201] = 1, [1101] = 2, [205] = 2, [1103] = 3, [1111] = 1, __size = 6},
		lotteryWeight1 = 150,
		lotteryType2 = {[1112] = 1, [201] = 1, [1101] = 2, [205] = 2, [1103] = 3, [1111] = 1, __size = 6},
		lotteryWeight2 = 100,
		lotteryType3 = {[1112] = 1, [201] = 1, [1101] = 2, [205] = 2, [1103] = 3, [1111] = 1, __size = 6},
		lotteryWeight3 = 50,
		lotteryType4 = {[1112] = 1, [201] = 2, [1101] = 2, [205] = 1, [1103] = 3, [1111] = 1, __size = 6},
		lotteryWeight4 = 30,
		lotteryType5 = {[1112] = 1, [201] = 2, [1101] = 2, [205] = 1, [1103] = 3, [1111] = 1, __size = 6},
		lotteryWeight5 = 20,
		lotteryType6 = {[1112] = 1, [201] = 2, [1101] = 2, [205] = 1, [1103] = 3, [1111] = 1, __size = 6},
		lotteryWeight6 = 10
	},
	[1021] = {
		id = 1021,
		drawType = 'limit_box_rmb1_1112',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1022] = {
		id = 1022,
		drawType = 'limit_box_rmb1_1112',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1031] = {
		id = 1031,
		drawType = 'limit_box_rmb10_1112',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1032] = {
		id = 1032,
		drawType = 'limit_box_rmb10_1112',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1033] = {
		id = 1033,
		drawType = 'limit_box_rmb10_1112',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1034] = {
		id = 1034,
		drawType = 'limit_box_rmb10_1112',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1035] = {
		id = 1035,
		drawType = 'limit_box_rmb10_1112',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1036] = {
		id = 1036,
		drawType = 'limit_box_rmb10_1112',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1041] = {
		id = 1041,
		drawType = 'limit_box_rmb1_1212',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1042] = {
		id = 1042,
		drawType = 'limit_box_rmb1_1212',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1051] = {
		id = 1051,
		drawType = 'limit_box_rmb10_1212',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1052] = {
		id = 1052,
		drawType = 'limit_box_rmb10_1212',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1053] = {
		id = 1053,
		drawType = 'limit_box_rmb10_1212',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1054] = {
		id = 1054,
		drawType = 'limit_box_rmb10_1212',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1055] = {
		id = 1055,
		drawType = 'limit_box_rmb10_1212',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1056] = {
		id = 1056,
		drawType = 'limit_box_rmb10_1212',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1061] = {
		id = 1061,
		drawType = 'limit_box_rmb1_1291',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1062] = {
		id = 1062,
		drawType = 'limit_box_rmb1_1291',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1071] = {
		id = 1071,
		drawType = 'limit_box_rmb10_1291',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1072] = {
		id = 1072,
		drawType = 'limit_box_rmb10_1291',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1073] = {
		id = 1073,
		drawType = 'limit_box_rmb10_1291',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1074] = {
		id = 1074,
		drawType = 'limit_box_rmb10_1291',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1075] = {
		id = 1075,
		drawType = 'limit_box_rmb10_1291',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1076] = {
		id = 1076,
		drawType = 'limit_box_rmb10_1291',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1081] = {
		id = 1081,
		drawType = 'limit_box_rmb1_1381',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1082] = {
		id = 1082,
		drawType = 'limit_box_rmb1_1381',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1091] = {
		id = 1091,
		drawType = 'limit_box_rmb10_1381',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1092] = {
		id = 1092,
		drawType = 'limit_box_rmb10_1381',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1093] = {
		id = 1093,
		drawType = 'limit_box_rmb10_1381',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1094] = {
		id = 1094,
		drawType = 'limit_box_rmb10_1381',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1095] = {
		id = 1095,
		drawType = 'limit_box_rmb10_1381',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1096] = {
		id = 1096,
		drawType = 'limit_box_rmb10_1381',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1101] = {
		id = 1101,
		drawType = 'limit_box_rmb1_1512',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1102] = {
		id = 1102,
		drawType = 'limit_box_rmb1_1512',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1111] = {
		id = 1111,
		drawType = 'limit_box_rmb10_1512',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1112] = {
		id = 1112,
		drawType = 'limit_box_rmb10_1512',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1113] = {
		id = 1113,
		drawType = 'limit_box_rmb10_1512',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1114] = {
		id = 1114,
		drawType = 'limit_box_rmb10_1512',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1115] = {
		id = 1115,
		drawType = 'limit_box_rmb10_1512',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1116] = {
		id = 1116,
		drawType = 'limit_box_rmb10_1512',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1121] = {
		id = 1121,
		drawType = 'limit_box_rmb1_1642',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1122] = {
		id = 1122,
		drawType = 'limit_box_rmb1_1642',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1131] = {
		id = 1131,
		drawType = 'limit_box_rmb10_1642',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1132] = {
		id = 1132,
		drawType = 'limit_box_rmb10_1642',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1133] = {
		id = 1133,
		drawType = 'limit_box_rmb10_1642',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1134] = {
		id = 1134,
		drawType = 'limit_box_rmb10_1642',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1135] = {
		id = 1135,
		drawType = 'limit_box_rmb10_1642',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1136] = {
		id = 1136,
		drawType = 'limit_box_rmb10_1642',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1141] = {
		id = 1141,
		drawType = 'limit_box_rmb1_1772',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1142] = {
		id = 1142,
		drawType = 'limit_box_rmb1_1772',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1151] = {
		id = 1151,
		drawType = 'limit_box_rmb10_1772',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1152] = {
		id = 1152,
		drawType = 'limit_box_rmb10_1772',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1153] = {
		id = 1153,
		drawType = 'limit_box_rmb10_1772',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1154] = {
		id = 1154,
		drawType = 'limit_box_rmb10_1772',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1155] = {
		id = 1155,
		drawType = 'limit_box_rmb10_1772',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1156] = {
		id = 1156,
		drawType = 'limit_box_rmb10_1772',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1161] = {
		id = 1161,
		drawType = 'limit_box_rmb1_1851',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1162] = {
		id = 1162,
		drawType = 'limit_box_rmb1_1851',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1171] = {
		id = 1171,
		drawType = 'limit_box_rmb10_1851',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1172] = {
		id = 1172,
		drawType = 'limit_box_rmb10_1851',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1173] = {
		id = 1173,
		drawType = 'limit_box_rmb10_1851',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1174] = {
		id = 1174,
		drawType = 'limit_box_rmb10_1851',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1175] = {
		id = 1175,
		drawType = 'limit_box_rmb10_1851',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1176] = {
		id = 1176,
		drawType = 'limit_box_rmb10_1851',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1181] = {
		id = 1181,
		drawType = 'limit_box_rmb1_1941',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1182] = {
		id = 1182,
		drawType = 'limit_box_rmb1_1941',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1191] = {
		id = 1191,
		drawType = 'limit_box_rmb10_1941',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1192] = {
		id = 1192,
		drawType = 'limit_box_rmb10_1941',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1193] = {
		id = 1193,
		drawType = 'limit_box_rmb10_1941',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1194] = {
		id = 1194,
		drawType = 'limit_box_rmb10_1941',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1195] = {
		id = 1195,
		drawType = 'limit_box_rmb10_1941',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1196] = {
		id = 1196,
		drawType = 'limit_box_rmb10_1941',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1201] = {
		id = 1201,
		drawType = 'limit_box_rmb1_2031',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1202] = {
		id = 1202,
		drawType = 'limit_box_rmb1_2031',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1211] = {
		id = 1211,
		drawType = 'limit_box_rmb10_2031',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1212] = {
		id = 1212,
		drawType = 'limit_box_rmb10_2031',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1213] = {
		id = 1213,
		drawType = 'limit_box_rmb10_2031',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1214] = {
		id = 1214,
		drawType = 'limit_box_rmb10_2031',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1215] = {
		id = 1215,
		drawType = 'limit_box_rmb10_2031',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1216] = {
		id = 1216,
		drawType = 'limit_box_rmb10_2031',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1221] = {
		id = 1221,
		drawType = 'limit_box_rmb1_2121',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1222] = {
		id = 1222,
		drawType = 'limit_box_rmb1_2121',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1231] = {
		id = 1231,
		drawType = 'limit_box_rmb10_2121',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1232] = {
		id = 1232,
		drawType = 'limit_box_rmb10_2121',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1233] = {
		id = 1233,
		drawType = 'limit_box_rmb10_2121',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1234] = {
		id = 1234,
		drawType = 'limit_box_rmb10_2121',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1235] = {
		id = 1235,
		drawType = 'limit_box_rmb10_2121',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1236] = {
		id = 1236,
		drawType = 'limit_box_rmb10_2121',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1241] = {
		id = 1241,
		drawType = 'limit_box_rmb1_2231',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1242] = {
		id = 1242,
		drawType = 'limit_box_rmb1_2231',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1251] = {
		id = 1251,
		drawType = 'limit_box_rmb10_2231',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1252] = {
		id = 1252,
		drawType = 'limit_box_rmb10_2231',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1253] = {
		id = 1253,
		drawType = 'limit_box_rmb10_2231',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1254] = {
		id = 1254,
		drawType = 'limit_box_rmb10_2231',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1255] = {
		id = 1255,
		drawType = 'limit_box_rmb10_2231',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1256] = {
		id = 1256,
		drawType = 'limit_box_rmb10_2231',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1261] = {
		id = 1261,
		drawType = 'limit_box_rmb1_2301',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1262] = {
		id = 1262,
		drawType = 'limit_box_rmb1_2301',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1271] = {
		id = 1271,
		drawType = 'limit_box_rmb10_2301',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1272] = {
		id = 1272,
		drawType = 'limit_box_rmb10_2301',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1273] = {
		id = 1273,
		drawType = 'limit_box_rmb10_2301',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1274] = {
		id = 1274,
		drawType = 'limit_box_rmb10_2301',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1275] = {
		id = 1275,
		drawType = 'limit_box_rmb10_2301',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1276] = {
		id = 1276,
		drawType = 'limit_box_rmb10_2301',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1281] = {
		id = 1281,
		drawType = 'limit_box_rmb1_2391',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1282] = {
		id = 1282,
		drawType = 'limit_box_rmb1_2391',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1291] = {
		id = 1291,
		drawType = 'limit_box_rmb10_2391',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1292] = {
		id = 1292,
		drawType = 'limit_box_rmb10_2391',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1293] = {
		id = 1293,
		drawType = 'limit_box_rmb10_2391',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1294] = {
		id = 1294,
		drawType = 'limit_box_rmb10_2391',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1295] = {
		id = 1295,
		drawType = 'limit_box_rmb10_2391',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1296] = {
		id = 1296,
		drawType = 'limit_box_rmb10_2391',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1301] = {
		id = 1301,
		drawType = 'limit_box_rmb1_2491',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1302] = {
		id = 1302,
		drawType = 'limit_box_rmb1_2491',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1311] = {
		id = 1311,
		drawType = 'limit_box_rmb10_2491',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1312] = {
		id = 1312,
		drawType = 'limit_box_rmb10_2491',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1313] = {
		id = 1313,
		drawType = 'limit_box_rmb10_2491',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1314] = {
		id = 1314,
		drawType = 'limit_box_rmb10_2491',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1315] = {
		id = 1315,
		drawType = 'limit_box_rmb10_2491',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1316] = {
		id = 1316,
		drawType = 'limit_box_rmb10_2491',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1321] = {
		id = 1321,
		drawType = 'limit_box_rmb1_2591',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1322] = {
		id = 1322,
		drawType = 'limit_box_rmb1_2591',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1331] = {
		id = 1331,
		drawType = 'limit_box_rmb10_2591',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1332] = {
		id = 1332,
		drawType = 'limit_box_rmb10_2591',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1333] = {
		id = 1333,
		drawType = 'limit_box_rmb10_2591',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1334] = {
		id = 1334,
		drawType = 'limit_box_rmb10_2591',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1335] = {
		id = 1335,
		drawType = 'limit_box_rmb10_2591',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1336] = {
		id = 1336,
		drawType = 'limit_box_rmb10_2591',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1341] = {
		id = 1341,
		drawType = 'limit_box_rmb1_2681',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1342] = {
		id = 1342,
		drawType = 'limit_box_rmb1_2681',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1351] = {
		id = 1351,
		drawType = 'limit_box_rmb10_2681',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1352] = {
		id = 1352,
		drawType = 'limit_box_rmb10_2681',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1353] = {
		id = 1353,
		drawType = 'limit_box_rmb10_2681',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1354] = {
		id = 1354,
		drawType = 'limit_box_rmb10_2681',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1355] = {
		id = 1355,
		drawType = 'limit_box_rmb10_2681',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1356] = {
		id = 1356,
		drawType = 'limit_box_rmb10_2681',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1361] = {
		id = 1361,
		drawType = 'limit_box_rmb1_2741',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1362] = {
		id = 1362,
		drawType = 'limit_box_rmb1_2741',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1371] = {
		id = 1371,
		drawType = 'limit_box_rmb10_2741',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1372] = {
		id = 1372,
		drawType = 'limit_box_rmb10_2741',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1373] = {
		id = 1373,
		drawType = 'limit_box_rmb10_2741',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1374] = {
		id = 1374,
		drawType = 'limit_box_rmb10_2741',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1375] = {
		id = 1375,
		drawType = 'limit_box_rmb10_2741',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1376] = {
		id = 1376,
		drawType = 'limit_box_rmb10_2741',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1381] = {
		id = 1381,
		drawType = 'limit_box_rmb1_2861',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1382] = {
		id = 1382,
		drawType = 'limit_box_rmb1_2861',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1391] = {
		id = 1391,
		drawType = 'limit_box_rmb10_2861',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1392] = {
		id = 1392,
		drawType = 'limit_box_rmb10_2861',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1393] = {
		id = 1393,
		drawType = 'limit_box_rmb10_2861',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1394] = {
		id = 1394,
		drawType = 'limit_box_rmb10_2861',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1395] = {
		id = 1395,
		drawType = 'limit_box_rmb10_2861',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1396] = {
		id = 1396,
		drawType = 'limit_box_rmb10_2861',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1400] = {
		id = 1400,
		drawType = 'limit_box_rmb1_2951',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1401] = {
		id = 1401,
		drawType = 'limit_box_rmb1_2951',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1411] = {
		id = 1411,
		drawType = 'limit_box_rmb10_2951',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1412] = {
		id = 1412,
		drawType = 'limit_box_rmb10_2951',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1413] = {
		id = 1413,
		drawType = 'limit_box_rmb10_2951',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1414] = {
		id = 1414,
		drawType = 'limit_box_rmb10_2951',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1415] = {
		id = 1415,
		drawType = 'limit_box_rmb10_2951',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1416] = {
		id = 1416,
		drawType = 'limit_box_rmb10_2951',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1420] = {
		id = 1420,
		drawType = 'limit_box_rmb1_3051',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1421] = {
		id = 1421,
		drawType = 'limit_box_rmb1_3051',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1431] = {
		id = 1431,
		drawType = 'limit_box_rmb10_3051',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1432] = {
		id = 1432,
		drawType = 'limit_box_rmb10_3051',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1433] = {
		id = 1433,
		drawType = 'limit_box_rmb10_3051',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1434] = {
		id = 1434,
		drawType = 'limit_box_rmb10_3051',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1435] = {
		id = 1435,
		drawType = 'limit_box_rmb10_3051',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1436] = {
		id = 1436,
		drawType = 'limit_box_rmb10_3051',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1440] = {
		id = 1440,
		drawType = 'limit_box_rmb1_3121',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1441] = {
		id = 1441,
		drawType = 'limit_box_rmb1_3121',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1451] = {
		id = 1451,
		drawType = 'limit_box_rmb10_3121',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1452] = {
		id = 1452,
		drawType = 'limit_box_rmb10_3121',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1453] = {
		id = 1453,
		drawType = 'limit_box_rmb10_3121',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1454] = {
		id = 1454,
		drawType = 'limit_box_rmb10_3121',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1455] = {
		id = 1455,
		drawType = 'limit_box_rmb10_3121',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1456] = {
		id = 1456,
		drawType = 'limit_box_rmb10_3121',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1460] = {
		id = 1460,
		drawType = 'limit_box_rmb1_3211',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1461] = {
		id = 1461,
		drawType = 'limit_box_rmb1_3211',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1471] = {
		id = 1471,
		drawType = 'limit_box_rmb10_3211',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1472] = {
		id = 1472,
		drawType = 'limit_box_rmb10_3211',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1473] = {
		id = 1473,
		drawType = 'limit_box_rmb10_3211',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1474] = {
		id = 1474,
		drawType = 'limit_box_rmb10_3211',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1475] = {
		id = 1475,
		drawType = 'limit_box_rmb10_3211',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1476] = {
		id = 1476,
		drawType = 'limit_box_rmb10_3211',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[1480] = {
		id = 1480,
		drawType = 'limit_box_rmb1_3361',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[1481] = {
		id = 1481,
		drawType = 'limit_box_rmb1_3361',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[1491] = {
		id = 1491,
		drawType = 'limit_box_rmb10_3361',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1492] = {
		id = 1492,
		drawType = 'limit_box_rmb10_3361',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1493] = {
		id = 1493,
		drawType = 'limit_box_rmb10_3361',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1494] = {
		id = 1494,
		drawType = 'limit_box_rmb10_3361',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1495] = {
		id = 1495,
		drawType = 'limit_box_rmb10_3361',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[1496] = {
		id = 1496,
		drawType = 'limit_box_rmb10_3361',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[2001] = {
		id = 2001,
		drawType = 'limit_up_rmb1_1071',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[2002] = {
		id = 2002,
		drawType = 'limit_up_rmb1_1071',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[2003] = {
		id = 2003,
		drawType = 'limit_up_rmb1_1071',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1200] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[2004] = {
		id = 2004,
		drawType = 'limit_up_rmb1_1071',
		drawTriggerTimes = 6,
		effectLimit = 10,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1200] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[2005] = {
		id = 2005,
		drawType = 'limit_up_rmb1_1071',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1200] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[2011] = {
		id = 2011,
		drawType = 'limit_up_rmb10_1071',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[1200] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[1200] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100
	},
	[2012] = {
		id = 2012,
		drawType = 'limit_up_rmb10_1071',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		effectLimit = 20,
		lotteryType1 = {[1200] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1200] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1200] = 1, [205] = 5, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1200] = 2, [205] = 3, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1200] = 2, [205] = 4, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1200] = 2, [205] = 5, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1200] = 3, [205] = 2, [206] = 5, __size = 3},
		lotteryType8 = {[1200] = 3, [205] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[1200] = 3, [205] = 4, [206] = 3, __size = 3}
	},
	[2013] = {
		id = 2013,
		drawType = 'limit_up_rmb10_1071',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[205] = 3, [206] = 6, [1201] = 1, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [206] = 5, [1201] = 1, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [206] = 4, [1201] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [206] = 5, [1201] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [206] = 4, [1201] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [206] = 3, [1201] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [206] = 5, [1201] = 3, __size = 3},
		lotteryType8 = {[205] = 3, [206] = 4, [1201] = 3, __size = 3},
		lotteryType9 = {[205] = 4, [206] = 3, [1201] = 3, __size = 3}
	},
	[2021] = {
		id = 2021,
		drawType = 'limit_up_rmb1_1161',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[2022] = {
		id = 2022,
		drawType = 'limit_up_rmb1_1161',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[2023] = {
		id = 2023,
		drawType = 'limit_up_rmb1_1161',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1202] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[2024] = {
		id = 2024,
		drawType = 'limit_up_rmb1_1161',
		drawTriggerTimes = 6,
		effectLimit = 10,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1202] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[2025] = {
		id = 2025,
		drawType = 'limit_up_rmb1_1161',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1202] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[2031] = {
		id = 2031,
		drawType = 'limit_up_rmb10_1161',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[205] = 3, [206] = 6, [1202] = 1, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[205] = 4, [206] = 5, [1202] = 1, __size = 3},
		lotteryWeight2 = 100
	},
	[2032] = {
		id = 2032,
		drawType = 'limit_up_rmb10_1161',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		effectLimit = 20,
		lotteryType1 = {[205] = 3, [206] = 6, [1202] = 1, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [206] = 5, [1202] = 1, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [206] = 4, [1202] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [206] = 5, [1202] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [206] = 4, [1202] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [206] = 3, [1202] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [206] = 5, [1202] = 3, __size = 3},
		lotteryType8 = {[205] = 3, [206] = 4, [1202] = 3, __size = 3},
		lotteryType9 = {[205] = 4, [206] = 3, [1202] = 3, __size = 3}
	},
	[2033] = {
		id = 2033,
		drawType = 'limit_up_rmb10_1161',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[205] = 3, [206] = 6, [1203] = 1, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [206] = 5, [1203] = 1, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [206] = 4, [1203] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [206] = 5, [1203] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [206] = 4, [1203] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [206] = 3, [1203] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [206] = 5, [1203] = 3, __size = 3},
		lotteryType8 = {[205] = 3, [206] = 4, [1203] = 3, __size = 3},
		lotteryType9 = {[205] = 4, [206] = 3, [1203] = 3, __size = 3}
	},
	[2041] = {
		id = 2041,
		drawType = 'limit_up_rmb1_1251',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[2042] = {
		id = 2042,
		drawType = 'limit_up_rmb1_1251',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[2043] = {
		id = 2043,
		drawType = 'limit_up_rmb1_1251',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[2044] = {
		id = 2044,
		drawType = 'limit_up_rmb1_1251',
		drawTriggerTimes = 6,
		effectLimit = 10,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[2045] = {
		id = 2045,
		drawType = 'limit_up_rmb1_1251',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[2051] = {
		id = 2051,
		drawType = 'limit_up_rmb10_1251',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[1204] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[1204] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100
	},
	[2052] = {
		id = 2052,
		drawType = 'limit_up_rmb10_1251',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		effectLimit = 20,
		lotteryType1 = {[1204] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1204] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1204] = 1, [205] = 5, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1204] = 2, [205] = 3, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1204] = 2, [205] = 4, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1204] = 2, [205] = 5, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1204] = 3, [205] = 2, [206] = 5, __size = 3},
		lotteryType8 = {[1204] = 3, [205] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[1204] = 3, [205] = 4, [206] = 3, __size = 3}
	},
	[2053] = {
		id = 2053,
		drawType = 'limit_up_rmb10_1251',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[1205] = 1, [206] = 6, [205] = 3, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1205] = 1, [206] = 5, [205] = 4, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1205] = 1, [206] = 4, [205] = 5, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1205] = 2, [206] = 5, [205] = 3, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1205] = 2, [206] = 4, [205] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1205] = 2, [206] = 3, [205] = 5, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1205] = 3, [206] = 5, [205] = 2, __size = 3},
		lotteryType8 = {[1205] = 3, [206] = 4, [205] = 3, __size = 3},
		lotteryType9 = {[1205] = 3, [206] = 3, [205] = 4, __size = 3}
	},
	[2061] = {
		id = 2061,
		drawType = 'limit_up_rmb1_1330',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[2062] = {
		id = 2062,
		drawType = 'limit_up_rmb1_1330',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[2063] = {
		id = 2063,
		drawType = 'limit_up_rmb1_1330',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1200] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[2064] = {
		id = 2064,
		drawType = 'limit_up_rmb1_1330',
		drawTriggerTimes = 6,
		effectLimit = 10,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1200] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[2065] = {
		id = 2065,
		drawType = 'limit_up_rmb1_1330',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1200] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[2071] = {
		id = 2071,
		drawType = 'limit_up_rmb10_1330',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[1200] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[1200] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100
	},
	[2072] = {
		id = 2072,
		drawType = 'limit_up_rmb10_1330',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		effectLimit = 20,
		lotteryType1 = {[1200] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1200] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1200] = 1, [205] = 5, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1200] = 2, [205] = 3, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1200] = 2, [205] = 4, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1200] = 2, [205] = 5, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1200] = 3, [205] = 2, [206] = 5, __size = 3},
		lotteryType8 = {[1200] = 3, [205] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[1200] = 3, [205] = 4, [206] = 3, __size = 3}
	},
	[2073] = {
		id = 2073,
		drawType = 'limit_up_rmb10_1330',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[205] = 3, [206] = 6, [1201] = 1, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [206] = 5, [1201] = 1, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [206] = 4, [1201] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [206] = 5, [1201] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [206] = 4, [1201] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [206] = 3, [1201] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [206] = 5, [1201] = 3, __size = 3},
		lotteryType8 = {[205] = 3, [206] = 4, [1201] = 3, __size = 3},
		lotteryType9 = {[205] = 4, [206] = 3, [1201] = 3, __size = 3}
	},
	[5001] = {
		id = 5001,
		drawType = 'limit_rmb1_1256',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1111] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5002] = {
		id = 5002,
		drawType = 'limit_rmb1_1256',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5003] = {
		id = 5003,
		drawType = 'limit_rmb1_1256',
		drawTriggerTimes = 70,
		effectLimit = 1,
		startCount = 63,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1113] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5004] = {
		id = 5004,
		drawType = 'limit_rmb1_1256',
		drawTriggerTimes = 135,
		effectLimit = 999,
		startCount = 123,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 70,
		lotteryType1 = {[1113] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5011] = {
		id = 5011,
		drawType = 'limit_rmb1_1335',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryWeight4 = 150
	},
	[5012] = {
		id = 5012,
		drawType = 'limit_rmb1_1335',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5013] = {
		id = 5013,
		drawType = 'limit_rmb1_1335',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1114] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5014] = {
		id = 5014,
		drawType = 'limit_rmb1_1335',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1114] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5021] = {
		id = 5021,
		drawType = 'limit_rmb1_1385',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1117] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5022] = {
		id = 5022,
		drawType = 'limit_rmb1_1385',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5023] = {
		id = 5023,
		drawType = 'limit_rmb1_1385',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1116] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5024] = {
		id = 5024,
		drawType = 'limit_rmb1_1385',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 141,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 68,
		lotteryType1 = {[1116] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5031] = {
		id = 5031,
		drawType = 'limit_rmb1_1431',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1111] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5032] = {
		id = 5032,
		drawType = 'limit_rmb1_1431',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5033] = {
		id = 5033,
		drawType = 'limit_rmb1_1431',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1113] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5034] = {
		id = 5034,
		drawType = 'limit_rmb1_1431',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1113] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5041] = {
		id = 5041,
		drawType = 'limit_rmb1_1516',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryWeight4 = 150
	},
	[5042] = {
		id = 5042,
		drawType = 'limit_rmb1_1516',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5043] = {
		id = 5043,
		drawType = 'limit_rmb1_1516',
		drawTriggerTimes = 70,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1114] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5044] = {
		id = 5044,
		drawType = 'limit_rmb1_1516',
		drawTriggerTimes = 140,
		effectLimit = 999,
		startCount = 146,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 73,
		lotteryType1 = {[1114] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5051] = {
		id = 5051,
		drawType = 'limit_rmb1_1571',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1117] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5052] = {
		id = 5052,
		drawType = 'limit_rmb1_1571',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5053] = {
		id = 5053,
		drawType = 'limit_rmb1_1571',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1116] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5054] = {
		id = 5054,
		drawType = 'limit_rmb1_1571',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1116] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5061] = {
		id = 5061,
		drawType = 'limit_rmb1_1581',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1119] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5062] = {
		id = 5062,
		drawType = 'limit_rmb1_1581',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5063] = {
		id = 5063,
		drawType = 'limit_rmb1_1581',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1118] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5064] = {
		id = 5064,
		drawType = 'limit_rmb1_1581',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1118] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5071] = {
		id = 5071,
		drawType = 'limit_rmb1_1701',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryWeight4 = 150
	},
	[5072] = {
		id = 5072,
		drawType = 'limit_rmb1_1701',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5073] = {
		id = 5073,
		drawType = 'limit_rmb1_1701',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1114] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5074] = {
		id = 5074,
		drawType = 'limit_rmb1_1701',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1114] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5081] = {
		id = 5081,
		drawType = 'limit_rmb1_1810',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1119] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5082] = {
		id = 5082,
		drawType = 'limit_rmb1_1810',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5083] = {
		id = 5083,
		drawType = 'limit_rmb1_1810',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1118] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5084] = {
		id = 5084,
		drawType = 'limit_rmb1_1810',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1118] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5091] = {
		id = 5091,
		drawType = 'limit_rmb1_1890',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1151] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5092] = {
		id = 5092,
		drawType = 'limit_rmb1_1890',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5093] = {
		id = 5093,
		drawType = 'limit_rmb1_1890',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1150] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5094] = {
		id = 5094,
		drawType = 'limit_rmb1_1890',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1150] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5101] = {
		id = 5101,
		drawType = 'limit_rmb1_1980',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1153] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5102] = {
		id = 5102,
		drawType = 'limit_rmb1_1980',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5103] = {
		id = 5103,
		drawType = 'limit_rmb1_1980',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1152] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5104] = {
		id = 5104,
		drawType = 'limit_rmb1_1980',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1152] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5111] = {
		id = 5111,
		drawType = 'limit_rmb1_2080',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1155] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5112] = {
		id = 5112,
		drawType = 'limit_rmb1_2080',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5113] = {
		id = 5113,
		drawType = 'limit_rmb1_2080',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1154] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5114] = {
		id = 5114,
		drawType = 'limit_rmb1_2080',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1154] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5121] = {
		id = 5121,
		drawType = 'limit_rmb1_2140',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1157] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5122] = {
		id = 5122,
		drawType = 'limit_rmb1_2140',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5123] = {
		id = 5123,
		drawType = 'limit_rmb1_2140',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1156] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5124] = {
		id = 5124,
		drawType = 'limit_rmb1_2140',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1156] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5131] = {
		id = 5131,
		drawType = 'limit_rmb1_2260',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1159] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5132] = {
		id = 5132,
		drawType = 'limit_rmb1_2260',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5133] = {
		id = 5133,
		drawType = 'limit_rmb1_2260',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1158] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5134] = {
		id = 5134,
		drawType = 'limit_rmb1_2260',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1158] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5141] = {
		id = 5141,
		drawType = 'limit_rmb1_2340',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1161] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5142] = {
		id = 5142,
		drawType = 'limit_rmb1_2340',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5143] = {
		id = 5143,
		drawType = 'limit_rmb1_2340',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1160] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5144] = {
		id = 5144,
		drawType = 'limit_rmb1_2340',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1160] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5151] = {
		id = 5151,
		drawType = 'limit_rmb1_2350',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1163] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5152] = {
		id = 5152,
		drawType = 'limit_rmb1_2350',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5153] = {
		id = 5153,
		drawType = 'limit_rmb1_2350',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1162] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5154] = {
		id = 5154,
		drawType = 'limit_rmb1_2350',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1162] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5161] = {
		id = 5161,
		drawType = 'limit_rmb1_2430',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1165] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5162] = {
		id = 5162,
		drawType = 'limit_rmb1_2430',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5163] = {
		id = 5163,
		drawType = 'limit_rmb1_2430',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1164] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5164] = {
		id = 5164,
		drawType = 'limit_rmb1_2430',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1164] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5171] = {
		id = 5171,
		drawType = 'limit_rmb1_2510',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1167] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5172] = {
		id = 5172,
		drawType = 'limit_rmb1_2510',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5173] = {
		id = 5173,
		drawType = 'limit_rmb1_2510',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1166] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5174] = {
		id = 5174,
		drawType = 'limit_rmb1_2510',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1166] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5181] = {
		id = 5181,
		drawType = 'limit_rmb1_2540',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1169] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5182] = {
		id = 5182,
		drawType = 'limit_rmb1_2540',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5183] = {
		id = 5183,
		drawType = 'limit_rmb1_2540',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1168] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5184] = {
		id = 5184,
		drawType = 'limit_rmb1_2540',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1168] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5191] = {
		id = 5191,
		drawType = 'limit_rmb1_2640',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1171] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5192] = {
		id = 5192,
		drawType = 'limit_rmb1_2640',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5193] = {
		id = 5193,
		drawType = 'limit_rmb1_2640',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1170] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5194] = {
		id = 5194,
		drawType = 'limit_rmb1_2640',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1170] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5201] = {
		id = 5201,
		drawType = 'limit_rmb1_2650',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1173] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5202] = {
		id = 5202,
		drawType = 'limit_rmb1_2650',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5203] = {
		id = 5203,
		drawType = 'limit_rmb1_2650',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1172] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5204] = {
		id = 5204,
		drawType = 'limit_rmb1_2650',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1172] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5211] = {
		id = 5211,
		drawType = 'limit_rmb1_2710',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1177] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5212] = {
		id = 5212,
		drawType = 'limit_rmb1_2710',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5213] = {
		id = 5213,
		drawType = 'limit_rmb1_2710',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1176] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5214] = {
		id = 5214,
		drawType = 'limit_rmb1_2710',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1176] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5221] = {
		id = 5221,
		drawType = 'limit_rmb1_2720',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1175] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5222] = {
		id = 5222,
		drawType = 'limit_rmb1_2720',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5223] = {
		id = 5223,
		drawType = 'limit_rmb1_2720',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1174] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5224] = {
		id = 5224,
		drawType = 'limit_rmb1_2720',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1174] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5231] = {
		id = 5231,
		drawType = 'limit_rmb1_2780',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1179] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5232] = {
		id = 5232,
		drawType = 'limit_rmb1_2780',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5233] = {
		id = 5233,
		drawType = 'limit_rmb1_2780',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1178] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5234] = {
		id = 5234,
		drawType = 'limit_rmb1_2780',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1178] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5241] = {
		id = 5241,
		drawType = 'limit_rmb1_2810',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1181] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5242] = {
		id = 5242,
		drawType = 'limit_rmb1_2810',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5243] = {
		id = 5243,
		drawType = 'limit_rmb1_2810',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1180] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5244] = {
		id = 5244,
		drawType = 'limit_rmb1_2810',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1180] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5251] = {
		id = 5251,
		drawType = 'limit_rmb1_2820',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1183] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5252] = {
		id = 5252,
		drawType = 'limit_rmb1_2820',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5253] = {
		id = 5253,
		drawType = 'limit_rmb1_2820',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1182] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5254] = {
		id = 5254,
		drawType = 'limit_rmb1_2820',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1182] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5260] = {
		id = 5260,
		drawType = 'limit_rmb1_2890',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1157] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5261] = {
		id = 5261,
		drawType = 'limit_rmb1_2890',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5262] = {
		id = 5262,
		drawType = 'limit_rmb1_2890',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1156] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5263] = {
		id = 5263,
		drawType = 'limit_rmb1_2890',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1156] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5270] = {
		id = 5270,
		drawType = 'limit_rmb1_2910',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1185] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5271] = {
		id = 5271,
		drawType = 'limit_rmb1_2910',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5272] = {
		id = 5272,
		drawType = 'limit_rmb1_2910',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5273] = {
		id = 5273,
		drawType = 'limit_rmb1_2910',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5280] = {
		id = 5280,
		drawType = 'limit_rmb1_2970',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[22512] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1185] = 1, __size = 1},
		lotteryWeight4 = 150,
		lotteryType5 = {[1184] = 1, __size = 1},
		lotteryWeight5 = 1
	},
	[5281] = {
		id = 5281,
		drawType = 'limit_rmb1_2970',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		probEffectInterval = 10,
		lotteryType1 = {[22512] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5282] = {
		id = 5282,
		drawType = 'limit_rmb1_2970',
		drawTriggerTimes = 250,
		effectLimit = 1,
		startCount = 150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5283] = {
		id = 5283,
		drawType = 'limit_rmb1_2970',
		drawTriggerTimes = 400,
		effectLimit = 1,
		startCount = 300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5284] = {
		id = 5284,
		drawType = 'limit_rmb1_2970',
		drawTriggerTimes = 450,
		effectLimit = 1,
		startCount = 450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5285] = {
		id = 5285,
		drawType = 'limit_rmb1_2970',
		drawTriggerTimes = 500,
		effectLimit = 1,
		startCount = 600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5290] = {
		id = 5290,
		drawType = 'limit_rmb1_3000',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1189] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5291] = {
		id = 5291,
		drawType = 'limit_rmb1_3000',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5292] = {
		id = 5292,
		drawType = 'limit_rmb1_3000',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5293] = {
		id = 5293,
		drawType = 'limit_rmb1_3000',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5300] = {
		id = 5300,
		drawType = 'limit_rmb1_3070',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1704] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5301] = {
		id = 5301,
		drawType = 'limit_rmb1_3070',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5302] = {
		id = 5302,
		drawType = 'limit_rmb1_3070',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1703] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5303] = {
		id = 5303,
		drawType = 'limit_rmb1_3070',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1703] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5310] = {
		id = 5310,
		drawType = 'limit_rmb1_3080',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1706] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5311] = {
		id = 5311,
		drawType = 'limit_rmb1_3080',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5312] = {
		id = 5312,
		drawType = 'limit_rmb1_3080',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1705] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5313] = {
		id = 5313,
		drawType = 'limit_rmb1_3080',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1705] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5320] = {
		id = 5320,
		drawType = 'limit_rmb1_3130',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1708] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5321] = {
		id = 5321,
		drawType = 'limit_rmb1_3130',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5322] = {
		id = 5322,
		drawType = 'limit_rmb1_3130',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1707] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5323] = {
		id = 5323,
		drawType = 'limit_rmb1_3130',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1707] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5330] = {
		id = 5330,
		drawType = 'limit_rmb1_3150',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1710] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5331] = {
		id = 5331,
		drawType = 'limit_rmb1_3150',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5332] = {
		id = 5332,
		drawType = 'limit_rmb1_3150',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1709] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5333] = {
		id = 5333,
		drawType = 'limit_rmb1_3150',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1709] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5340] = {
		id = 5340,
		drawType = 'limit_rmb1_3160',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1712] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5341] = {
		id = 5341,
		drawType = 'limit_rmb1_3160',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5342] = {
		id = 5342,
		drawType = 'limit_rmb1_3160',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1711] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5343] = {
		id = 5343,
		drawType = 'limit_rmb1_3160',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1711] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5350] = {
		id = 5350,
		drawType = 'limit_rmb1_3220',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1714] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5351] = {
		id = 5351,
		drawType = 'limit_rmb1_3220',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5352] = {
		id = 5352,
		drawType = 'limit_rmb1_3220',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1713] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5353] = {
		id = 5353,
		drawType = 'limit_rmb1_3220',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1713] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5360] = {
		id = 5360,
		drawType = 'limit_rmb1_3250',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1716] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5361] = {
		id = 5361,
		drawType = 'limit_rmb1_3250',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5362] = {
		id = 5362,
		drawType = 'limit_rmb1_3250',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1715] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5363] = {
		id = 5363,
		drawType = 'limit_rmb1_3250',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1715] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5370] = {
		id = 5370,
		drawType = 'limit_rmb1_3290',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1716] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5371] = {
		id = 5371,
		drawType = 'limit_rmb1_3290',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5372] = {
		id = 5372,
		drawType = 'limit_rmb1_3290',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1715] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5373] = {
		id = 5373,
		drawType = 'limit_rmb1_3290',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1715] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5380] = {
		id = 5380,
		drawType = 'limit_rmb1_3300',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1718] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5381] = {
		id = 5381,
		drawType = 'limit_rmb1_3300',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5382] = {
		id = 5382,
		drawType = 'limit_rmb1_3300',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1717] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5383] = {
		id = 5383,
		drawType = 'limit_rmb1_3300',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1717] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5390] = {
		id = 5390,
		drawType = 'limit_rmb1_3340',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1163] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5391] = {
		id = 5391,
		drawType = 'limit_rmb1_3340',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5392] = {
		id = 5392,
		drawType = 'limit_rmb1_3340',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1162] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5393] = {
		id = 5393,
		drawType = 'limit_rmb1_3340',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1162] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5400] = {
		id = 5400,
		drawType = 'limit_rmb1_3356',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1121] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5401] = {
		id = 5401,
		drawType = 'limit_rmb1_3356',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5402] = {
		id = 5402,
		drawType = 'limit_rmb1_3356',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1120] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5403] = {
		id = 5403,
		drawType = 'limit_rmb1_3356',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1120] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5404] = {
		id = 5404,
		drawType = 'limit_rmb1_10001',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1122] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[5405] = {
		id = 5405,
		drawType = 'limit_rmb1_10001',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5406] = {
		id = 5406,
		drawType = 'limit_rmb1_10001',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1149] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[5407] = {
		id = 5407,
		drawType = 'limit_rmb1_10001',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1149] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[7000] = {
		id = 7000,
		drawType = 'limit_rmb1_2900',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1702] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[7001] = {
		id = 7001,
		drawType = 'limit_rmb1_2900',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[7002] = {
		id = 7002,
		drawType = 'limit_rmb1_2900',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1701] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[7003] = {
		id = 7003,
		drawType = 'limit_rmb1_2900',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1701] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[11880] = {
		id = 11880,
		drawType = 'limit_up_rmb1_15641',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[11881] = {
		id = 11881,
		drawType = 'limit_up_rmb1_15641',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11882] = {
		id = 11882,
		drawType = 'limit_up_rmb1_15641',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1218] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11883] = {
		id = 11883,
		drawType = 'limit_up_rmb1_15641',
		drawTriggerTimes = 6,
		effectLimit = 10,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1218] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11884] = {
		id = 11884,
		drawType = 'limit_up_rmb1_15641',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1218] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11890] = {
		id = 11890,
		drawType = 'limit_up_rmb10_15641',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[205] = 3, [1218] = 1, [206] = 6, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[205] = 4, [1218] = 1, [206] = 5, __size = 3},
		lotteryWeight2 = 100
	},
	[11891] = {
		id = 11891,
		drawType = 'limit_up_rmb10_15641',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		effectLimit = 20,
		lotteryType1 = {[205] = 3, [1218] = 1, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [1218] = 1, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [1218] = 1, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [1218] = 2, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [1218] = 2, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [1218] = 2, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [1218] = 3, [206] = 5, __size = 3},
		lotteryType8 = {[205] = 3, [1218] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[205] = 4, [1218] = 3, [206] = 3, __size = 3}
	},
	[11892] = {
		id = 11892,
		drawType = 'limit_up_rmb10_15641',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[205] = 3, [206] = 6, [1219] = 1, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [206] = 5, [1219] = 1, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [206] = 4, [1219] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [206] = 5, [1219] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [206] = 4, [1219] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [206] = 3, [1219] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [206] = 5, [1219] = 3, __size = 3},
		lotteryType8 = {[205] = 3, [206] = 4, [1219] = 3, __size = 3},
		lotteryType9 = {[205] = 4, [206] = 3, [1219] = 3, __size = 3}
	},
	[11900] = {
		id = 11900,
		drawType = 'limit_up_rmb1_15651',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[11901] = {
		id = 11901,
		drawType = 'limit_up_rmb1_15651',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11902] = {
		id = 11902,
		drawType = 'limit_up_rmb1_15651',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11903] = {
		id = 11903,
		drawType = 'limit_up_rmb1_15651',
		drawTriggerTimes = 6,
		effectLimit = 10,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11904] = {
		id = 11904,
		drawType = 'limit_up_rmb1_15651',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11910] = {
		id = 11910,
		drawType = 'limit_up_rmb10_15651',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[1204] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[1204] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100
	},
	[11911] = {
		id = 11911,
		drawType = 'limit_up_rmb10_15651',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		effectLimit = 20,
		lotteryType1 = {[1204] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1204] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1204] = 1, [205] = 5, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1204] = 2, [205] = 3, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1204] = 2, [205] = 4, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1204] = 2, [205] = 5, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1204] = 3, [205] = 2, [206] = 5, __size = 3},
		lotteryType8 = {[1204] = 3, [205] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[1204] = 3, [205] = 4, [206] = 3, __size = 3}
	},
	[11912] = {
		id = 11912,
		drawType = 'limit_up_rmb10_15651',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[1205] = 1, [206] = 6, [205] = 3, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1205] = 1, [206] = 5, [205] = 4, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1205] = 1, [206] = 4, [205] = 5, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1205] = 2, [206] = 5, [205] = 3, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1205] = 2, [206] = 4, [205] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1205] = 2, [206] = 3, [205] = 5, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1205] = 3, [206] = 5, [205] = 2, __size = 3},
		lotteryType8 = {[1205] = 3, [206] = 4, [205] = 3, __size = 3},
		lotteryType9 = {[1205] = 3, [206] = 3, [205] = 4, __size = 3}
	},
	[11920] = {
		id = 11920,
		drawType = 'limit_up_rmb1_15661',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[11921] = {
		id = 11921,
		drawType = 'limit_up_rmb1_15661',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11922] = {
		id = 11922,
		drawType = 'limit_up_rmb1_15661',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1206] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11923] = {
		id = 11923,
		drawType = 'limit_up_rmb1_15661',
		drawTriggerTimes = 6,
		effectLimit = 10,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1206] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11924] = {
		id = 11924,
		drawType = 'limit_up_rmb1_15661',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1206] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11930] = {
		id = 11930,
		drawType = 'limit_up_rmb10_15661',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[205] = 3, [1206] = 1, [206] = 6, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[205] = 4, [1206] = 1, [206] = 5, __size = 3},
		lotteryWeight2 = 100
	},
	[11931] = {
		id = 11931,
		drawType = 'limit_up_rmb10_15661',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		effectLimit = 20,
		lotteryType1 = {[205] = 3, [1206] = 1, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [1206] = 1, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [1206] = 1, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [1206] = 2, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [1206] = 2, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [1206] = 2, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [1206] = 3, [206] = 5, __size = 3},
		lotteryType8 = {[205] = 3, [1206] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[205] = 4, [1206] = 3, [206] = 3, __size = 3}
	},
	[11932] = {
		id = 11932,
		drawType = 'limit_up_rmb10_15661',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[205] = 3, [206] = 6, [1207] = 1, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [206] = 5, [1207] = 1, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [206] = 4, [1207] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [206] = 5, [1207] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [206] = 4, [1207] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [206] = 3, [1207] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [206] = 5, [1207] = 3, __size = 3},
		lotteryType8 = {[205] = 3, [206] = 4, [1207] = 3, __size = 3},
		lotteryType9 = {[205] = 4, [206] = 3, [1207] = 3, __size = 3}
	},
	[11940] = {
		id = 11940,
		drawType = 'limit_up_rmb1_15671',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[11941] = {
		id = 11941,
		drawType = 'limit_up_rmb1_15671',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11942] = {
		id = 11942,
		drawType = 'limit_up_rmb1_15671',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1208] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11943] = {
		id = 11943,
		drawType = 'limit_up_rmb1_15671',
		drawTriggerTimes = 6,
		effectLimit = 10,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1208] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11944] = {
		id = 11944,
		drawType = 'limit_up_rmb1_15671',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1208] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11950] = {
		id = 11950,
		drawType = 'limit_up_rmb10_15671',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[1208] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[1208] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100
	},
	[11951] = {
		id = 11951,
		drawType = 'limit_up_rmb10_15671',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		effectLimit = 20,
		lotteryType1 = {[1208] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1208] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1208] = 1, [205] = 5, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1208] = 2, [205] = 3, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1208] = 2, [205] = 4, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1208] = 2, [205] = 5, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1208] = 3, [205] = 2, [206] = 5, __size = 3},
		lotteryType8 = {[1208] = 3, [205] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[1208] = 3, [205] = 4, [206] = 3, __size = 3}
	},
	[11952] = {
		id = 11952,
		drawType = 'limit_up_rmb10_15671',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[205] = 3, [206] = 6, [1209] = 1, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [206] = 5, [1209] = 1, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [206] = 4, [1209] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [206] = 5, [1209] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [206] = 4, [1209] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [206] = 3, [1209] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [206] = 5, [1209] = 3, __size = 3},
		lotteryType8 = {[205] = 3, [206] = 4, [1209] = 3, __size = 3},
		lotteryType9 = {[205] = 4, [206] = 3, [1209] = 3, __size = 3}
	},
	[11960] = {
		id = 11960,
		drawType = 'limit_up_rmb1_15681',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[11961] = {
		id = 11961,
		drawType = 'limit_up_rmb1_15681',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11962] = {
		id = 11962,
		drawType = 'limit_up_rmb1_15681',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1210] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11963] = {
		id = 11963,
		drawType = 'limit_up_rmb1_15681',
		drawTriggerTimes = 6,
		effectLimit = 10,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1210] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11964] = {
		id = 11964,
		drawType = 'limit_up_rmb1_15681',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1210] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11970] = {
		id = 11970,
		drawType = 'limit_up_rmb10_15681',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[205] = 3, [1210] = 1, [206] = 6, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[205] = 4, [1210] = 1, [206] = 5, __size = 3},
		lotteryWeight2 = 100
	},
	[11971] = {
		id = 11971,
		drawType = 'limit_up_rmb10_15681',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		effectLimit = 20,
		lotteryType1 = {[205] = 3, [1210] = 1, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [1210] = 1, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [1210] = 1, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [1210] = 2, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [1210] = 2, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [1210] = 2, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [1210] = 3, [206] = 5, __size = 3},
		lotteryType8 = {[205] = 3, [1210] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[205] = 4, [1210] = 3, [206] = 3, __size = 3}
	},
	[11972] = {
		id = 11972,
		drawType = 'limit_up_rmb10_15681',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[205] = 3, [206] = 6, [1211] = 1, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [206] = 5, [1211] = 1, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [206] = 4, [1211] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [206] = 5, [1211] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [206] = 4, [1211] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [206] = 3, [1211] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [206] = 5, [1211] = 3, __size = 3},
		lotteryType8 = {[205] = 3, [206] = 4, [1211] = 3, __size = 3},
		lotteryType9 = {[205] = 4, [206] = 3, [1211] = 3, __size = 3}
	},
	[11980] = {
		id = 11980,
		drawType = 'limit_up_rmb1_15691',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[11981] = {
		id = 11981,
		drawType = 'limit_up_rmb1_15691',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11982] = {
		id = 11982,
		drawType = 'limit_up_rmb1_15691',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1212] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11983] = {
		id = 11983,
		drawType = 'limit_up_rmb1_15691',
		drawTriggerTimes = 6,
		effectLimit = 10,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1212] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11984] = {
		id = 11984,
		drawType = 'limit_up_rmb1_15691',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1212] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[11990] = {
		id = 11990,
		drawType = 'limit_up_rmb10_15691',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[1212] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[1212] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100
	},
	[11991] = {
		id = 11991,
		drawType = 'limit_up_rmb10_15691',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[1212] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1212] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1212] = 1, [205] = 5, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1212] = 2, [205] = 3, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1212] = 2, [205] = 4, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1212] = 2, [205] = 5, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1212] = 3, [205] = 2, [206] = 5, __size = 3},
		lotteryType8 = {[1212] = 3, [205] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[1212] = 3, [205] = 4, [206] = 3, __size = 3}
	},
	[11992] = {
		id = 11992,
		drawType = 'limit_up_rmb10_15691',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[1213] = 1, [206] = 6, [205] = 3, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1213] = 1, [206] = 5, [205] = 4, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1213] = 1, [206] = 4, [205] = 5, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1213] = 2, [206] = 5, [205] = 3, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1213] = 2, [206] = 4, [205] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1213] = 2, [206] = 3, [205] = 5, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1213] = 3, [206] = 5, [205] = 2, __size = 3},
		lotteryType8 = {[1213] = 3, [206] = 4, [205] = 3, __size = 3},
		lotteryType9 = {[1213] = 3, [206] = 3, [205] = 4, __size = 3}
	},
	[12000] = {
		id = 12000,
		drawType = 'limit_up_rmb1_15701',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[12001] = {
		id = 12001,
		drawType = 'limit_up_rmb1_15701',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[12002] = {
		id = 12002,
		drawType = 'limit_up_rmb1_15701',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1214] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[12003] = {
		id = 12003,
		drawType = 'limit_up_rmb1_15701',
		drawTriggerTimes = 6,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1214] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[12004] = {
		id = 12004,
		drawType = 'limit_up_rmb1_15701',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1214] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[12010] = {
		id = 12010,
		drawType = 'limit_up_rmb10_15701',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[205] = 3, [1214] = 1, [206] = 6, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[205] = 4, [1214] = 1, [206] = 5, __size = 3},
		lotteryWeight2 = 100
	},
	[12011] = {
		id = 12011,
		drawType = 'limit_up_rmb10_15701',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 3, [1214] = 1, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [1214] = 1, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [1214] = 1, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [1214] = 2, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [1214] = 2, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [1214] = 2, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [1214] = 3, [206] = 5, __size = 3},
		lotteryType8 = {[205] = 3, [1214] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[205] = 4, [1214] = 3, [206] = 3, __size = 3}
	},
	[12012] = {
		id = 12012,
		drawType = 'limit_up_rmb10_15701',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[205] = 3, [206] = 6, [1215] = 1, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [206] = 5, [1215] = 1, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [206] = 4, [1215] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [206] = 5, [1215] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [206] = 4, [1215] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [206] = 3, [1215] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [206] = 5, [1215] = 3, __size = 3},
		lotteryType8 = {[205] = 3, [206] = 4, [1215] = 3, __size = 3},
		lotteryType9 = {[205] = 4, [206] = 3, [1215] = 3, __size = 3}
	},
	[12020] = {
		id = 12020,
		drawType = 'limit_up_rmb1_15711',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[12021] = {
		id = 12021,
		drawType = 'limit_up_rmb1_15711',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[12022] = {
		id = 12022,
		drawType = 'limit_up_rmb1_15711',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1216] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[12023] = {
		id = 12023,
		drawType = 'limit_up_rmb1_15711',
		drawTriggerTimes = 6,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1216] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[12024] = {
		id = 12024,
		drawType = 'limit_up_rmb1_15711',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1216] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[12030] = {
		id = 12030,
		drawType = 'limit_up_rmb10_15711',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[1216] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[1216] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100
	},
	[12031] = {
		id = 12031,
		drawType = 'limit_up_rmb10_15711',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[1216] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1216] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1216] = 1, [205] = 5, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1216] = 2, [205] = 3, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1216] = 2, [205] = 4, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1216] = 2, [205] = 5, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1216] = 3, [205] = 2, [206] = 5, __size = 3},
		lotteryType8 = {[1216] = 3, [205] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[1216] = 3, [205] = 4, [206] = 3, __size = 3}
	},
	[12032] = {
		id = 12032,
		drawType = 'limit_up_rmb10_15711',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[205] = 3, [206] = 6, [1217] = 1, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [206] = 5, [1217] = 1, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [206] = 4, [1217] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [206] = 5, [1217] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [206] = 4, [1217] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [206] = 3, [1217] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [206] = 5, [1217] = 3, __size = 3},
		lotteryType8 = {[205] = 3, [206] = 4, [1217] = 3, __size = 3},
		lotteryType9 = {[205] = 4, [206] = 3, [1217] = 3, __size = 3}
	},
	[15000] = {
		id = 15000,
		drawType = 'group_up_rmb1_1',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[194] = 1, __size = 1},
		lotteryWeight1 = 100,
		lotteryType2 = {[195] = 1, __size = 1},
		lotteryWeight2 = 100
	},
	[15001] = {
		id = 15001,
		drawType = 'group_up_rmb1_1',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[196] = 1, __size = 1},
		lotteryWeight1 = 80
	},
	[15002] = {
		id = 15002,
		drawType = 'group_up_rmb1_1',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1251] = 1, __size = 1},
		lotteryWeight1 = 50
	},
	[15003] = {
		id = 15003,
		drawType = 'group_up_rmb1_1',
		drawTriggerTimes = 6,
		effectLimit = 10,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1251] = 1, __size = 1},
		lotteryWeight1 = 50
	},
	[15004] = {
		id = 15004,
		drawType = 'group_up_rmb1_1',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1251] = 1, __size = 1},
		lotteryWeight1 = 50
	},
	[15011] = {
		id = 15011,
		drawType = 'group_up_rmb10_1',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[194] = 3, [195] = 6, [1251] = 1, __size = 3},
		lotteryWeight1 = 60,
		lotteryType2 = {[194] = 4, [195] = 5, [1251] = 1, __size = 3},
		lotteryWeight2 = 60
	},
	[15012] = {
		id = 15012,
		drawType = 'group_up_rmb10_1',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[194] = 3, [195] = 6, [1251] = 1, __size = 3},
		lotteryWeight1 = 80,
		lotteryType2 = {[194] = 4, [195] = 5, [1251] = 1, __size = 3},
		lotteryWeight2 = 60,
		lotteryType3 = {[194] = 5, [195] = 4, [1251] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[194] = 3, [195] = 5, [1251] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[194] = 4, [195] = 4, [1251] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[194] = 5, [195] = 3, [1251] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[194] = 2, [195] = 5, [1251] = 3, __size = 3},
		lotteryType8 = {[194] = 3, [195] = 4, [1251] = 3, __size = 3},
		lotteryType9 = {[194] = 4, [195] = 3, [1251] = 3, __size = 3}
	},
	[15013] = {
		id = 15013,
		drawType = 'group_up_rmb10_1',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[1252] = 1, [194] = 3, [195] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1252] = 1, [194] = 4, [195] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1252] = 1, [194] = 5, [195] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1252] = 2, [194] = 3, [195] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1252] = 2, [194] = 4, [195] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1252] = 2, [194] = 5, [195] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1252] = 3, [194] = 2, [195] = 5, __size = 3},
		lotteryType8 = {[1252] = 3, [194] = 3, [195] = 4, __size = 3},
		lotteryType9 = {[1252] = 3, [194] = 4, [195] = 3, __size = 3}
	},
	[15100] = {
		id = 15100,
		drawType = 'group_up_rmb1_2',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[197] = 1, __size = 1},
		lotteryWeight1 = 100,
		lotteryType2 = {[198] = 1, __size = 1},
		lotteryWeight2 = 100
	},
	[15101] = {
		id = 15101,
		drawType = 'group_up_rmb1_2',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[199] = 1, __size = 1},
		lotteryWeight1 = 80
	},
	[15102] = {
		id = 15102,
		drawType = 'group_up_rmb1_2',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1253] = 1, __size = 1},
		lotteryWeight1 = 50
	},
	[15103] = {
		id = 15103,
		drawType = 'group_up_rmb1_2',
		drawTriggerTimes = 6,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1253] = 1, __size = 1},
		lotteryWeight1 = 50
	},
	[15104] = {
		id = 15104,
		drawType = 'group_up_rmb1_2',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1253] = 1, __size = 1},
		lotteryWeight1 = 50
	},
	[15111] = {
		id = 15111,
		drawType = 'group_up_rmb10_2',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[197] = 3, [198] = 6, [1253] = 1, __size = 3},
		lotteryWeight1 = 60,
		lotteryType2 = {[197] = 4, [198] = 5, [1253] = 1, __size = 3},
		lotteryWeight2 = 60
	},
	[15112] = {
		id = 15112,
		drawType = 'group_up_rmb10_2',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[197] = 3, [198] = 6, [1253] = 1, __size = 3},
		lotteryWeight1 = 80,
		lotteryType2 = {[197] = 4, [198] = 5, [1253] = 1, __size = 3},
		lotteryWeight2 = 60,
		lotteryType3 = {[197] = 5, [198] = 4, [1253] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[197] = 3, [198] = 5, [1253] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[197] = 4, [198] = 4, [1253] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[197] = 5, [198] = 3, [1253] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[197] = 2, [198] = 5, [1253] = 3, __size = 3},
		lotteryType8 = {[197] = 3, [198] = 4, [1253] = 3, __size = 3},
		lotteryType9 = {[197] = 4, [198] = 3, [1253] = 3, __size = 3}
	},
	[15113] = {
		id = 15113,
		drawType = 'group_up_rmb10_2',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[197] = 3, [1254] = 1, [198] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[197] = 4, [1254] = 1, [198] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[197] = 5, [1254] = 1, [198] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[197] = 3, [1254] = 2, [198] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[197] = 4, [1254] = 2, [198] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[197] = 5, [1254] = 2, [198] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[197] = 2, [1254] = 3, [198] = 5, __size = 3},
		lotteryType8 = {[197] = 3, [1254] = 3, [198] = 4, __size = 3},
		lotteryType9 = {[197] = 4, [1254] = 3, [198] = 3, __size = 3}
	},
	[15200] = {
		id = 15200,
		drawType = 'group_up_rmb1_3',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[200] = 1, __size = 1},
		lotteryWeight1 = 100,
		lotteryType2 = {[183] = 1, __size = 1},
		lotteryWeight2 = 100
	},
	[15201] = {
		id = 15201,
		drawType = 'group_up_rmb1_3',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[184] = 1, __size = 1},
		lotteryWeight1 = 80
	},
	[15202] = {
		id = 15202,
		drawType = 'group_up_rmb1_3',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1255] = 1, __size = 1},
		lotteryWeight1 = 50
	},
	[15203] = {
		id = 15203,
		drawType = 'group_up_rmb1_3',
		drawTriggerTimes = 6,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1255] = 1, __size = 1},
		lotteryWeight1 = 50
	},
	[15204] = {
		id = 15204,
		drawType = 'group_up_rmb1_3',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1255] = 1, __size = 1},
		lotteryWeight1 = 50
	},
	[15211] = {
		id = 15211,
		drawType = 'group_up_rmb10_3',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[200] = 3, [183] = 6, [1255] = 1, __size = 3},
		lotteryWeight1 = 60,
		lotteryType2 = {[200] = 4, [183] = 5, [1255] = 1, __size = 3},
		lotteryWeight2 = 60
	},
	[15212] = {
		id = 15212,
		drawType = 'group_up_rmb10_3',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[200] = 3, [183] = 6, [1255] = 1, __size = 3},
		lotteryWeight1 = 80,
		lotteryType2 = {[200] = 4, [183] = 5, [1255] = 1, __size = 3},
		lotteryWeight2 = 60,
		lotteryType3 = {[200] = 5, [183] = 4, [1255] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[200] = 3, [183] = 5, [1255] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[200] = 4, [183] = 4, [1255] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[200] = 5, [183] = 3, [1255] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[200] = 2, [183] = 5, [1255] = 3, __size = 3},
		lotteryType8 = {[200] = 3, [183] = 4, [1255] = 3, __size = 3},
		lotteryType9 = {[200] = 4, [183] = 3, [1255] = 3, __size = 3}
	},
	[15213] = {
		id = 15213,
		drawType = 'group_up_rmb10_3',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[1256] = 1, [200] = 3, [183] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1256] = 1, [200] = 4, [183] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1256] = 1, [200] = 5, [183] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1256] = 2, [200] = 3, [183] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1256] = 2, [200] = 4, [183] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1256] = 2, [200] = 5, [183] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1256] = 3, [200] = 2, [183] = 5, __size = 3},
		lotteryType8 = {[1256] = 3, [200] = 3, [183] = 4, __size = 3},
		lotteryType9 = {[1256] = 3, [200] = 4, [183] = 3, __size = 3}
	},
	[15300] = {
		id = 15300,
		drawType = 'group_up_rmb1_4',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[185] = 1, __size = 1},
		lotteryWeight1 = 100,
		lotteryType2 = {[186] = 1, __size = 1},
		lotteryWeight2 = 100
	},
	[15301] = {
		id = 15301,
		drawType = 'group_up_rmb1_4',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[187] = 1, __size = 1},
		lotteryWeight1 = 80
	},
	[15302] = {
		id = 15302,
		drawType = 'group_up_rmb1_4',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1257] = 1, __size = 1},
		lotteryWeight1 = 50
	},
	[15303] = {
		id = 15303,
		drawType = 'group_up_rmb1_4',
		drawTriggerTimes = 6,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1257] = 1, __size = 1},
		lotteryWeight1 = 50
	},
	[15304] = {
		id = 15304,
		drawType = 'group_up_rmb1_4',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1257] = 1, __size = 1},
		lotteryWeight1 = 50
	},
	[15311] = {
		id = 15311,
		drawType = 'group_up_rmb10_4',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[1257] = 1, [186] = 6, [185] = 3, __size = 3},
		lotteryWeight1 = 60,
		lotteryType2 = {[1257] = 1, [186] = 5, [185] = 4, __size = 3},
		lotteryWeight2 = 60
	},
	[15312] = {
		id = 15312,
		drawType = 'group_up_rmb10_4',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[1257] = 1, [186] = 6, [185] = 3, __size = 3},
		lotteryWeight1 = 80,
		lotteryType2 = {[1257] = 1, [186] = 5, [185] = 4, __size = 3},
		lotteryWeight2 = 60,
		lotteryType3 = {[1257] = 1, [186] = 4, [185] = 5, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1257] = 2, [186] = 5, [185] = 3, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1257] = 2, [186] = 4, [185] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1257] = 2, [186] = 3, [185] = 5, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1257] = 3, [186] = 5, [185] = 2, __size = 3},
		lotteryType8 = {[1257] = 3, [186] = 4, [185] = 3, __size = 3},
		lotteryType9 = {[1257] = 3, [186] = 3, [185] = 4, __size = 3}
	},
	[15313] = {
		id = 15313,
		drawType = 'group_up_rmb10_4',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[185] = 3, [1258] = 1, [186] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[185] = 4, [1258] = 1, [186] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[185] = 5, [1258] = 1, [186] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[185] = 3, [1258] = 2, [186] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[185] = 4, [1258] = 2, [186] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[185] = 5, [1258] = 2, [186] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[185] = 2, [1258] = 3, [186] = 5, __size = 3},
		lotteryType8 = {[185] = 3, [1258] = 3, [186] = 4, __size = 3},
		lotteryType9 = {[185] = 4, [1258] = 3, [186] = 3, __size = 3}
	},
	[15400] = {
		id = 15400,
		drawType = 'group_up_rmb1_5',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[15401] = {
		id = 15401,
		drawType = 'group_up_rmb1_5',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[15402] = {
		id = 15402,
		drawType = 'group_up_rmb1_5',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1259] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[15403] = {
		id = 15403,
		drawType = 'group_up_rmb1_5',
		drawTriggerTimes = 6,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1259] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[15404] = {
		id = 15404,
		drawType = 'group_up_rmb1_5',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1259] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[15411] = {
		id = 15411,
		drawType = 'group_up_rmb10_5',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[205] = 3, [206] = 6, [1259] = 1, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[205] = 4, [206] = 5, [1259] = 1, __size = 3},
		lotteryWeight2 = 100
	},
	[15412] = {
		id = 15412,
		drawType = 'group_up_rmb10_5',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 3, [206] = 6, [1259] = 1, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [206] = 5, [1259] = 1, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [206] = 4, [1259] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [206] = 5, [1259] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [206] = 4, [1259] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [206] = 3, [1259] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [206] = 5, [1259] = 3, __size = 3},
		lotteryType8 = {[205] = 3, [206] = 4, [1259] = 3, __size = 3},
		lotteryType9 = {[205] = 4, [206] = 3, [1259] = 3, __size = 3}
	},
	[15413] = {
		id = 15413,
		drawType = 'group_up_rmb10_5',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[1260] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1260] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1260] = 1, [205] = 5, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1260] = 2, [205] = 3, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1260] = 2, [205] = 4, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1260] = 2, [205] = 5, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1260] = 3, [205] = 2, [206] = 5, __size = 3},
		lotteryType8 = {[1260] = 3, [205] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[1260] = 3, [205] = 4, [206] = 3, __size = 3}
	},
	[15500] = {
		id = 15500,
		drawType = 'group_up_rmb1_6',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[15501] = {
		id = 15501,
		drawType = 'group_up_rmb1_6',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[15502] = {
		id = 15502,
		drawType = 'group_up_rmb1_6',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1261] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[15503] = {
		id = 15503,
		drawType = 'group_up_rmb1_6',
		drawTriggerTimes = 6,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1261] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[15504] = {
		id = 15504,
		drawType = 'group_up_rmb1_6',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1261] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[15511] = {
		id = 15511,
		drawType = 'group_up_rmb10_6',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[1261] = 1, [206] = 6, [205] = 3, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[1261] = 1, [206] = 5, [205] = 4, __size = 3},
		lotteryWeight2 = 100
	},
	[15512] = {
		id = 15512,
		drawType = 'group_up_rmb10_6',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[1261] = 1, [206] = 6, [205] = 3, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1261] = 1, [206] = 5, [205] = 4, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1261] = 1, [206] = 4, [205] = 5, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1261] = 2, [206] = 5, [205] = 3, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1261] = 2, [206] = 4, [205] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1261] = 2, [206] = 3, [205] = 5, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1261] = 3, [206] = 5, [205] = 2, __size = 3},
		lotteryType8 = {[1261] = 3, [206] = 4, [205] = 3, __size = 3},
		lotteryType9 = {[1261] = 3, [206] = 3, [205] = 4, __size = 3}
	},
	[15513] = {
		id = 15513,
		drawType = 'group_up_rmb10_6',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[205] = 3, [1262] = 1, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [1262] = 1, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [1262] = 1, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [1262] = 2, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [1262] = 2, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [1262] = 2, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [1262] = 3, [206] = 5, __size = 3},
		lotteryType8 = {[205] = 3, [1262] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[205] = 4, [1262] = 3, [206] = 3, __size = 3}
	},
	[15600] = {
		id = 15600,
		drawType = 'group_up_rmb1_7',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[15601] = {
		id = 15601,
		drawType = 'group_up_rmb1_7',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[15602] = {
		id = 15602,
		drawType = 'group_up_rmb1_7',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1263] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[15603] = {
		id = 15603,
		drawType = 'group_up_rmb1_7',
		drawTriggerTimes = 6,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1263] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[15604] = {
		id = 15604,
		drawType = 'group_up_rmb1_7',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1263] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[15611] = {
		id = 15611,
		drawType = 'group_up_rmb10_7',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[205] = 3, [206] = 6, [1263] = 1, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[205] = 4, [206] = 5, [1263] = 1, __size = 3},
		lotteryWeight2 = 100
	},
	[15612] = {
		id = 15612,
		drawType = 'group_up_rmb10_7',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 3, [206] = 6, [1263] = 1, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [206] = 5, [1263] = 1, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [206] = 4, [1263] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [206] = 5, [1263] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [206] = 4, [1263] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [206] = 3, [1263] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [206] = 5, [1263] = 3, __size = 3},
		lotteryType8 = {[205] = 3, [206] = 4, [1263] = 3, __size = 3},
		lotteryType9 = {[205] = 4, [206] = 3, [1263] = 3, __size = 3}
	},
	[15613] = {
		id = 15613,
		drawType = 'group_up_rmb10_7',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[1264] = 1, [205] = 3, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[1264] = 1, [205] = 4, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[1264] = 1, [205] = 5, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[1264] = 2, [205] = 3, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[1264] = 2, [205] = 4, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[1264] = 2, [205] = 5, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[1264] = 3, [205] = 2, [206] = 5, __size = 3},
		lotteryType8 = {[1264] = 3, [205] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[1264] = 3, [205] = 4, [206] = 3, __size = 3}
	},
	[15700] = {
		id = 15700,
		drawType = 'group_up_rmb1_8',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 1, __size = 1},
		lotteryWeight1 = 50,
		lotteryType2 = {[206] = 1, __size = 1},
		lotteryWeight2 = 50
	},
	[15701] = {
		id = 15701,
		drawType = 'group_up_rmb1_8',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[204] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[15702] = {
		id = 15702,
		drawType = 'group_up_rmb1_8',
		drawTriggerTimes = 5,
		effectLimit = 1,
		startCount = 5,
		probInit = 0.17,
		probStep = 0.06,
		probLimit = 0.5,
		probMiniTimes = 6,
		probEffectInterval = 999999,
		lotteryType1 = {[1265] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[15703] = {
		id = 15703,
		drawType = 'group_up_rmb1_8',
		drawTriggerTimes = 6,
		startCount = 10,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1265] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[15704] = {
		id = 15704,
		drawType = 'group_up_rmb1_8',
		drawTriggerTimes = 160,
		startCount = 160,
		probInit = 0.05,
		probStep = 0.015,
		probLimit = 0.5,
		probMiniTimes = 15,
		probEffectInterval = 15,
		lotteryType1 = {[1265] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[15711] = {
		id = 15711,
		drawType = 'group_up_rmb10_8',
		drawTriggerType = 4,
		drawTriggerTimes = 1,
		lotteryType1 = {[205] = 3, [206] = 6, [1265] = 1, __size = 3},
		lotteryWeight1 = 100,
		lotteryType2 = {[205] = 4, [206] = 5, [1265] = 1, __size = 3},
		lotteryWeight2 = 100
	},
	[15712] = {
		id = 15712,
		drawType = 'group_up_rmb10_8',
		drawTriggerType = 0,
		drawTriggerTimes = 2,
		lotteryType1 = {[205] = 3, [206] = 6, [1265] = 1, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [206] = 5, [1265] = 1, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [206] = 4, [1265] = 1, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [206] = 5, [1265] = 2, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [206] = 4, [1265] = 2, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [206] = 3, [1265] = 2, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [206] = 5, [1265] = 3, __size = 3},
		lotteryType8 = {[205] = 3, [206] = 4, [1265] = 3, __size = 3},
		lotteryType9 = {[205] = 4, [206] = 3, [1265] = 3, __size = 3}
	},
	[15713] = {
		id = 15713,
		drawType = 'group_up_rmb10_8',
		drawTriggerType = 0,
		drawTriggerTimes = 22,
		lotteryType1 = {[205] = 3, [1266] = 1, [206] = 6, __size = 3},
		lotteryWeight1 = 150,
		lotteryType2 = {[205] = 4, [1266] = 1, [206] = 5, __size = 3},
		lotteryWeight2 = 100,
		lotteryType3 = {[205] = 5, [1266] = 1, [206] = 4, __size = 3},
		lotteryWeight3 = 50,
		lotteryType4 = {[205] = 3, [1266] = 2, [206] = 5, __size = 3},
		lotteryWeight4 = 30,
		lotteryType5 = {[205] = 4, [1266] = 2, [206] = 4, __size = 3},
		lotteryWeight5 = 20,
		lotteryType6 = {[205] = 5, [1266] = 2, [206] = 3, __size = 3},
		lotteryWeight6 = 10,
		lotteryType7 = {[205] = 2, [1266] = 3, [206] = 5, __size = 3},
		lotteryType8 = {[205] = 3, [1266] = 3, [206] = 4, __size = 3},
		lotteryType9 = {[205] = 4, [1266] = 3, [206] = 3, __size = 3}
	},
	[16002] = {
		id = 16002,
		drawType = 'limit_box_rmb1_60001',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[16003] = {
		id = 16003,
		drawType = 'limit_box_rmb1_60001',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[16005] = {
		id = 16005,
		drawType = 'limit_box_rmb10_60001',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[16006] = {
		id = 16006,
		drawType = 'limit_box_rmb10_60001',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[16007] = {
		id = 16007,
		drawType = 'limit_box_rmb10_60001',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[16008] = {
		id = 16008,
		drawType = 'limit_box_rmb10_60001',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[16009] = {
		id = 16009,
		drawType = 'limit_box_rmb10_60001',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[16010] = {
		id = 16010,
		drawType = 'limit_box_rmb10_60001',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[16020] = {
		id = 16020,
		drawType = 'limit_box_rmb1_60002',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		lotteryType1 = {[150] = 1, __size = 1},
		lotteryWeight1 = 100
	},
	[16021] = {
		id = 16021,
		drawType = 'limit_box_rmb1_60002',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 1, __size = 1},
		lotteryWeight1 = 45,
		lotteryType2 = {[153] = 1, __size = 1},
		lotteryWeight2 = 15,
		lotteryType3 = {[154] = 1, __size = 1},
		lotteryWeight3 = 15,
		lotteryType4 = {[160] = 1, __size = 1},
		lotteryWeight4 = 15,
		lotteryType5 = {[161] = 1, __size = 1},
		lotteryWeight5 = 5
	},
	[16023] = {
		id = 16023,
		drawType = 'limit_box_rmb10_60002',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.04,
		weightEnd = 0.09,
		startCount = 19,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[16024] = {
		id = 16024,
		drawType = 'limit_box_rmb10_60002',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.08,
		weightEnd = 0.15,
		effectLimit = 1,
		startCount = 11,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[16025] = {
		id = 16025,
		drawType = 'limit_box_rmb10_60002',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 5,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [162] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[16026] = {
		id = 16026,
		drawType = 'limit_box_rmb10_60002',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.06,
		weightEnd = 0.12,
		startCount = 6,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[16027] = {
		id = 16027,
		drawType = 'limit_box_rmb10_60002',
		drawTriggerType = 1,
		drawTriggerTimes = 10,
		weightStart = 0.2,
		weightEnd = 0.3,
		effectLimit = 1,
		startCount = 2,
		lotteryType1 = {[152] = 3, [153] = 1, [154] = 2, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 2, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 4, [153] = 1, [154] = 1, [160] = 1, [1101] = 1, [150] = 1, [151] = 1, __size = 7},
		lotteryWeight3 = 100
	},
	[16028] = {
		id = 16028,
		drawType = 'limit_box_rmb10_60002',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[152] = 3, [153] = 2, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight1 = 100,
		lotteryType2 = {[152] = 3, [153] = 1, [154] = 2, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight2 = 100,
		lotteryType3 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight3 = 100,
		lotteryType4 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight4 = 100,
		lotteryType5 = {[152] = 4, [153] = 2, [154] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 6},
		lotteryWeight5 = 100,
		lotteryType6 = {[152] = 4, [153] = 1, [154] = 1, [161] = 1, [1101] = 1, [150] = 1, [160] = 1, __size = 7},
		lotteryWeight6 = 100,
		lotteryType7 = {[152] = 4, [153] = 1, [154] = 2, [1101] = 1, [150] = 1, [161] = 1, __size = 6},
		lotteryWeight7 = 100,
		lotteryType8 = {[152] = 3, [153] = 1, [154] = 3, [1101] = 1, [150] = 1, [160] = 1, __size = 6}
	},
	[16101] = {
		id = 16101,
		drawType = 'limit_rmb1_60003',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1191] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[16102] = {
		id = 16102,
		drawType = 'limit_rmb1_60003',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[16103] = {
		id = 16103,
		drawType = 'limit_rmb1_60003',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1190] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[16104] = {
		id = 16104,
		drawType = 'limit_rmb1_60003',
		drawTriggerTimes = 155,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1190] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[51181] = {
		id = 51181,
		drawType = 'limit_rmb1_9954',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1119] = 1, __size = 1},
		lotteryWeight4 = 150
	},
	[51182] = {
		id = 51182,
		drawType = 'limit_rmb1_9954',
		drawTriggerTimes = 1,
		startCount = 3,
		probInit = 0.15,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 5,
		probEffectInterval = 10,
		lotteryType1 = {[201] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[51183] = {
		id = 51183,
		drawType = 'limit_rmb1_9954',
		drawTriggerTimes = 80,
		effectLimit = 1,
		startCount = 73,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 99999,
		lotteryType1 = {[1118] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[51184] = {
		id = 51184,
		drawType = 'limit_rmb1_9954',
		drawTriggerTimes = 155,
		effectLimit = 999,
		startCount = 144,
		probInit = 0.1,
		probStep = 0.05,
		probLimit = 0.4,
		probMiniTimes = 9,
		probEffectInterval = 71,
		lotteryType1 = {[1118] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60002] = {
		id = 60002,
		drawType = 'limit_rmb1_16601',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[22515] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1185] = 1, __size = 1},
		lotteryWeight4 = 150,
		lotteryType5 = {[1186] = 1, __size = 1},
		lotteryWeight5 = 1
	},
	[60003] = {
		id = 60003,
		drawType = 'limit_rmb1_16601',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		probEffectInterval = 10,
		lotteryType1 = {[22515] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60004] = {
		id = 60004,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 250,
		effectLimit = 1,
		startCount = 150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60005] = {
		id = 60005,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 400,
		effectLimit = 1,
		startCount = 300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60006] = {
		id = 60006,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 550,
		effectLimit = 1,
		startCount = 450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60007] = {
		id = 60007,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 700,
		effectLimit = 1,
		startCount = 600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60008] = {
		id = 60008,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 850,
		effectLimit = 1,
		startCount = 750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60009] = {
		id = 60009,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 1000,
		effectLimit = 1,
		startCount = 900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60010] = {
		id = 60010,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 1150,
		effectLimit = 1,
		startCount = 1050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60011] = {
		id = 60011,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 1300,
		effectLimit = 1,
		startCount = 1200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60012] = {
		id = 60012,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 1450,
		effectLimit = 1,
		startCount = 1350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60013] = {
		id = 60013,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 1600,
		effectLimit = 1,
		startCount = 1500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60014] = {
		id = 60014,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 1750,
		effectLimit = 1,
		startCount = 1650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60015] = {
		id = 60015,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 1900,
		effectLimit = 1,
		startCount = 1800,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60016] = {
		id = 60016,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 2050,
		effectLimit = 1,
		startCount = 1950,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60017] = {
		id = 60017,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 2200,
		effectLimit = 1,
		startCount = 2100,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60018] = {
		id = 60018,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 2350,
		effectLimit = 1,
		startCount = 2250,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60019] = {
		id = 60019,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 2500,
		effectLimit = 1,
		startCount = 2400,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60020] = {
		id = 60020,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 2650,
		effectLimit = 1,
		startCount = 2550,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60021] = {
		id = 60021,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 2800,
		effectLimit = 1,
		startCount = 2700,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60022] = {
		id = 60022,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 2950,
		effectLimit = 1,
		startCount = 2850,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60023] = {
		id = 60023,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 3100,
		effectLimit = 1,
		startCount = 3000,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60024] = {
		id = 60024,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 3250,
		effectLimit = 1,
		startCount = 3150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60025] = {
		id = 60025,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 3400,
		effectLimit = 1,
		startCount = 3300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60026] = {
		id = 60026,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 3550,
		effectLimit = 1,
		startCount = 3450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60027] = {
		id = 60027,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 3700,
		effectLimit = 1,
		startCount = 3600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60028] = {
		id = 60028,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 3850,
		effectLimit = 1,
		startCount = 3750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60029] = {
		id = 60029,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 4000,
		effectLimit = 1,
		startCount = 3900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60030] = {
		id = 60030,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 4150,
		effectLimit = 1,
		startCount = 4050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60031] = {
		id = 60031,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 4300,
		effectLimit = 1,
		startCount = 4200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60032] = {
		id = 60032,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 4450,
		effectLimit = 1,
		startCount = 4350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60033] = {
		id = 60033,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 4600,
		effectLimit = 1,
		startCount = 4500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60034] = {
		id = 60034,
		drawType = 'limit_rmb1_16601',
		drawTriggerTimes = 4750,
		effectLimit = 1,
		startCount = 4650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60035] = {
		id = 60035,
		drawType = 'limit_rmb1_16602',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[22519] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1185] = 1, __size = 1},
		lotteryWeight4 = 150,
		lotteryType5 = {[1187] = 1, __size = 1},
		lotteryWeight5 = 1
	},
	[60036] = {
		id = 60036,
		drawType = 'limit_rmb1_16602',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		probEffectInterval = 10,
		lotteryType1 = {[22519] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60037] = {
		id = 60037,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 250,
		effectLimit = 1,
		startCount = 150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60038] = {
		id = 60038,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 400,
		effectLimit = 1,
		startCount = 300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60039] = {
		id = 60039,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 550,
		effectLimit = 1,
		startCount = 450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60040] = {
		id = 60040,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 700,
		effectLimit = 1,
		startCount = 600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60041] = {
		id = 60041,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 850,
		effectLimit = 1,
		startCount = 750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60042] = {
		id = 60042,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 1000,
		effectLimit = 1,
		startCount = 900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60043] = {
		id = 60043,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 1150,
		effectLimit = 1,
		startCount = 1050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60044] = {
		id = 60044,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 1300,
		effectLimit = 1,
		startCount = 1200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60045] = {
		id = 60045,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 1450,
		effectLimit = 1,
		startCount = 1350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60046] = {
		id = 60046,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 1600,
		effectLimit = 1,
		startCount = 1500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60047] = {
		id = 60047,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 1750,
		effectLimit = 1,
		startCount = 1650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60048] = {
		id = 60048,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 1900,
		effectLimit = 1,
		startCount = 1800,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60049] = {
		id = 60049,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 2050,
		effectLimit = 1,
		startCount = 1950,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60050] = {
		id = 60050,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 2200,
		effectLimit = 1,
		startCount = 2100,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60051] = {
		id = 60051,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 2350,
		effectLimit = 1,
		startCount = 2250,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60052] = {
		id = 60052,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 2500,
		effectLimit = 1,
		startCount = 2400,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60053] = {
		id = 60053,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 2650,
		effectLimit = 1,
		startCount = 2550,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60054] = {
		id = 60054,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 2800,
		effectLimit = 1,
		startCount = 2700,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60055] = {
		id = 60055,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 2950,
		effectLimit = 1,
		startCount = 2850,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60056] = {
		id = 60056,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 3100,
		effectLimit = 1,
		startCount = 3000,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60057] = {
		id = 60057,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 3250,
		effectLimit = 1,
		startCount = 3150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60058] = {
		id = 60058,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 3400,
		effectLimit = 1,
		startCount = 3300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60059] = {
		id = 60059,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 3550,
		effectLimit = 1,
		startCount = 3450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60060] = {
		id = 60060,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 3700,
		effectLimit = 1,
		startCount = 3600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60061] = {
		id = 60061,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 3850,
		effectLimit = 1,
		startCount = 3750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60062] = {
		id = 60062,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 4000,
		effectLimit = 1,
		startCount = 3900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60063] = {
		id = 60063,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 4150,
		effectLimit = 1,
		startCount = 4050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60064] = {
		id = 60064,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 4300,
		effectLimit = 1,
		startCount = 4200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60065] = {
		id = 60065,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 4450,
		effectLimit = 1,
		startCount = 4350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60066] = {
		id = 60066,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 4600,
		effectLimit = 1,
		startCount = 4500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60067] = {
		id = 60067,
		drawType = 'limit_rmb1_16602',
		drawTriggerTimes = 4750,
		effectLimit = 1,
		startCount = 4650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60068] = {
		id = 60068,
		drawType = 'limit_rmb1_16603',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[22512] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1185] = 1, __size = 1},
		lotteryWeight4 = 150,
		lotteryType5 = {[1184] = 1, __size = 1},
		lotteryWeight5 = 1
	},
	[60069] = {
		id = 60069,
		drawType = 'limit_rmb1_16603',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		probEffectInterval = 10,
		lotteryType1 = {[22512] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60070] = {
		id = 60070,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 250,
		effectLimit = 1,
		startCount = 150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60071] = {
		id = 60071,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 400,
		effectLimit = 1,
		startCount = 300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60072] = {
		id = 60072,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 550,
		effectLimit = 1,
		startCount = 450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60073] = {
		id = 60073,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 700,
		effectLimit = 1,
		startCount = 600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60074] = {
		id = 60074,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 850,
		effectLimit = 1,
		startCount = 750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60075] = {
		id = 60075,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 1000,
		effectLimit = 1,
		startCount = 900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60076] = {
		id = 60076,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 1150,
		effectLimit = 1,
		startCount = 1050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60077] = {
		id = 60077,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 1300,
		effectLimit = 1,
		startCount = 1200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60078] = {
		id = 60078,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 1450,
		effectLimit = 1,
		startCount = 1350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60079] = {
		id = 60079,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 1600,
		effectLimit = 1,
		startCount = 1500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60080] = {
		id = 60080,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 1750,
		effectLimit = 1,
		startCount = 1650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60081] = {
		id = 60081,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 1900,
		effectLimit = 1,
		startCount = 1800,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60082] = {
		id = 60082,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 2050,
		effectLimit = 1,
		startCount = 1950,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60083] = {
		id = 60083,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 2200,
		effectLimit = 1,
		startCount = 2100,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60084] = {
		id = 60084,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 2350,
		effectLimit = 1,
		startCount = 2250,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60085] = {
		id = 60085,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 2500,
		effectLimit = 1,
		startCount = 2400,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60086] = {
		id = 60086,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 2650,
		effectLimit = 1,
		startCount = 2550,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60087] = {
		id = 60087,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 2800,
		effectLimit = 1,
		startCount = 2700,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60088] = {
		id = 60088,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 2950,
		effectLimit = 1,
		startCount = 2850,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60089] = {
		id = 60089,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 3100,
		effectLimit = 1,
		startCount = 3000,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60090] = {
		id = 60090,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 3250,
		effectLimit = 1,
		startCount = 3150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60091] = {
		id = 60091,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 3400,
		effectLimit = 1,
		startCount = 3300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60092] = {
		id = 60092,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 3550,
		effectLimit = 1,
		startCount = 3450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60093] = {
		id = 60093,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 3700,
		effectLimit = 1,
		startCount = 3600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60094] = {
		id = 60094,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 3850,
		effectLimit = 1,
		startCount = 3750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60095] = {
		id = 60095,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 4000,
		effectLimit = 1,
		startCount = 3900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60096] = {
		id = 60096,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 4150,
		effectLimit = 1,
		startCount = 4050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60097] = {
		id = 60097,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 4300,
		effectLimit = 1,
		startCount = 4200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60098] = {
		id = 60098,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 4450,
		effectLimit = 1,
		startCount = 4350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60099] = {
		id = 60099,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 4600,
		effectLimit = 1,
		startCount = 4500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60100] = {
		id = 60100,
		drawType = 'limit_rmb1_16603',
		drawTriggerTimes = 4750,
		effectLimit = 1,
		startCount = 4650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60101] = {
		id = 60101,
		drawType = 'limit_rmb1_16604',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[22515] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1185] = 1, __size = 1},
		lotteryWeight4 = 150,
		lotteryType5 = {[1186] = 1, __size = 1},
		lotteryWeight5 = 1
	},
	[60102] = {
		id = 60102,
		drawType = 'limit_rmb1_16604',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		probEffectInterval = 10,
		lotteryType1 = {[22515] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60103] = {
		id = 60103,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 250,
		effectLimit = 1,
		startCount = 150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60104] = {
		id = 60104,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 400,
		effectLimit = 1,
		startCount = 300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60105] = {
		id = 60105,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 550,
		effectLimit = 1,
		startCount = 450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60106] = {
		id = 60106,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 700,
		effectLimit = 1,
		startCount = 600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60107] = {
		id = 60107,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 850,
		effectLimit = 1,
		startCount = 750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60108] = {
		id = 60108,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 1000,
		effectLimit = 1,
		startCount = 900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60109] = {
		id = 60109,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 1150,
		effectLimit = 1,
		startCount = 1050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60110] = {
		id = 60110,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 1300,
		effectLimit = 1,
		startCount = 1200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60111] = {
		id = 60111,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 1450,
		effectLimit = 1,
		startCount = 1350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60112] = {
		id = 60112,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 1600,
		effectLimit = 1,
		startCount = 1500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60113] = {
		id = 60113,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 1750,
		effectLimit = 1,
		startCount = 1650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60114] = {
		id = 60114,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 1900,
		effectLimit = 1,
		startCount = 1800,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60115] = {
		id = 60115,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 2050,
		effectLimit = 1,
		startCount = 1950,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60116] = {
		id = 60116,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 2200,
		effectLimit = 1,
		startCount = 2100,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60117] = {
		id = 60117,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 2350,
		effectLimit = 1,
		startCount = 2250,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60118] = {
		id = 60118,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 2500,
		effectLimit = 1,
		startCount = 2400,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60119] = {
		id = 60119,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 2650,
		effectLimit = 1,
		startCount = 2550,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60120] = {
		id = 60120,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 2800,
		effectLimit = 1,
		startCount = 2700,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60121] = {
		id = 60121,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 2950,
		effectLimit = 1,
		startCount = 2850,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60122] = {
		id = 60122,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 3100,
		effectLimit = 1,
		startCount = 3000,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60123] = {
		id = 60123,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 3250,
		effectLimit = 1,
		startCount = 3150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60124] = {
		id = 60124,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 3400,
		effectLimit = 1,
		startCount = 3300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60125] = {
		id = 60125,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 3550,
		effectLimit = 1,
		startCount = 3450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60126] = {
		id = 60126,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 3700,
		effectLimit = 1,
		startCount = 3600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60127] = {
		id = 60127,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 3850,
		effectLimit = 1,
		startCount = 3750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60128] = {
		id = 60128,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 4000,
		effectLimit = 1,
		startCount = 3900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60129] = {
		id = 60129,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 4150,
		effectLimit = 1,
		startCount = 4050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60130] = {
		id = 60130,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 4300,
		effectLimit = 1,
		startCount = 4200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60131] = {
		id = 60131,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 4450,
		effectLimit = 1,
		startCount = 4350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60132] = {
		id = 60132,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 4600,
		effectLimit = 1,
		startCount = 4500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60133] = {
		id = 60133,
		drawType = 'limit_rmb1_16604',
		drawTriggerTimes = 4750,
		effectLimit = 1,
		startCount = 4650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60134] = {
		id = 60134,
		drawType = 'limit_rmb1_16605',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[22526] = 1, __size = 1},
		lotteryType2 = {[22527] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1185] = 1, __size = 1},
		lotteryWeight4 = 150,
		lotteryType5 = {[1188] = 1, __size = 1},
		lotteryWeight5 = 1
	},
	[60135] = {
		id = 60135,
		drawType = 'limit_rmb1_16605',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		probEffectInterval = 10,
		lotteryType1 = {[22526] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60136] = {
		id = 60136,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 250,
		effectLimit = 1,
		startCount = 150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60137] = {
		id = 60137,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 400,
		effectLimit = 1,
		startCount = 300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60138] = {
		id = 60138,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 550,
		effectLimit = 1,
		startCount = 450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60139] = {
		id = 60139,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 700,
		effectLimit = 1,
		startCount = 600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60140] = {
		id = 60140,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 850,
		effectLimit = 1,
		startCount = 750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60141] = {
		id = 60141,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 1000,
		effectLimit = 1,
		startCount = 900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60142] = {
		id = 60142,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 1150,
		effectLimit = 1,
		startCount = 1050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60143] = {
		id = 60143,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 1300,
		effectLimit = 1,
		startCount = 1200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60144] = {
		id = 60144,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 1450,
		effectLimit = 1,
		startCount = 1350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60145] = {
		id = 60145,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 1600,
		effectLimit = 1,
		startCount = 1500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60146] = {
		id = 60146,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 1750,
		effectLimit = 1,
		startCount = 1650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60147] = {
		id = 60147,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 1900,
		effectLimit = 1,
		startCount = 1800,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60148] = {
		id = 60148,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 2050,
		effectLimit = 1,
		startCount = 1950,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60149] = {
		id = 60149,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 2200,
		effectLimit = 1,
		startCount = 2100,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60150] = {
		id = 60150,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 2350,
		effectLimit = 1,
		startCount = 2250,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60151] = {
		id = 60151,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 2500,
		effectLimit = 1,
		startCount = 2400,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60152] = {
		id = 60152,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 2650,
		effectLimit = 1,
		startCount = 2550,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60153] = {
		id = 60153,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 2800,
		effectLimit = 1,
		startCount = 2700,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60154] = {
		id = 60154,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 2950,
		effectLimit = 1,
		startCount = 2850,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60155] = {
		id = 60155,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 3100,
		effectLimit = 1,
		startCount = 3000,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60156] = {
		id = 60156,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 3250,
		effectLimit = 1,
		startCount = 3150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60157] = {
		id = 60157,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 3400,
		effectLimit = 1,
		startCount = 3300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60158] = {
		id = 60158,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 3550,
		effectLimit = 1,
		startCount = 3450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60159] = {
		id = 60159,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 3700,
		effectLimit = 1,
		startCount = 3600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60160] = {
		id = 60160,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 3850,
		effectLimit = 1,
		startCount = 3750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60161] = {
		id = 60161,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 4000,
		effectLimit = 1,
		startCount = 3900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60162] = {
		id = 60162,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 4150,
		effectLimit = 1,
		startCount = 4050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60163] = {
		id = 60163,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 4300,
		effectLimit = 1,
		startCount = 4200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60164] = {
		id = 60164,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 4450,
		effectLimit = 1,
		startCount = 4350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60165] = {
		id = 60165,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 4600,
		effectLimit = 1,
		startCount = 4500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60166] = {
		id = 60166,
		drawType = 'limit_rmb1_16605',
		drawTriggerTimes = 4750,
		effectLimit = 1,
		startCount = 4650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1188] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60167] = {
		id = 60167,
		drawType = 'limit_rmb1_16606',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[22519] = 1, __size = 1},
		lotteryType2 = {[22529] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1185] = 1, __size = 1},
		lotteryWeight4 = 150,
		lotteryType5 = {[1187] = 1, __size = 1},
		lotteryWeight5 = 1
	},
	[60168] = {
		id = 60168,
		drawType = 'limit_rmb1_16606',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		probEffectInterval = 10,
		lotteryType1 = {[22519] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60169] = {
		id = 60169,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 250,
		effectLimit = 1,
		startCount = 150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60170] = {
		id = 60170,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 400,
		effectLimit = 1,
		startCount = 300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60171] = {
		id = 60171,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 550,
		effectLimit = 1,
		startCount = 450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60172] = {
		id = 60172,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 700,
		effectLimit = 1,
		startCount = 600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60173] = {
		id = 60173,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 850,
		effectLimit = 1,
		startCount = 750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60174] = {
		id = 60174,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 1000,
		effectLimit = 1,
		startCount = 900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60175] = {
		id = 60175,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 1150,
		effectLimit = 1,
		startCount = 1050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60176] = {
		id = 60176,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 1300,
		effectLimit = 1,
		startCount = 1200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60177] = {
		id = 60177,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 1450,
		effectLimit = 1,
		startCount = 1350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60178] = {
		id = 60178,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 1600,
		effectLimit = 1,
		startCount = 1500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60179] = {
		id = 60179,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 1750,
		effectLimit = 1,
		startCount = 1650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60180] = {
		id = 60180,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 1900,
		effectLimit = 1,
		startCount = 1800,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60181] = {
		id = 60181,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 2050,
		effectLimit = 1,
		startCount = 1950,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60182] = {
		id = 60182,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 2200,
		effectLimit = 1,
		startCount = 2100,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60183] = {
		id = 60183,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 2350,
		effectLimit = 1,
		startCount = 2250,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60184] = {
		id = 60184,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 2500,
		effectLimit = 1,
		startCount = 2400,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60185] = {
		id = 60185,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 2650,
		effectLimit = 1,
		startCount = 2550,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60186] = {
		id = 60186,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 2800,
		effectLimit = 1,
		startCount = 2700,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60187] = {
		id = 60187,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 2950,
		effectLimit = 1,
		startCount = 2850,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60188] = {
		id = 60188,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 3100,
		effectLimit = 1,
		startCount = 3000,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60189] = {
		id = 60189,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 3250,
		effectLimit = 1,
		startCount = 3150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60190] = {
		id = 60190,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 3400,
		effectLimit = 1,
		startCount = 3300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60191] = {
		id = 60191,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 3550,
		effectLimit = 1,
		startCount = 3450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60192] = {
		id = 60192,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 3700,
		effectLimit = 1,
		startCount = 3600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60193] = {
		id = 60193,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 3850,
		effectLimit = 1,
		startCount = 3750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60194] = {
		id = 60194,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 4000,
		effectLimit = 1,
		startCount = 3900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60195] = {
		id = 60195,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 4150,
		effectLimit = 1,
		startCount = 4050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60196] = {
		id = 60196,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 4300,
		effectLimit = 1,
		startCount = 4200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60197] = {
		id = 60197,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 4450,
		effectLimit = 1,
		startCount = 4350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60198] = {
		id = 60198,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 4600,
		effectLimit = 1,
		startCount = 4500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60199] = {
		id = 60199,
		drawType = 'limit_rmb1_16606',
		drawTriggerTimes = 4750,
		effectLimit = 1,
		startCount = 4650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60200] = {
		id = 60200,
		drawType = 'limit_rmb1_16901',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[22515] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1185] = 1, __size = 1},
		lotteryWeight4 = 150,
		lotteryType5 = {[1186] = 1, __size = 1},
		lotteryWeight5 = 1
	},
	[60201] = {
		id = 60201,
		drawType = 'limit_rmb1_16901',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		probEffectInterval = 10,
		lotteryType1 = {[22515] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60202] = {
		id = 60202,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 250,
		effectLimit = 1,
		startCount = 150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60203] = {
		id = 60203,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 400,
		effectLimit = 1,
		startCount = 300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60204] = {
		id = 60204,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 550,
		effectLimit = 1,
		startCount = 450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60205] = {
		id = 60205,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 700,
		effectLimit = 1,
		startCount = 600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60206] = {
		id = 60206,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 850,
		effectLimit = 1,
		startCount = 750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60207] = {
		id = 60207,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 1000,
		effectLimit = 1,
		startCount = 900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60208] = {
		id = 60208,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 1150,
		effectLimit = 1,
		startCount = 1050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60209] = {
		id = 60209,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 1300,
		effectLimit = 1,
		startCount = 1200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60210] = {
		id = 60210,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 1450,
		effectLimit = 1,
		startCount = 1350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60211] = {
		id = 60211,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 1600,
		effectLimit = 1,
		startCount = 1500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60212] = {
		id = 60212,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 1750,
		effectLimit = 1,
		startCount = 1650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60213] = {
		id = 60213,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 1900,
		effectLimit = 1,
		startCount = 1800,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60214] = {
		id = 60214,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 2050,
		effectLimit = 1,
		startCount = 1950,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60215] = {
		id = 60215,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 2200,
		effectLimit = 1,
		startCount = 2100,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60216] = {
		id = 60216,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 2350,
		effectLimit = 1,
		startCount = 2250,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60217] = {
		id = 60217,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 2500,
		effectLimit = 1,
		startCount = 2400,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60218] = {
		id = 60218,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 2650,
		effectLimit = 1,
		startCount = 2550,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60219] = {
		id = 60219,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 2800,
		effectLimit = 1,
		startCount = 2700,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60220] = {
		id = 60220,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 2950,
		effectLimit = 1,
		startCount = 2850,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60221] = {
		id = 60221,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 3100,
		effectLimit = 1,
		startCount = 3000,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60222] = {
		id = 60222,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 3250,
		effectLimit = 1,
		startCount = 3150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60223] = {
		id = 60223,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 3400,
		effectLimit = 1,
		startCount = 3300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60224] = {
		id = 60224,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 3550,
		effectLimit = 1,
		startCount = 3450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60225] = {
		id = 60225,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 3700,
		effectLimit = 1,
		startCount = 3600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60226] = {
		id = 60226,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 3850,
		effectLimit = 1,
		startCount = 3750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60227] = {
		id = 60227,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 4000,
		effectLimit = 1,
		startCount = 3900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60228] = {
		id = 60228,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 4150,
		effectLimit = 1,
		startCount = 4050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60229] = {
		id = 60229,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 4300,
		effectLimit = 1,
		startCount = 4200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60230] = {
		id = 60230,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 4450,
		effectLimit = 1,
		startCount = 4350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60231] = {
		id = 60231,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 4600,
		effectLimit = 1,
		startCount = 4500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60232] = {
		id = 60232,
		drawType = 'limit_rmb1_16901',
		drawTriggerTimes = 4750,
		effectLimit = 1,
		startCount = 4650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1186] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60233] = {
		id = 60233,
		drawType = 'limit_rmb1_16607',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[22519] = 1, __size = 1},
		lotteryType2 = {[22529] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1185] = 1, __size = 1},
		lotteryWeight4 = 150,
		lotteryType5 = {[1187] = 1, __size = 1},
		lotteryWeight5 = 1
	},
	[60234] = {
		id = 60234,
		drawType = 'limit_rmb1_16607',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		probEffectInterval = 10,
		lotteryType1 = {[22519] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60235] = {
		id = 60235,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 250,
		effectLimit = 1,
		startCount = 150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60236] = {
		id = 60236,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 400,
		effectLimit = 1,
		startCount = 300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60237] = {
		id = 60237,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 550,
		effectLimit = 1,
		startCount = 450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60238] = {
		id = 60238,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 700,
		effectLimit = 1,
		startCount = 600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60239] = {
		id = 60239,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 850,
		effectLimit = 1,
		startCount = 750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60240] = {
		id = 60240,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 1000,
		effectLimit = 1,
		startCount = 900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60241] = {
		id = 60241,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 1150,
		effectLimit = 1,
		startCount = 1050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60242] = {
		id = 60242,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 1300,
		effectLimit = 1,
		startCount = 1200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60243] = {
		id = 60243,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 1450,
		effectLimit = 1,
		startCount = 1350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60244] = {
		id = 60244,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 1600,
		effectLimit = 1,
		startCount = 1500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60245] = {
		id = 60245,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 1750,
		effectLimit = 1,
		startCount = 1650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60246] = {
		id = 60246,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 1900,
		effectLimit = 1,
		startCount = 1800,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60247] = {
		id = 60247,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 2050,
		effectLimit = 1,
		startCount = 1950,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60248] = {
		id = 60248,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 2200,
		effectLimit = 1,
		startCount = 2100,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60249] = {
		id = 60249,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 2350,
		effectLimit = 1,
		startCount = 2250,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60250] = {
		id = 60250,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 2500,
		effectLimit = 1,
		startCount = 2400,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60251] = {
		id = 60251,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 2650,
		effectLimit = 1,
		startCount = 2550,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60252] = {
		id = 60252,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 2800,
		effectLimit = 1,
		startCount = 2700,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60253] = {
		id = 60253,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 2950,
		effectLimit = 1,
		startCount = 2850,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60254] = {
		id = 60254,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 3100,
		effectLimit = 1,
		startCount = 3000,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60255] = {
		id = 60255,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 3250,
		effectLimit = 1,
		startCount = 3150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60256] = {
		id = 60256,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 3400,
		effectLimit = 1,
		startCount = 3300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60257] = {
		id = 60257,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 3550,
		effectLimit = 1,
		startCount = 3450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60258] = {
		id = 60258,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 3700,
		effectLimit = 1,
		startCount = 3600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60259] = {
		id = 60259,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 3850,
		effectLimit = 1,
		startCount = 3750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60260] = {
		id = 60260,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 4000,
		effectLimit = 1,
		startCount = 3900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60261] = {
		id = 60261,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 4150,
		effectLimit = 1,
		startCount = 4050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60262] = {
		id = 60262,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 4300,
		effectLimit = 1,
		startCount = 4200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60263] = {
		id = 60263,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 4450,
		effectLimit = 1,
		startCount = 4350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60264] = {
		id = 60264,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 4600,
		effectLimit = 1,
		startCount = 4500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60265] = {
		id = 60265,
		drawType = 'limit_rmb1_16607',
		drawTriggerTimes = 4750,
		effectLimit = 1,
		startCount = 4650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60266] = {
		id = 60266,
		drawType = 'limit_rmb1_16608',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[22512] = 1, __size = 1},
		lotteryType2 = {[1105] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1185] = 1, __size = 1},
		lotteryWeight4 = 150,
		lotteryType5 = {[1184] = 1, __size = 1},
		lotteryWeight5 = 1
	},
	[60267] = {
		id = 60267,
		drawType = 'limit_rmb1_16608',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		probEffectInterval = 10,
		lotteryType1 = {[22512] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60268] = {
		id = 60268,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 250,
		effectLimit = 1,
		startCount = 150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60269] = {
		id = 60269,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 400,
		effectLimit = 1,
		startCount = 300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60270] = {
		id = 60270,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 550,
		effectLimit = 1,
		startCount = 450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60271] = {
		id = 60271,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 700,
		effectLimit = 1,
		startCount = 600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60272] = {
		id = 60272,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 850,
		effectLimit = 1,
		startCount = 750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60273] = {
		id = 60273,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 1000,
		effectLimit = 1,
		startCount = 900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60274] = {
		id = 60274,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 1150,
		effectLimit = 1,
		startCount = 1050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60275] = {
		id = 60275,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 1300,
		effectLimit = 1,
		startCount = 1200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60276] = {
		id = 60276,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 1450,
		effectLimit = 1,
		startCount = 1350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60277] = {
		id = 60277,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 1600,
		effectLimit = 1,
		startCount = 1500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60278] = {
		id = 60278,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 1750,
		effectLimit = 1,
		startCount = 1650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60279] = {
		id = 60279,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 1900,
		effectLimit = 1,
		startCount = 1800,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60280] = {
		id = 60280,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 2050,
		effectLimit = 1,
		startCount = 1950,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60281] = {
		id = 60281,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 2200,
		effectLimit = 1,
		startCount = 2100,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60282] = {
		id = 60282,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 2350,
		effectLimit = 1,
		startCount = 2250,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60283] = {
		id = 60283,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 2500,
		effectLimit = 1,
		startCount = 2400,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60284] = {
		id = 60284,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 2650,
		effectLimit = 1,
		startCount = 2550,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60285] = {
		id = 60285,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 2800,
		effectLimit = 1,
		startCount = 2700,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60286] = {
		id = 60286,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 2950,
		effectLimit = 1,
		startCount = 2850,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60287] = {
		id = 60287,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 3100,
		effectLimit = 1,
		startCount = 3000,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60288] = {
		id = 60288,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 3250,
		effectLimit = 1,
		startCount = 3150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60289] = {
		id = 60289,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 3400,
		effectLimit = 1,
		startCount = 3300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60290] = {
		id = 60290,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 3550,
		effectLimit = 1,
		startCount = 3450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60291] = {
		id = 60291,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 3700,
		effectLimit = 1,
		startCount = 3600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60292] = {
		id = 60292,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 3850,
		effectLimit = 1,
		startCount = 3750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60293] = {
		id = 60293,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 4000,
		effectLimit = 1,
		startCount = 3900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60294] = {
		id = 60294,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 4150,
		effectLimit = 1,
		startCount = 4050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60295] = {
		id = 60295,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 4300,
		effectLimit = 1,
		startCount = 4200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60296] = {
		id = 60296,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 4450,
		effectLimit = 1,
		startCount = 4350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60297] = {
		id = 60297,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 4600,
		effectLimit = 1,
		startCount = 4500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60298] = {
		id = 60298,
		drawType = 'limit_rmb1_16608',
		drawTriggerTimes = 4750,
		effectLimit = 1,
		startCount = 4650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1184] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60299] = {
		id = 60299,
		drawType = 'limit_rmb1_16609',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[22531] = 1, __size = 1},
		lotteryType2 = {[22532] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1185] = 1, __size = 1},
		lotteryWeight4 = 150,
		lotteryType5 = {[1192] = 1, __size = 1},
		lotteryWeight5 = 1
	},
	[60300] = {
		id = 60300,
		drawType = 'limit_rmb1_16609',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		probEffectInterval = 10,
		lotteryType1 = {[22531] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60301] = {
		id = 60301,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 250,
		effectLimit = 1,
		startCount = 150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60302] = {
		id = 60302,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 400,
		effectLimit = 1,
		startCount = 300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60303] = {
		id = 60303,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 550,
		effectLimit = 1,
		startCount = 450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60304] = {
		id = 60304,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 700,
		effectLimit = 1,
		startCount = 600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60305] = {
		id = 60305,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 850,
		effectLimit = 1,
		startCount = 750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60306] = {
		id = 60306,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 1000,
		effectLimit = 1,
		startCount = 900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60307] = {
		id = 60307,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 1150,
		effectLimit = 1,
		startCount = 1050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60308] = {
		id = 60308,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 1300,
		effectLimit = 1,
		startCount = 1200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60309] = {
		id = 60309,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 1450,
		effectLimit = 1,
		startCount = 1350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60310] = {
		id = 60310,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 1600,
		effectLimit = 1,
		startCount = 1500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60311] = {
		id = 60311,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 1750,
		effectLimit = 1,
		startCount = 1650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60312] = {
		id = 60312,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 1900,
		effectLimit = 1,
		startCount = 1800,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60313] = {
		id = 60313,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 2050,
		effectLimit = 1,
		startCount = 1950,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60314] = {
		id = 60314,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 2200,
		effectLimit = 1,
		startCount = 2100,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60315] = {
		id = 60315,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 2350,
		effectLimit = 1,
		startCount = 2250,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60316] = {
		id = 60316,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 2500,
		effectLimit = 1,
		startCount = 2400,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60317] = {
		id = 60317,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 2650,
		effectLimit = 1,
		startCount = 2550,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60318] = {
		id = 60318,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 2800,
		effectLimit = 1,
		startCount = 2700,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60319] = {
		id = 60319,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 2950,
		effectLimit = 1,
		startCount = 2850,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60320] = {
		id = 60320,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 3100,
		effectLimit = 1,
		startCount = 3000,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60321] = {
		id = 60321,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 3250,
		effectLimit = 1,
		startCount = 3150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60322] = {
		id = 60322,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 3400,
		effectLimit = 1,
		startCount = 3300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60323] = {
		id = 60323,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 3550,
		effectLimit = 1,
		startCount = 3450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60324] = {
		id = 60324,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 3700,
		effectLimit = 1,
		startCount = 3600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60325] = {
		id = 60325,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 3850,
		effectLimit = 1,
		startCount = 3750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60326] = {
		id = 60326,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 4000,
		effectLimit = 1,
		startCount = 3900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60327] = {
		id = 60327,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 4150,
		effectLimit = 1,
		startCount = 4050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60328] = {
		id = 60328,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 4300,
		effectLimit = 1,
		startCount = 4200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60329] = {
		id = 60329,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 4450,
		effectLimit = 1,
		startCount = 4350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60330] = {
		id = 60330,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 4600,
		effectLimit = 1,
		startCount = 4500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60331] = {
		id = 60331,
		drawType = 'limit_rmb1_16609',
		drawTriggerTimes = 4750,
		effectLimit = 1,
		startCount = 4650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1192] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60332] = {
		id = 60332,
		drawType = 'limit_rmb1_16610',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[22519] = 1, __size = 1},
		lotteryType2 = {[22529] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1185] = 1, __size = 1},
		lotteryWeight4 = 150,
		lotteryType5 = {[1187] = 1, __size = 1},
		lotteryWeight5 = 1
	},
	[60333] = {
		id = 60333,
		drawType = 'limit_rmb1_16610',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		probEffectInterval = 10,
		lotteryType1 = {[22519] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60334] = {
		id = 60334,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 250,
		effectLimit = 1,
		startCount = 150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60335] = {
		id = 60335,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 400,
		effectLimit = 1,
		startCount = 300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60336] = {
		id = 60336,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 550,
		effectLimit = 1,
		startCount = 450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60337] = {
		id = 60337,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 700,
		effectLimit = 1,
		startCount = 600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60338] = {
		id = 60338,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 850,
		effectLimit = 1,
		startCount = 750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60339] = {
		id = 60339,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 1000,
		effectLimit = 1,
		startCount = 900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60340] = {
		id = 60340,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 1150,
		effectLimit = 1,
		startCount = 1050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60341] = {
		id = 60341,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 1300,
		effectLimit = 1,
		startCount = 1200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60342] = {
		id = 60342,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 1450,
		effectLimit = 1,
		startCount = 1350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60343] = {
		id = 60343,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 1600,
		effectLimit = 1,
		startCount = 1500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60344] = {
		id = 60344,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 1750,
		effectLimit = 1,
		startCount = 1650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60345] = {
		id = 60345,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 1900,
		effectLimit = 1,
		startCount = 1800,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60346] = {
		id = 60346,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 2050,
		effectLimit = 1,
		startCount = 1950,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60347] = {
		id = 60347,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 2200,
		effectLimit = 1,
		startCount = 2100,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60348] = {
		id = 60348,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 2350,
		effectLimit = 1,
		startCount = 2250,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60349] = {
		id = 60349,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 2500,
		effectLimit = 1,
		startCount = 2400,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60350] = {
		id = 60350,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 2650,
		effectLimit = 1,
		startCount = 2550,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60351] = {
		id = 60351,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 2800,
		effectLimit = 1,
		startCount = 2700,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60352] = {
		id = 60352,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 2950,
		effectLimit = 1,
		startCount = 2850,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60353] = {
		id = 60353,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 3100,
		effectLimit = 1,
		startCount = 3000,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60354] = {
		id = 60354,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 3250,
		effectLimit = 1,
		startCount = 3150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60355] = {
		id = 60355,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 3400,
		effectLimit = 1,
		startCount = 3300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60356] = {
		id = 60356,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 3550,
		effectLimit = 1,
		startCount = 3450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60357] = {
		id = 60357,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 3700,
		effectLimit = 1,
		startCount = 3600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60358] = {
		id = 60358,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 3850,
		effectLimit = 1,
		startCount = 3750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60359] = {
		id = 60359,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 4000,
		effectLimit = 1,
		startCount = 3900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60360] = {
		id = 60360,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 4150,
		effectLimit = 1,
		startCount = 4050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60361] = {
		id = 60361,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 4300,
		effectLimit = 1,
		startCount = 4200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60362] = {
		id = 60362,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 4450,
		effectLimit = 1,
		startCount = 4350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60363] = {
		id = 60363,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 4600,
		effectLimit = 1,
		startCount = 4500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60364] = {
		id = 60364,
		drawType = 'limit_rmb1_16610',
		drawTriggerTimes = 4750,
		effectLimit = 1,
		startCount = 4650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[1187] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60365] = {
		id = 60365,
		drawType = 'limit_rmb1_16611',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[22533] = 1, __size = 1},
		lotteryType2 = {[22534] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1185] = 1, __size = 1},
		lotteryWeight4 = 150,
		lotteryType5 = {[22535] = 1, __size = 1},
		lotteryWeight5 = 1
	},
	[60366] = {
		id = 60366,
		drawType = 'limit_rmb1_16611',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		probEffectInterval = 10,
		lotteryType1 = {[22533] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60367] = {
		id = 60367,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 250,
		effectLimit = 1,
		startCount = 150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60368] = {
		id = 60368,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 400,
		effectLimit = 1,
		startCount = 300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60369] = {
		id = 60369,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 550,
		effectLimit = 1,
		startCount = 450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60370] = {
		id = 60370,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 700,
		effectLimit = 1,
		startCount = 600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60371] = {
		id = 60371,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 850,
		effectLimit = 1,
		startCount = 750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60372] = {
		id = 60372,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 1000,
		effectLimit = 1,
		startCount = 900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60373] = {
		id = 60373,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 1150,
		effectLimit = 1,
		startCount = 1050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60374] = {
		id = 60374,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 1300,
		effectLimit = 1,
		startCount = 1200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60375] = {
		id = 60375,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 1450,
		effectLimit = 1,
		startCount = 1350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60376] = {
		id = 60376,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 1600,
		effectLimit = 1,
		startCount = 1500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60377] = {
		id = 60377,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 1750,
		effectLimit = 1,
		startCount = 1650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60378] = {
		id = 60378,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 1900,
		effectLimit = 1,
		startCount = 1800,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60379] = {
		id = 60379,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 2050,
		effectLimit = 1,
		startCount = 1950,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60380] = {
		id = 60380,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 2200,
		effectLimit = 1,
		startCount = 2100,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60381] = {
		id = 60381,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 2350,
		effectLimit = 1,
		startCount = 2250,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60382] = {
		id = 60382,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 2500,
		effectLimit = 1,
		startCount = 2400,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60383] = {
		id = 60383,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 2650,
		effectLimit = 1,
		startCount = 2550,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60384] = {
		id = 60384,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 2800,
		effectLimit = 1,
		startCount = 2700,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60385] = {
		id = 60385,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 2950,
		effectLimit = 1,
		startCount = 2850,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60386] = {
		id = 60386,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 3100,
		effectLimit = 1,
		startCount = 3000,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60387] = {
		id = 60387,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 3250,
		effectLimit = 1,
		startCount = 3150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60388] = {
		id = 60388,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 3400,
		effectLimit = 1,
		startCount = 3300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60389] = {
		id = 60389,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 3550,
		effectLimit = 1,
		startCount = 3450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60390] = {
		id = 60390,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 3700,
		effectLimit = 1,
		startCount = 3600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60391] = {
		id = 60391,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 3850,
		effectLimit = 1,
		startCount = 3750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60392] = {
		id = 60392,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 4000,
		effectLimit = 1,
		startCount = 3900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60393] = {
		id = 60393,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 4150,
		effectLimit = 1,
		startCount = 4050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60394] = {
		id = 60394,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 4300,
		effectLimit = 1,
		startCount = 4200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60395] = {
		id = 60395,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 4450,
		effectLimit = 1,
		startCount = 4350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60396] = {
		id = 60396,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 4600,
		effectLimit = 1,
		startCount = 4500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60397] = {
		id = 60397,
		drawType = 'limit_rmb1_16611',
		drawTriggerTimes = 4750,
		effectLimit = 1,
		startCount = 4650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22535] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60398] = {
		id = 60398,
		drawType = 'limit_rmb1_16612',
		drawTriggerType = 0,
		drawTriggerTimes = 1,
		lotteryType1 = {[22536] = 1, __size = 1},
		lotteryType2 = {[22537] = 1, __size = 1},
		lotteryWeight2 = 200,
		lotteryType3 = {[1107] = 1, __size = 1},
		lotteryWeight3 = 625,
		lotteryType4 = {[1185] = 1, __size = 1},
		lotteryWeight4 = 150,
		lotteryType5 = {[22538] = 1, __size = 1},
		lotteryWeight5 = 1
	},
	[60399] = {
		id = 60399,
		drawType = 'limit_rmb1_16612',
		drawTriggerType = 3,
		drawTriggerTimes = 10,
		probEffectInterval = 10,
		lotteryType1 = {[22536] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60400] = {
		id = 60400,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 250,
		effectLimit = 1,
		startCount = 150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60401] = {
		id = 60401,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 400,
		effectLimit = 1,
		startCount = 300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60402] = {
		id = 60402,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 550,
		effectLimit = 1,
		startCount = 450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60403] = {
		id = 60403,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 700,
		effectLimit = 1,
		startCount = 600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60404] = {
		id = 60404,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 850,
		effectLimit = 1,
		startCount = 750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60405] = {
		id = 60405,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 1000,
		effectLimit = 1,
		startCount = 900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60406] = {
		id = 60406,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 1150,
		effectLimit = 1,
		startCount = 1050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60407] = {
		id = 60407,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 1300,
		effectLimit = 1,
		startCount = 1200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60408] = {
		id = 60408,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 1450,
		effectLimit = 1,
		startCount = 1350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60409] = {
		id = 60409,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 1600,
		effectLimit = 1,
		startCount = 1500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60410] = {
		id = 60410,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 1750,
		effectLimit = 1,
		startCount = 1650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60411] = {
		id = 60411,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 1900,
		effectLimit = 1,
		startCount = 1800,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60412] = {
		id = 60412,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 2050,
		effectLimit = 1,
		startCount = 1950,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60413] = {
		id = 60413,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 2200,
		effectLimit = 1,
		startCount = 2100,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60414] = {
		id = 60414,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 2350,
		effectLimit = 1,
		startCount = 2250,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60415] = {
		id = 60415,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 2500,
		effectLimit = 1,
		startCount = 2400,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60416] = {
		id = 60416,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 2650,
		effectLimit = 1,
		startCount = 2550,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60417] = {
		id = 60417,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 2800,
		effectLimit = 1,
		startCount = 2700,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60418] = {
		id = 60418,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 2950,
		effectLimit = 1,
		startCount = 2850,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60419] = {
		id = 60419,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 3100,
		effectLimit = 1,
		startCount = 3000,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60420] = {
		id = 60420,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 3250,
		effectLimit = 1,
		startCount = 3150,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60421] = {
		id = 60421,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 3400,
		effectLimit = 1,
		startCount = 3300,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60422] = {
		id = 60422,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 3550,
		effectLimit = 1,
		startCount = 3450,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60423] = {
		id = 60423,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 3700,
		effectLimit = 1,
		startCount = 3600,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60424] = {
		id = 60424,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 3850,
		effectLimit = 1,
		startCount = 3750,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60425] = {
		id = 60425,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 4000,
		effectLimit = 1,
		startCount = 3900,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60426] = {
		id = 60426,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 4150,
		effectLimit = 1,
		startCount = 4050,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60427] = {
		id = 60427,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 4300,
		effectLimit = 1,
		startCount = 4200,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60428] = {
		id = 60428,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 4450,
		effectLimit = 1,
		startCount = 4350,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60429] = {
		id = 60429,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 4600,
		effectLimit = 1,
		startCount = 4500,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60430] = {
		id = 60430,
		drawType = 'limit_rmb1_16612',
		drawTriggerTimes = 4750,
		effectLimit = 1,
		startCount = 4650,
		probInit = 0.1,
		probStep = 0.9,
		probLimit = 1.0,
		probMiniTimes = 99,
		probEffectInterval = 99999,
		lotteryType1 = {[22538] = 1, __size = 1},
		lotteryWeight1 = 1
	},
	[60533] = {
		id = 60533
	},
	[60534] = {
		id = 60534
	},
	[60535] = {
		id = 60535
	},
	[60536] = {
		id = 60536
	},
	[60537] = {
		id = 60537
	},
	[60538] = {
		id = 60538
	},
	[60539] = {
		id = 60539
	},
	[60540] = {
		id = 60540
	},
	[60541] = {
		id = 60541
	},
	[60542] = {
		id = 60542
	},
	[60543] = {
		id = 60543
	},
	[60544] = {
		id = 60544
	},
	[60545] = {
		id = 60545
	},
	[60546] = {
		id = 60546
	},
	[60547] = {
		id = 60547
	},
	[60548] = {
		id = 60548
	},
	[60549] = {
		id = 60549
	},
	[60550] = {
		id = 60550
	},
	[60551] = {
		id = 60551
	},
	[60552] = {
		id = 60552
	},
	[60553] = {
		id = 60553
	},
	[60554] = {
		id = 60554
	},
	[60555] = {
		id = 60555
	},

	__size = 1067,
	__default = {
		__index = {
			drawTriggerType = 2,
			drawTriggerTimes = 0,
			weightStart = 0,
			weightEnd = 0,
			effectLimit = 0,
			startCount = 0,
			probInit = 0,
			probStep = 0.01,
			probLimit = 0.2,
			probMiniTimes = 30,
			probEffectInterval = 50,
			lotteryType1 = {__size = 0},
			lotteryWeight1 = 0,
			lotteryType2 = {__size = 0},
			lotteryWeight2 = 0,
			lotteryType3 = {__size = 0},
			lotteryWeight3 = 0,
			lotteryType4 = {__size = 0},
			lotteryWeight4 = 0,
			lotteryType5 = {__size = 0},
			lotteryWeight5 = 0,
			lotteryType6 = {__size = 0},
			lotteryWeight6 = 0,
			lotteryType7 = {__size = 0},
			lotteryWeight7 = 0,
			lotteryType8 = {__size = 0},
			lotteryWeight8 = 0,
			lotteryType9 = {__size = 0},
			lotteryWeight9 = 0,
			lotteryType10 = {__size = 0},
			lotteryWeight10 = 0
		}
	}
}
return csv.draw_card