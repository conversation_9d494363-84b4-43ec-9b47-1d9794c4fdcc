<?xml version="1.0" encoding="UTF-8"?>
<data version="1.0">
    <struct type="Settings">
        <key>fileFormatVersion</key>
        <int>6</int>
        <key>texturePackerVersion</key>
        <string>7.0.3</string>
        <key>autoSDSettings</key>
        <array>
            <struct type="AutoSDSettings">
                <key>scale</key>
                <double>1</double>
                <key>extension</key>
                <string></string>
                <key>spriteFilter</key>
                <string></string>
                <key>acceptFractionalValues</key>
                <false/>
                <key>maxTextureSize</key>
                <QSize>
                    <key>width</key>
                    <int>-1</int>
                    <key>height</key>
                    <int>-1</int>
                </QSize>
            </struct>
        </array>
        <key>allowRotation</key>
        <true/>
        <key>shapeDebug</key>
        <false/>
        <key>dpi</key>
        <uint>72</uint>
        <key>dataFormat</key>
        <string>cocos2d</string>
        <key>textureFileName</key>
        <filename>txts0.pvr.ccz</filename>
        <key>flipPVR</key>
        <false/>
        <key>pvrQualityLevel</key>
        <uint>3</uint>
        <key>astcQualityLevel</key>
        <uint>2</uint>
        <key>basisUniversalQualityLevel</key>
        <uint>2</uint>
        <key>etc1QualityLevel</key>
        <uint>40</uint>
        <key>etc2QualityLevel</key>
        <uint>40</uint>
        <key>dxtCompressionMode</key>
        <enum type="SettingsBase::DxtCompressionMode">DXT_PERCEPTUAL</enum>
        <key>ditherType</key>
        <enum type="SettingsBase::DitherType">NearestNeighbour</enum>
        <key>backgroundColor</key>
        <uint>0</uint>
        <key>libGdx</key>
        <struct type="LibGDX">
            <key>filtering</key>
            <struct type="LibGDXFiltering">
                <key>x</key>
                <enum type="LibGDXFiltering::Filtering">Linear</enum>
                <key>y</key>
                <enum type="LibGDXFiltering::Filtering">Linear</enum>
            </struct>
        </struct>
        <key>shapePadding</key>
        <uint>1</uint>
        <key>jpgQuality</key>
        <uint>80</uint>
        <key>pngOptimizationLevel</key>
        <uint>0</uint>
        <key>webpQualityLevel</key>
        <uint>101</uint>
        <key>textureSubPath</key>
        <string></string>
        <key>textureFormat</key>
        <enum type="SettingsBase::TextureFormat">pvr3ccz</enum>
        <key>borderPadding</key>
        <uint>1</uint>
        <key>maxTextureSize</key>
        <QSize>
            <key>width</key>
            <int>2048</int>
            <key>height</key>
            <int>2048</int>
        </QSize>
        <key>fixedTextureSize</key>
        <QSize>
            <key>width</key>
            <int>-1</int>
            <key>height</key>
            <int>-1</int>
        </QSize>
        <key>algorithmSettings</key>
        <struct type="AlgorithmSettings">
            <key>algorithm</key>
            <enum type="AlgorithmSettings::AlgorithmId">MaxRects</enum>
            <key>freeSizeMode</key>
            <enum type="AlgorithmSettings::AlgorithmFreeSizeMode">Best</enum>
            <key>sizeConstraints</key>
            <enum type="AlgorithmSettings::SizeConstraints">WordAligned</enum>
            <key>forceSquared</key>
            <false/>
            <key>maxRects</key>
            <struct type="AlgorithmMaxRectsSettings">
                <key>heuristic</key>
                <enum type="AlgorithmMaxRectsSettings::Heuristic">Best</enum>
            </struct>
            <key>basic</key>
            <struct type="AlgorithmBasicSettings">
                <key>sortBy</key>
                <enum type="AlgorithmBasicSettings::SortBy">Best</enum>
                <key>order</key>
                <enum type="AlgorithmBasicSettings::Order">Ascending</enum>
            </struct>
            <key>polygon</key>
            <struct type="AlgorithmPolygonSettings">
                <key>alignToGrid</key>
                <uint>1</uint>
            </struct>
        </struct>
        <key>dataFileNames</key>
        <map type="GFileNameMap">
            <key>data</key>
            <struct type="DataFile">
                <key>name</key>
                <filename>txts0.plist</filename>
            </struct>
        </map>
        <key>multiPackMode</key>
        <enum type="SettingsBase::MultiPackMode">MultiPackOff</enum>
        <key>forceIdenticalLayout</key>
        <false/>
        <key>outputFormat</key>
        <enum type="SettingsBase::OutputFormat">RGBA8888</enum>
        <key>alphaHandling</key>
        <enum type="SettingsBase::AlphaHandling">PremultiplyAlpha</enum>
        <key>contentProtection</key>
        <struct type="ContentProtection">
            <key>key</key>
            <string></string>
        </struct>
        <key>autoAliasEnabled</key>
        <true/>
        <key>trimSpriteNames</key>
        <false/>
        <key>prependSmartFolderName</key>
        <false/>
        <key>autodetectAnimations</key>
        <true/>
        <key>globalSpriteSettings</key>
        <struct type="SpriteSettings">
            <key>scale</key>
            <double>0.5</double>
            <key>scaleMode</key>
            <enum type="ScaleMode">Smooth</enum>
            <key>extrude</key>
            <uint>0</uint>
            <key>trimThreshold</key>
            <uint>1</uint>
            <key>trimMargin</key>
            <uint>1</uint>
            <key>trimMode</key>
            <enum type="SpriteSettings::TrimMode">Trim</enum>
            <key>tracerTolerance</key>
            <int>200</int>
            <key>heuristicMask</key>
            <false/>
            <key>defaultPivotPoint</key>
            <point_f>0.5,0.5</point_f>
            <key>writePivotPoints</key>
            <false/>
        </struct>
        <key>individualSpriteSettings</key>
        <map type="IndividualSpriteSettingsMap">
            <key type="filename">bg_bj_di.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>85,34,169,67</rect>
                <key>scale9Paddings</key>
                <rect>85,34,169,67</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">bg_kz_di.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>39,17,77,33</rect>
                <key>scale9Paddings</key>
                <rect>39,17,77,33</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">bg_nqjl_di.png</key>
            <key type="filename">bg_ptsh_di.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>38,16,75,32</rect>
                <key>scale9Paddings</key>
                <rect>38,16,75,32</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">bg_zlsz_di.png</key>
            <key type="filename">txt_zsh.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>196,47,391,93</rect>
                <key>scale9Paddings</key>
                <rect>196,47,391,93</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">bg_zzl_di.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>100,27,201,54</rect>
                <key>scale9Paddings</key>
                <rect>100,27,201,54</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">tat_zy.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>75,22,151,43</rect>
                <key>scale9Paddings</key>
                <rect>75,22,151,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>64,22,129,43</rect>
                <key>scale9Paddings</key>
                <rect>64,22,129,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bdqdtgpng.png</key>
            <key type="filename">txt_gy.png</key>
            <key type="filename">txt_swyj.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>53,22,105,43</rect>
                <key>scale9Paddings</key>
                <rect>53,22,105,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bfx.png</key>
            <key type="filename">txt_fs1.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>66,25,131,49</rect>
                <key>scale9Paddings</key>
                <rect>66,25,131,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bh.png</key>
            <key type="filename">txt_smlj.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>65,25,129,49</rect>
                <key>scale9Paddings</key>
                <rect>65,25,129,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bj.png</key>
            <key type="filename">txt_hbxj.png</key>
            <key type="filename">txt_sb.png</key>
            <key type="filename">txt_shzy.png</key>
            <key type="filename">txt_ys.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>57,22,113,43</rect>
                <key>scale9Paddings</key>
                <rect>57,22,113,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bjkxjd.png</key>
            <key type="filename">txt_bskxjd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>92,22,184,43</rect>
                <key>scale9Paddings</key>
                <rect>92,22,184,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bjkxtg.png</key>
            <key type="filename">txt_bskxtg.png</key>
            <key type="filename">txt_shtg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>91,22,182,43</rect>
                <key>scale9Paddings</key>
                <rect>91,22,182,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bjljd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>82,22,164,43</rect>
                <key>scale9Paddings</key>
                <rect>82,22,164,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bjltg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>85,22,171,43</rect>
                <key>scale9Paddings</key>
                <rect>85,22,171,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bjshjd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>77,22,153,43</rect>
                <key>scale9Paddings</key>
                <rect>77,22,153,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bjshtg.png</key>
            <key type="filename">txt_bsshtg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>78,22,155,43</rect>
                <key>scale9Paddings</key>
                <rect>78,22,155,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bl.png</key>
            <key type="filename">txt_dq.png</key>
            <key type="filename">txt_pz.png</key>
            <key type="filename">txt_tb.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>48,25,95,49</rect>
                <key>scale9Paddings</key>
                <rect>48,25,95,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bn.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>45,22,89,43</rect>
                <key>scale9Paddings</key>
                <rect>45,22,89,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bq.png</key>
            <key type="filename">txt_gd1.png</key>
            <key type="filename">txt_rh.png</key>
            <key type="filename">txt_wsjd.png</key>
            <key type="filename">txt_zs1.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>60,22,120,43</rect>
                <key>scale9Paddings</key>
                <rect>60,22,120,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bqzz.png</key>
            <key type="filename">txt_jsym.png</key>
            <key type="filename">txt_tcdk.png</key>
            <key type="filename">txt_wjph.png</key>
            <key type="filename">txt_xrzt.png</key>
            <key type="filename">txt_ylxf.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>132,25,264,49</rect>
                <key>scale9Paddings</key>
                <rect>132,25,264,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bsshjd.png</key>
            <key type="filename">txt_tftg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>74,22,147,43</rect>
                <key>scale9Paddings</key>
                <rect>74,22,147,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bsxys.png</key>
            <key type="filename">txt_dsxys.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>103,22,206,43</rect>
                <key>scale9Paddings</key>
                <rect>103,22,206,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bxys.png</key>
            <key type="filename">txt_dxys.png</key>
            <key type="filename">txt_hxys.png</key>
            <key type="filename">txt_sxys.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>88,22,175,43</rect>
                <key>scale9Paddings</key>
                <rect>88,22,175,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_by.png</key>
            <key type="filename">txt_fx.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>84,25,167,49</rect>
                <key>scale9Paddings</key>
                <rect>84,25,167,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bz.png</key>
            <key type="filename">txt_cr.png</key>
            <key type="filename">txt_cyzt.png</key>
            <key type="filename">txt_fdzs.png</key>
            <key type="filename">txt_fh.png</key>
            <key type="filename">txt_hj.png</key>
            <key type="filename">txt_hl1.png</key>
            <key type="filename">txt_hp.png</key>
            <key type="filename">txt_hs.png</key>
            <key type="filename">txt_jc.png</key>
            <key type="filename">txt_jdx.png</key>
            <key type="filename">txt_jw.png</key>
            <key type="filename">txt_jyqh.png</key>
            <key type="filename">txt_kj.png</key>
            <key type="filename">txt_lb.png</key>
            <key type="filename">txt_lx2.png</key>
            <key type="filename">txt_lx3.png</key>
            <key type="filename">txt_ly1.png</key>
            <key type="filename">txt_mkl.png</key>
            <key type="filename">txt_my2.png</key>
            <key type="filename">txt_ny.png</key>
            <key type="filename">txt_qh.png</key>
            <key type="filename">txt_sd1.png</key>
            <key type="filename">txt_sg.png</key>
            <key type="filename">txt_sh.png</key>
            <key type="filename">txt_snzf.png</key>
            <key type="filename">txt_sx1.png</key>
            <key type="filename">txt_tg.png</key>
            <key type="filename">txt_ts.png</key>
            <key type="filename">txt_xb.png</key>
            <key type="filename">txt_xfzr.png</key>
            <key type="filename">txt_xs.png</key>
            <key type="filename">txt_xx2.png</key>
            <key type="filename">txt_yl.png</key>
            <key type="filename">txt_yqtc.png</key>
            <key type="filename">txt_ys1.png</key>
            <key type="filename">txt_zh1.png</key>
            <key type="filename">txt_zh3.png</key>
            <key type="filename">txt_zz.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>56,25,111,49</rect>
                <key>scale9Paddings</key>
                <rect>56,25,111,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_bzxt.png</key>
            <key type="filename">txt_fz.png</key>
            <key type="filename">txt_gd.png</key>
            <key type="filename">txt_hf.png</key>
            <key type="filename">txt_zy.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>55,22,110,43</rect>
                <key>scale9Paddings</key>
                <rect>55,22,110,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_ccg.png</key>
            <key type="filename">txt_ch.png</key>
            <key type="filename">txt_ewxd.png</key>
            <key type="filename">txt_fz1.png</key>
            <key type="filename">txt_hx.png</key>
            <key type="filename">txt_jdc.png</key>
            <key type="filename">txt_jl.png</key>
            <key type="filename">txt_jt.png</key>
            <key type="filename">txt_jyrh.png</key>
            <key type="filename">txt_ldfx.png</key>
            <key type="filename">txt_ls1.png</key>
            <key type="filename">txt_lsg.png</key>
            <key type="filename">txt_ly.png</key>
            <key type="filename">txt_lyzl.png</key>
            <key type="filename">txt_lz.png</key>
            <key type="filename">txt_mh2.png</key>
            <key type="filename">txt_mht.png</key>
            <key type="filename">txt_mysh.png</key>
            <key type="filename">txt_mysw.png</key>
            <key type="filename">txt_mzg.png</key>
            <key type="filename">txt_pljhz.png</key>
            <key type="filename">txt_qlms.png</key>
            <key type="filename">txt_qy1.png</key>
            <key type="filename">txt_rdy.png</key>
            <key type="filename">txt_sh2.png</key>
            <key type="filename">txt_shzs.png</key>
            <key type="filename">txt_sl2.png</key>
            <key type="filename">txt_spp.png</key>
            <key type="filename">txt_srm.png</key>
            <key type="filename">txt_tgshmy.png</key>
            <key type="filename">txt_wgshmy.png</key>
            <key type="filename">txt_wjsl.png</key>
            <key type="filename">txt_wshd.png</key>
            <key type="filename">txt_xf.png</key>
            <key type="filename">txt_xsnq.png</key>
            <key type="filename">txt_yh1.png</key>
            <key type="filename">txt_yl2.png</key>
            <key type="filename">txt_ym.png</key>
            <key type="filename">txt_yqw.png</key>
            <key type="filename">txt_yszt.png</key>
            <key type="filename">txt_yys.png</key>
            <key type="filename">txt_zlg.png</key>
            <key type="filename">txt_zy2.png</key>
            <key type="filename">txt_zyjh.png</key>
            <key type="filename">txt_zyqh.png</key>
            <key type="filename">txt_zyrh.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>91,25,182,49</rect>
                <key>scale9Paddings</key>
                <rect>91,25,182,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_cd.png</key>
            <key type="filename">txt_dxsh.png</key>
            <key type="filename">txt_fysl.png</key>
            <key type="filename">txt_hyzl.png</key>
            <key type="filename">txt_jbzl.png</key>
            <key type="filename">txt_ls3.png</key>
            <key type="filename">txt_myzm.png</key>
            <key type="filename">txt_nlfs.png</key>
            <key type="filename">txt_slcf.png</key>
            <key type="filename">txt_slhz.png</key>
            <key type="filename">txt_sslj.png</key>
            <key type="filename">txt_sxkzts.png</key>
            <key type="filename">txt_syfs.png</key>
            <key type="filename">txt_sylx.png</key>
            <key type="filename">txt_wqjh.png</key>
            <key type="filename">txt_yj.png</key>
            <key type="filename">txt_ylht.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>107,22,213,43</rect>
                <key>scale9Paddings</key>
                <rect>107,22,213,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_cf.png</key>
            <key type="filename">txt_lx.png</key>
            <key type="filename">txt_nqsxtg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>68,22,135,43</rect>
                <key>scale9Paddings</key>
                <rect>68,22,135,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_cf1.png</key>
            <key type="filename">txt_te.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>53,22,107,43</rect>
                <key>scale9Paddings</key>
                <rect>53,22,107,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_chzf.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>99,25,197,49</rect>
                <key>scale9Paddings</key>
                <rect>99,25,197,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_cm.png</key>
            <key type="filename">txt_fj.png</key>
            <key type="filename">txt_xz1.png</key>
            <key type="filename">txt_zf.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>62,22,123,43</rect>
                <key>scale9Paddings</key>
                <rect>62,22,123,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_cpms.png</key>
            <key type="filename">txt_jl1.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>47,22,95,43</rect>
                <key>scale9Paddings</key>
                <rect>47,22,95,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_cs.png</key>
            <key type="filename">txt_mybd.png</key>
            <key type="filename">txt_myfm.png</key>
            <key type="filename">txt_myzd.png</key>
            <key type="filename">txt_myzs.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>105,25,210,49</rect>
                <key>scale9Paddings</key>
                <rect>105,25,210,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_cs1.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>64,25,127,49</rect>
                <key>scale9Paddings</key>
                <rect>64,25,127,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_cszj.png</key>
            <key type="filename">txt_dlcd.png</key>
            <key type="filename">txt_dzmw.png</key>
            <key type="filename">txt_fdh.png</key>
            <key type="filename">txt_fryd.png</key>
            <key type="filename">txt_gsys.png</key>
            <key type="filename">txt_hjzd.png</key>
            <key type="filename">txt_hszz.png</key>
            <key type="filename">txt_hypz.png</key>
            <key type="filename">txt_jgzd.png</key>
            <key type="filename">txt_jyhldj.png</key>
            <key type="filename">txt_lyswzy.png</key>
            <key type="filename">txt_mysh1.png</key>
            <key type="filename">txt_phzj.png</key>
            <key type="filename">txt_rydz.png</key>
            <key type="filename">txt_shjj.png</key>
            <key type="filename">txt_sjll.png</key>
            <key type="filename">txt_skhs.png</key>
            <key type="filename">txt_wbxt.png</key>
            <key type="filename">txt_wszl.png</key>
            <key type="filename">txt_xzge.png</key>
            <key type="filename">txt_ycydj.png</key>
            <key type="filename">txt_zdh.png</key>
            <key type="filename">txt_zhcs.png</key>
            <key type="filename">txt_zhdqwft.png</key>
            <key type="filename">txt_zhfd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>114,25,227,49</rect>
                <key>scale9Paddings</key>
                <rect>114,25,227,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_cxy.png</key>
            <key type="filename">txt_nbzd.png</key>
            <key type="filename">txt_xl.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>54,22,108,43</rect>
                <key>scale9Paddings</key>
                <rect>54,22,108,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_dcxj.png</key>
            <key type="filename">txt_ljhs.png</key>
            <key type="filename">txt_lyk.png</key>
            <key type="filename">txt_mhxj.png</key>
            <key type="filename">txt_qr.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>77,25,153,49</rect>
                <key>scale9Paddings</key>
                <rect>77,25,153,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_ddfy.png</key>
            <key type="filename">txt_ldxt.png</key>
            <key type="filename">txt_ryzt.png</key>
            <key type="filename">txt_sjjx.png</key>
            <key type="filename">txt_tkxt.png</key>
            <key type="filename">txt_xyx.png</key>
            <key type="filename">txt_ymgh.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>78,25,155,49</rect>
                <key>scale9Paddings</key>
                <rect>78,25,155,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_df.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>98,22,197,43</rect>
                <key>scale9Paddings</key>
                <rect>98,22,197,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_dh.png</key>
            <key type="filename">txt_jf.png</key>
            <key type="filename">txt_tl.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>62,25,123,49</rect>
                <key>scale9Paddings</key>
                <rect>62,25,123,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_dlxj.png</key>
            <key type="filename">txt_gjyd.png</key>
            <key type="filename">txt_gtfc.png</key>
            <key type="filename">txt_hxrh.png</key>
            <key type="filename">txt_pmhd.png</key>
            <key type="filename">txt_tjzz.png</key>
            <key type="filename">txt_ylzh.png</key>
            <key type="filename">txt_zyjz.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>102,25,204,49</rect>
                <key>scale9Paddings</key>
                <rect>102,25,204,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_dyljd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>83,22,167,43</rect>
                <key>scale9Paddings</key>
                <rect>83,22,167,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_dyltg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>84,22,168,43</rect>
                <key>scale9Paddings</key>
                <rect>84,22,168,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_dyqdjd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>57,22,114,43</rect>
                <key>scale9Paddings</key>
                <rect>57,22,114,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_dyqdtg.png</key>
            <key type="filename">txt_zjzt.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>54,22,107,43</rect>
                <key>scale9Paddings</key>
                <rect>54,22,107,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_emyj.png</key>
            <key type="filename">txt_ylxw.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>112,20,224,40</rect>
                <key>scale9Paddings</key>
                <rect>112,20,224,40</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_fd.png</key>
            <key type="filename">txt_gm1.png</key>
            <key type="filename">txt_gthd.png</key>
            <key type="filename">txt_jy.png</key>
            <key type="filename">txt_lj.png</key>
            <key type="filename">txt_my1.png</key>
            <key type="filename">txt_slhr.png</key>
            <key type="filename">txt_tw.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>87,25,175,49</rect>
                <key>scale9Paddings</key>
                <rect>87,25,175,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_fmdkljd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>98,22,195,43</rect>
                <key>scale9Paddings</key>
                <rect>98,22,195,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_fmdkltg.png</key>
            <key type="filename">txt_nqhs.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>97,22,195,43</rect>
                <key>scale9Paddings</key>
                <rect>97,22,195,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_fn.png</key>
            <key type="filename">txt_rdlj.png</key>
            <key type="filename">txt_yh.png</key>
            <key type="filename">txt_zm.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>54,22,109,43</rect>
                <key>scale9Paddings</key>
                <rect>54,22,109,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_fs.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>58,22,115,43</rect>
                <key>scale9Paddings</key>
                <rect>58,22,115,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_fsms.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>82,22,163,43</rect>
                <key>scale9Paddings</key>
                <rect>82,22,163,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_fzxj.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>98,25,195,49</rect>
                <key>scale9Paddings</key>
                <rect>98,25,195,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_gd2.png</key>
            <key type="filename">txt_hd.png</key>
            <key type="filename">txt_qxdh.png</key>
            <key type="filename">txt_qyhq.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>58,22,117,43</rect>
                <key>scale9Paddings</key>
                <rect>58,22,117,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_gl.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>83,25,165,49</rect>
                <key>scale9Paddings</key>
                <rect>83,25,165,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_gsxt.png</key>
            <key type="filename">txt_sjbd.png</key>
            <key type="filename">txt_sjky.png</key>
            <key type="filename">txt_smzz.png</key>
            <key type="filename">txt_swlj.png</key>
            <key type="filename">txt_szjh.png</key>
            <key type="filename">txt_wfdt.png</key>
            <key type="filename">txt_zzzd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>80,25,159,49</rect>
                <key>scale9Paddings</key>
                <rect>80,25,159,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_gthd2.png</key>
            <key type="filename">txt_gtsp.png</key>
            <key type="filename">txt_lj1.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>81,25,161,49</rect>
                <key>scale9Paddings</key>
                <rect>81,25,161,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_hbhd.png</key>
            <key type="filename">txt_hl.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>67,22,134,43</rect>
                <key>scale9Paddings</key>
                <rect>67,22,134,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_hc.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>71,25,143,49</rect>
                <key>scale9Paddings</key>
                <rect>71,25,143,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_hh.png</key>
            <key type="filename">txt_ytjs.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>92,25,184,49</rect>
                <key>scale9Paddings</key>
                <rect>92,25,184,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_hjsh.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>56,22,111,43</rect>
                <key>scale9Paddings</key>
                <rect>56,22,111,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_hl2.png</key>
            <key type="filename">txt_jd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>43,25,85,49</rect>
                <key>scale9Paddings</key>
                <rect>43,25,85,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_hm.png</key>
            <key type="filename">txt_smsh.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>97,25,194,49</rect>
                <key>scale9Paddings</key>
                <rect>97,25,194,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_hnsh.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>96,25,193,49</rect>
                <key>scale9Paddings</key>
                <rect>96,25,193,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_hs1.png</key>
            <key type="filename">txt_nqjl.png</key>
            <key type="filename">txt_wfjd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>64,22,127,43</rect>
                <key>scale9Paddings</key>
                <rect>64,22,127,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_hxcf.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>111,22,221,43</rect>
                <key>scale9Paddings</key>
                <rect>111,22,221,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_hyms.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>86,25,173,49</rect>
                <key>scale9Paddings</key>
                <rect>86,25,173,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_hymz.png</key>
            <key type="filename">txt_ldhy.png</key>
            <key type="filename">txt_lsjj.png</key>
            <key type="filename">txt_lssz.png</key>
            <key type="filename">txt_mynqjd.png</key>
            <key type="filename">txt_rdyq.png</key>
            <key type="filename">txt_scyj.png</key>
            <key type="filename">txt_zhcjb.png</key>
            <key type="filename">txt_zhhcl.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>123,22,245,43</rect>
                <key>scale9Paddings</key>
                <rect>123,22,245,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_jh.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>63,25,127,49</rect>
                <key>scale9Paddings</key>
                <rect>63,25,127,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_jl2.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>101,22,201,43</rect>
                <key>scale9Paddings</key>
                <rect>101,22,201,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_jszz.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>63,22,127,43</rect>
                <key>scale9Paddings</key>
                <rect>63,22,127,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_jwzt.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>99,25,198,49</rect>
                <key>scale9Paddings</key>
                <rect>99,25,198,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_jydkljd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>71,22,142,43</rect>
                <key>scale9Paddings</key>
                <rect>71,22,142,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_jydkltg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>100,22,201,43</rect>
                <key>scale9Paddings</key>
                <rect>100,22,201,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_kn.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>37,25,74,49</rect>
                <key>scale9Paddings</key>
                <rect>37,25,74,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_kzljd.png</key>
            <key type="filename">txt_shjmtg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>93,22,187,43</rect>
                <key>scale9Paddings</key>
                <rect>93,22,187,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_kzltg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>89,22,177,43</rect>
                <key>scale9Paddings</key>
                <rect>89,22,177,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_kzmy.png</key>
            <key type="filename">txt_mykz.png</key>
            <key type="filename">txt_xz2.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>71,22,143,43</rect>
                <key>scale9Paddings</key>
                <rect>71,22,143,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_ll.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>75,25,149,49</rect>
                <key>scale9Paddings</key>
                <rect>75,25,149,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_ls.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>42,25,83,49</rect>
                <key>scale9Paddings</key>
                <rect>42,25,83,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_ls2.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>56,22,112,43</rect>
                <key>scale9Paddings</key>
                <rect>56,22,112,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_lwsxjd.png</key>
            <key type="filename">txt_tstg.png</key>
            <key type="filename">txt_zlljd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>73,22,146,43</rect>
                <key>scale9Paddings</key>
                <rect>73,22,146,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_lwsxts.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>75,22,149,43</rect>
                <key>scale9Paddings</key>
                <rect>75,22,149,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_lx1.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>56,22,113,43</rect>
                <key>scale9Paddings</key>
                <rect>56,22,113,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_lxgs.png</key>
            <key type="filename">txt_qy.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>69,22,138,43</rect>
                <key>scale9Paddings</key>
                <rect>69,22,138,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_mb.png</key>
            <key type="filename">txt_nqhftg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>43,22,85,43</rect>
                <key>scale9Paddings</key>
                <rect>43,22,85,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_mh.png</key>
            <key type="filename">txt_sf.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>55,22,111,43</rect>
                <key>scale9Paddings</key>
                <rect>55,22,111,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_mh3.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>76,25,151,49</rect>
                <key>scale9Paddings</key>
                <rect>76,25,151,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_mkljd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>90,22,179,43</rect>
                <key>scale9Paddings</key>
                <rect>90,22,179,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_mkltg.png</key>
            <key type="filename">txt_shjmjd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>91,22,181,43</rect>
                <key>scale9Paddings</key>
                <rect>91,22,181,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_mm.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>51,22,101,43</rect>
                <key>scale9Paddings</key>
                <rect>51,22,101,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_my.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>56,25,113,49</rect>
                <key>scale9Paddings</key>
                <rect>56,25,113,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_mycr.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>99,25,199,49</rect>
                <key>scale9Paddings</key>
                <rect>99,25,199,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_mydkfm.png</key>
            <key type="filename">txt_zsshjmtg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>91,22,183,43</rect>
                <key>scale9Paddings</key>
                <rect>91,22,183,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_mydkkz.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>103,22,205,43</rect>
                <key>scale9Paddings</key>
                <rect>103,22,205,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_myxg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>60,16,119,32</rect>
                <key>scale9Paddings</key>
                <rect>60,16,119,32</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_myzy.png</key>
            <key type="filename">txt_rhzx.png</key>
            <key type="filename">txt_ryzs.png</key>
            <key type="filename">txt_tkzh.png</key>
            <key type="filename">txt_wswf.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>91,25,183,49</rect>
                <key>scale9Paddings</key>
                <rect>91,25,183,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_mzljd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>96,22,191,43</rect>
                <key>scale9Paddings</key>
                <rect>96,22,191,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_mzltg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>96,22,193,43</rect>
                <key>scale9Paddings</key>
                <rect>96,22,193,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_nqhfjd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>44,22,89,43</rect>
                <key>scale9Paddings</key>
                <rect>44,22,89,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_nqjd.png</key>
            <key type="filename">txt_ze.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>50,22,99,43</rect>
                <key>scale9Paddings</key>
                <rect>50,22,99,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_nqtg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>44,22,87,43</rect>
                <key>scale9Paddings</key>
                <rect>44,22,87,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_ny1.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>57,25,113,49</rect>
                <key>scale9Paddings</key>
                <rect>57,25,113,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_pdyljd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>99,22,197,43</rect>
                <key>scale9Paddings</key>
                <rect>99,22,197,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_pdyltg.png</key>
            <key type="filename">txt_qyxt.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>65,22,130,43</rect>
                <key>scale9Paddings</key>
                <rect>65,22,130,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_ptzt.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>92,22,183,43</rect>
                <key>scale9Paddings</key>
                <rect>92,22,183,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_pz1.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>60,25,119,49</rect>
                <key>scale9Paddings</key>
                <rect>60,25,119,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_qs.png</key>
            <key type="filename">txt_wstg.png</key>
            <key type="filename">txt_zzyj.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>63,22,125,43</rect>
                <key>scale9Paddings</key>
                <rect>63,22,125,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_qthd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>74,21,148,43</rect>
                <key>scale9Paddings</key>
                <rect>74,21,148,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_qyzs.png</key>
            <key type="filename">txt_sd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>64,22,128,43</rect>
                <key>scale9Paddings</key>
                <rect>64,22,128,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_rh1.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>50,22,100,43</rect>
                <key>scale9Paddings</key>
                <rect>50,22,100,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_sbts.png</key>
            <key type="filename">txt_zzshjd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>46,22,93,43</rect>
                <key>scale9Paddings</key>
                <rect>46,22,93,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_sdjd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>42,22,83,43</rect>
                <key>scale9Paddings</key>
                <rect>42,22,83,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_sdsm.png</key>
            <key type="filename">txt_sl.png</key>
            <key type="filename">txt_sm.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>47,22,94,43</rect>
                <key>scale9Paddings</key>
                <rect>47,22,94,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_sdtg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>41,22,81,43</rect>
                <key>scale9Paddings</key>
                <rect>41,22,81,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_sgdj.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>51,22,102,43</rect>
                <key>scale9Paddings</key>
                <rect>51,22,102,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_shjd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>59,22,119,43</rect>
                <key>scale9Paddings</key>
                <rect>59,22,119,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_skjs.png</key>
            <key type="filename">txt_tx.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>53,22,106,43</rect>
                <key>scale9Paddings</key>
                <rect>53,22,106,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_sl1.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>44,25,87,49</rect>
                <key>scale9Paddings</key>
                <rect>44,25,87,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_smsxjd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>88,22,177,43</rect>
                <key>scale9Paddings</key>
                <rect>88,22,177,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_smsxtg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>89,22,178,43</rect>
                <key>scale9Paddings</key>
                <rect>89,22,178,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_sr.png</key>
            <key type="filename">txt_szll.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>75,25,150,49</rect>
                <key>scale9Paddings</key>
                <rect>75,25,150,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_sx.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>45,25,91,49</rect>
                <key>scale9Paddings</key>
                <rect>45,25,91,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_sxbkzts.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>108,22,215,43</rect>
                <key>scale9Paddings</key>
                <rect>108,22,215,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_sxsw.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>83,22,166,43</rect>
                <key>scale9Paddings</key>
                <rect>83,22,166,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_tfhsts.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>126,22,251,43</rect>
                <key>scale9Paddings</key>
                <rect>126,22,251,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_tfjd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>73,22,147,43</rect>
                <key>scale9Paddings</key>
                <rect>73,22,147,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_tgjd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>80,22,159,43</rect>
                <key>scale9Paddings</key>
                <rect>80,22,159,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_tgtg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>79,22,159,43</rect>
                <key>scale9Paddings</key>
                <rect>79,22,159,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_tqsxsj.png</key>
            <key type="filename">txt_wgjd.png</key>
            <key type="filename">txt_xy1.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>70,22,140,43</rect>
                <key>scale9Paddings</key>
                <rect>70,22,140,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_tsjcjd.png</key>
            <key type="filename">txt_wgtg.png</key>
            <key type="filename">txt_xx.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>70,22,139,43</rect>
                <key>scale9Paddings</key>
                <rect>70,22,139,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_tsjmtg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>104,22,208,43</rect>
                <key>scale9Paddings</key>
                <rect>104,22,208,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_tsys.png</key>
            <key type="filename">txt_wlys.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>61,22,122,43</rect>
                <key>scale9Paddings</key>
                <rect>61,22,122,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_wfhsts.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>115,22,229,43</rect>
                <key>scale9Paddings</key>
                <rect>115,22,229,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_wftg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>63,22,126,43</rect>
                <key>scale9Paddings</key>
                <rect>63,22,126,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_ws.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>57,22,115,43</rect>
                <key>scale9Paddings</key>
                <rect>57,22,115,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_wsjmtg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>95,22,190,43</rect>
                <key>scale9Paddings</key>
                <rect>95,22,190,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_wstf.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>104,25,207,49</rect>
                <key>scale9Paddings</key>
                <rect>104,25,207,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_wzbh.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>77,22,155,43</rect>
                <key>scale9Paddings</key>
                <rect>77,22,155,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_xd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>55,22,109,43</rect>
                <key>scale9Paddings</key>
                <rect>55,22,109,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_xd1.png</key>
            <key type="filename">txt_xwzy.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>48,22,95,43</rect>
                <key>scale9Paddings</key>
                <rect>48,22,95,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_xdwx.png</key>
            <key type="filename">txt_xls.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>90,25,179,49</rect>
                <key>scale9Paddings</key>
                <rect>90,25,179,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_xgbq.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>85,22,170,43</rect>
                <key>scale9Paddings</key>
                <rect>85,22,170,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_xh.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>69,22,139,43</rect>
                <key>scale9Paddings</key>
                <rect>69,22,139,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_xr.png</key>
            <key type="filename">txt_xz.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>45,25,89,49</rect>
                <key>scale9Paddings</key>
                <rect>45,25,89,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_xy.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>76,22,152,43</rect>
                <key>scale9Paddings</key>
                <rect>76,22,152,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_xyj.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>66,22,132,43</rect>
                <key>scale9Paddings</key>
                <rect>66,22,132,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_yb.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>73,25,147,49</rect>
                <key>scale9Paddings</key>
                <rect>73,25,147,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_yexj.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>70,25,139,49</rect>
                <key>scale9Paddings</key>
                <rect>70,25,139,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_ylxj.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>85,25,170,49</rect>
                <key>scale9Paddings</key>
                <rect>85,25,170,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_yn.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>69,22,137,43</rect>
                <key>scale9Paddings</key>
                <rect>69,22,137,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_yn1.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>47,25,95,49</rect>
                <key>scale9Paddings</key>
                <rect>47,25,95,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_yysh.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>105,25,209,49</rect>
                <key>scale9Paddings</key>
                <rect>105,25,209,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_yz.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>59,25,117,49</rect>
                <key>scale9Paddings</key>
                <rect>59,25,117,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_yzwl.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>85,25,169,49</rect>
                <key>scale9Paddings</key>
                <rect>85,25,169,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_zb.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>100,25,200,49</rect>
                <key>scale9Paddings</key>
                <rect>100,25,200,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_zd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>48,22,97,43</rect>
                <key>scale9Paddings</key>
                <rect>48,22,97,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_zh.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>39,25,78,49</rect>
                <key>scale9Paddings</key>
                <rect>39,25,78,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_zh2.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>70,25,140,49</rect>
                <key>scale9Paddings</key>
                <rect>70,25,140,49</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_zjwj.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>52,22,104,43</rect>
                <key>scale9Paddings</key>
                <rect>52,22,104,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_zlltg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>76,22,153,43</rect>
                <key>scale9Paddings</key>
                <rect>76,22,153,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_zlxgtg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>99,22,199,43</rect>
                <key>scale9Paddings</key>
                <rect>99,22,199,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_zm1.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>21,22,41,43</rect>
                <key>scale9Paddings</key>
                <rect>21,22,41,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_zm2.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>52,22,103,43</rect>
                <key>scale9Paddings</key>
                <rect>52,22,103,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_zs2.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>66,22,131,43</rect>
                <key>scale9Paddings</key>
                <rect>66,22,131,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_zsqdtg.png</key>
            <key type="filename">txt_zzl.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>109,22,217,43</rect>
                <key>scale9Paddings</key>
                <rect>109,22,217,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_zsshjmjd.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>90,22,180,43</rect>
                <key>scale9Paddings</key>
                <rect>90,22,180,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_zszy.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>61,22,123,43</rect>
                <key>scale9Paddings</key>
                <rect>61,22,123,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_zyxgts.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>83,22,165,43</rect>
                <key>scale9Paddings</key>
                <rect>83,22,165,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
            <key type="filename">txt_zzshtg.png</key>
            <struct type="IndividualSpriteSettings">
                <key>pivotPoint</key>
                <point_f>0.5,0.5</point_f>
                <key>spriteScale</key>
                <double>1</double>
                <key>scale9Enabled</key>
                <false/>
                <key>scale9Borders</key>
                <rect>51,22,103,43</rect>
                <key>scale9Paddings</key>
                <rect>51,22,103,43</rect>
                <key>scale9FromFile</key>
                <false/>
            </struct>
        </map>
        <key>fileLists</key>
        <map type="SpriteSheetMap">
            <key>default</key>
            <struct type="SpriteSheet">
                <key>files</key>
                <array>
                    <filename>txt_zzzd.png</filename>
                    <filename>txt_zzyj.png</filename>
                    <filename>txt_zzshtg.png</filename>
                    <filename>txt_zzshjd.png</filename>
                    <filename>txt_zzl.png</filename>
                    <filename>txt_zz.png</filename>
                    <filename>txt_zyxgts.png</filename>
                    <filename>txt_zyrh.png</filename>
                    <filename>txt_zyqh.png</filename>
                    <filename>txt_zyjh.png</filename>
                    <filename>txt_zy2.png</filename>
                    <filename>txt_zy.png</filename>
                    <filename>txt_zszy.png</filename>
                    <filename>txt_zsshjmtg.png</filename>
                    <filename>txt_zsshjmjd.png</filename>
                    <filename>txt_zsqdtg.png</filename>
                    <filename>txt_zsh.png</filename>
                    <filename>txt_zs2.png</filename>
                    <filename>txt_zs1.png</filename>
                    <filename>txt_zm2.png</filename>
                    <filename>txt_zm1.png</filename>
                    <filename>txt_zm.png</filename>
                    <filename>txt_zlxgtg.png</filename>
                    <filename>txt_zlltg.png</filename>
                    <filename>txt_zlljd.png</filename>
                    <filename>txt_zlg.png</filename>
                    <filename>txt_zjzt.png</filename>
                    <filename>txt_zjwj.png</filename>
                    <filename>txt_zhhcl.png</filename>
                    <filename>txt_zhfd.png</filename>
                    <filename>txt_zhdqwft.png</filename>
                    <filename>txt_zhcs.png</filename>
                    <filename>txt_zhcjb.png</filename>
                    <filename>txt_zh3.png</filename>
                    <filename>txt_zh2.png</filename>
                    <filename>txt_zh1.png</filename>
                    <filename>txt_zh.png</filename>
                    <filename>txt_zf.png</filename>
                    <filename>txt_ze.png</filename>
                    <filename>txt_zdh.png</filename>
                    <filename>txt_zd.png</filename>
                    <filename>txt_zb.png</filename>
                    <filename>txt_yzwl.png</filename>
                    <filename>txt_yz.png</filename>
                    <filename>txt_yys.png</filename>
                    <filename>txt_ytjs.png</filename>
                    <filename>txt_yszt.png</filename>
                    <filename>txt_ys1.png</filename>
                    <filename>txt_ys.png</filename>
                    <filename>txt_yqw.png</filename>
                    <filename>txt_yqtc.png</filename>
                    <filename>txt_yn1.png</filename>
                    <filename>txt_yn.png</filename>
                    <filename>txt_ymgh.png</filename>
                    <filename>txt_ym.png</filename>
                    <filename>txt_ylxw.png</filename>
                    <filename>txt_ylht.png</filename>
                    <filename>txt_yl2.png</filename>
                    <filename>txt_yl.png</filename>
                    <filename>txt_yj.png</filename>
                    <filename>txt_yh1.png</filename>
                    <filename>txt_yh.png</filename>
                    <filename>txt_ycydj.png</filename>
                    <filename>txt_yb.png</filename>
                    <filename>txt_xzge.png</filename>
                    <filename>txt_xz2.png</filename>
                    <filename>txt_xz1.png</filename>
                    <filename>txt_xz.png</filename>
                    <filename>txt_xyx.png</filename>
                    <filename>txt_xyj.png</filename>
                    <filename>txt_xy1.png</filename>
                    <filename>txt_xy.png</filename>
                    <filename>txt_xx2.png</filename>
                    <filename>txt_xx.png</filename>
                    <filename>txt_xwzy.png</filename>
                    <filename>txt_xsnq.png</filename>
                    <filename>txt_xs.png</filename>
                    <filename>txt_xr.png</filename>
                    <filename>txt_xls.png</filename>
                    <filename>txt_xl.png</filename>
                    <filename>txt_xh.png</filename>
                    <filename>txt_xgbq.png</filename>
                    <filename>txt_xfzr.png</filename>
                    <filename>txt_xf.png</filename>
                    <filename>txt_xd1.png</filename>
                    <filename>txt_xd.png</filename>
                    <filename>txt_xb.png</filename>
                    <filename>txt_wzbh.png</filename>
                    <filename>txt_wszl.png</filename>
                    <filename>txt_wswf.png</filename>
                    <filename>txt_wstg.png</filename>
                    <filename>txt_wstf.png</filename>
                    <filename>txt_wsjmtg.png</filename>
                    <filename>txt_wsjd.png</filename>
                    <filename>txt_wshd.png</filename>
                    <filename>txt_ws.png</filename>
                    <filename>txt_wqjh.png</filename>
                    <filename>txt_wlys.png</filename>
                    <filename>txt_wjsl.png</filename>
                    <filename>txt_wgtg.png</filename>
                    <filename>txt_wgshmy.png</filename>
                    <filename>txt_wgjd.png</filename>
                    <filename>txt_wftg.png</filename>
                    <filename>txt_wfjd.png</filename>
                    <filename>txt_wfhsts.png</filename>
                    <filename>txt_wfdt.png</filename>
                    <filename>txt_wbxt.png</filename>
                    <filename>txt_tx.png</filename>
                    <filename>txt_tw.png</filename>
                    <filename>txt_tsys.png</filename>
                    <filename>txt_tstg.png</filename>
                    <filename>txt_tsjmtg.png</filename>
                    <filename>txt_tsjcjd.png</filename>
                    <filename>txt_ts.png</filename>
                    <filename>txt_tqsxsj.png</filename>
                    <filename>txt_tl.png</filename>
                    <filename>txt_tkzh.png</filename>
                    <filename>txt_tkxt.png</filename>
                    <filename>txt_tgtg.png</filename>
                    <filename>txt_tgshmy.png</filename>
                    <filename>txt_tgjd.png</filename>
                    <filename>txt_tg.png</filename>
                    <filename>txt_tftg.png</filename>
                    <filename>txt_tfjd.png</filename>
                    <filename>txt_tfhsts.png</filename>
                    <filename>txt_te.png</filename>
                    <filename>txt_szll.png</filename>
                    <filename>txt_szjh.png</filename>
                    <filename>txt_sylx.png</filename>
                    <filename>txt_syfs.png</filename>
                    <filename>txt_sxys.png</filename>
                    <filename>txt_sxsw.png</filename>
                    <filename>txt_sxkzts.png</filename>
                    <filename>txt_sxbkzts.png</filename>
                    <filename>txt_sx1.png</filename>
                    <filename>txt_sx.png</filename>
                    <filename>txt_swyj.png</filename>
                    <filename>txt_swlj.png</filename>
                    <filename>txt_sslj.png</filename>
                    <filename>txt_srm.png</filename>
                    <filename>txt_sr.png</filename>
                    <filename>txt_spp.png</filename>
                    <filename>txt_snzf.png</filename>
                    <filename>txt_smzz.png</filename>
                    <filename>txt_smsxtg.png</filename>
                    <filename>txt_smsxjd.png</filename>
                    <filename>txt_smlj.png</filename>
                    <filename>txt_sm.png</filename>
                    <filename>txt_slhz.png</filename>
                    <filename>txt_slhr.png</filename>
                    <filename>txt_slcf.png</filename>
                    <filename>txt_sl2.png</filename>
                    <filename>txt_sl1.png</filename>
                    <filename>txt_sl.png</filename>
                    <filename>txt_skjs.png</filename>
                    <filename>txt_skhs.png</filename>
                    <filename>txt_sjll.png</filename>
                    <filename>txt_sjky.png</filename>
                    <filename>txt_sjjx.png</filename>
                    <filename>txt_sjbd.png</filename>
                    <filename>txt_shzy.png</filename>
                    <filename>txt_shzs.png</filename>
                    <filename>txt_shtg.png</filename>
                    <filename>txt_shjmtg.png</filename>
                    <filename>txt_shjmjd.png</filename>
                    <filename>txt_shjj.png</filename>
                    <filename>txt_shjd.png</filename>
                    <filename>txt_sh.png</filename>
                    <filename>txt_sh2.png</filename>
                    <filename>txt_sgdj.png</filename>
                    <filename>txt_sg.png</filename>
                    <filename>txt_sf.png</filename>
                    <filename>txt_sdtg.png</filename>
                    <filename>txt_sdsm.png</filename>
                    <filename>txt_sdjd.png</filename>
                    <filename>txt_sd1.png</filename>
                    <filename>txt_sd.png</filename>
                    <filename>txt_scyj.png</filename>
                    <filename>txt_sbts.png</filename>
                    <filename>txt_sb.png</filename>
                    <filename>txt_ryzt.png</filename>
                    <filename>txt_ryzs.png</filename>
                    <filename>txt_rydz.png</filename>
                    <filename>txt_rhzx.png</filename>
                    <filename>txt_rh1.png</filename>
                    <filename>txt_rh.png</filename>
                    <filename>txt_rdyq.png</filename>
                    <filename>txt_rdy.png</filename>
                    <filename>txt_rdlj.png</filename>
                    <filename>txt_qyzs.png</filename>
                    <filename>txt_qyxt.png</filename>
                    <filename>txt_qyhq.png</filename>
                    <filename>txt_qy1.png</filename>
                    <filename>txt_qy.png</filename>
                    <filename>txt_qxdh.png</filename>
                    <filename>txt_qthd.png</filename>
                    <filename>txt_qs.png</filename>
                    <filename>txt_qlms.png</filename>
                    <filename>txt_qh.png</filename>
                    <filename>txt_pz1.png</filename>
                    <filename>txt_ptzt.png</filename>
                    <filename>txt_pljhz.png</filename>
                    <filename>txt_phzj.png</filename>
                    <filename>txt_pdyltg.png</filename>
                    <filename>txt_pdyljd.png</filename>
                    <filename>txt_ny1.png</filename>
                    <filename>txt_ny.png</filename>
                    <filename>txt_nqtg.png</filename>
                    <filename>txt_nqsxtg.png</filename>
                    <filename>txt_nqjl.png</filename>
                    <filename>txt_nqjd.png</filename>
                    <filename>txt_nqhs.png</filename>
                    <filename>txt_nqhftg.png</filename>
                    <filename>txt_nqhfjd.png</filename>
                    <filename>txt_nlfs.png</filename>
                    <filename>txt_nbzd.png</filename>
                    <filename>txt_mzltg.png</filename>
                    <filename>txt_mzljd.png</filename>
                    <filename>txt_mzg.png</filename>
                    <filename>txt_myzy.png</filename>
                    <filename>txt_myzs.png</filename>
                    <filename>txt_myzm.png</filename>
                    <filename>txt_myzd.png</filename>
                    <filename>txt_myxg.png</filename>
                    <filename>txt_mysw.png</filename>
                    <filename>txt_mysh1.png</filename>
                    <filename>txt_mysh.png</filename>
                    <filename>txt_mynqjd.png</filename>
                    <filename>txt_mykz.png</filename>
                    <filename>txt_myfm.png</filename>
                    <filename>txt_mydkkz.png</filename>
                    <filename>txt_mydkfm.png</filename>
                    <filename>txt_mycr.png</filename>
                    <filename>txt_mybd.png</filename>
                    <filename>txt_my2.png</filename>
                    <filename>txt_my1.png</filename>
                    <filename>txt_my.png</filename>
                    <filename>txt_mm.png</filename>
                    <filename>txt_mkltg.png</filename>
                    <filename>txt_mkljd.png</filename>
                    <filename>txt_mkl.png</filename>
                    <filename>txt_mht.png</filename>
                    <filename>txt_mh2.png</filename>
                    <filename>txt_mh.png</filename>
                    <filename>txt_mb.png</filename>
                    <filename>txt_lz.png</filename>
                    <filename>txt_lyzl.png</filename>
                    <filename>txt_lyswzy.png</filename>
                    <filename>txt_ly1.png</filename>
                    <filename>txt_ly.png</filename>
                    <filename>txt_lxgs.png</filename>
                    <filename>txt_lx3.png</filename>
                    <filename>txt_lx2.png</filename>
                    <filename>txt_lx1.png</filename>
                    <filename>txt_lx.png</filename>
                    <filename>txt_lssz.png</filename>
                    <filename>txt_lsjj.png</filename>
                    <filename>txt_lsg.png</filename>
                    <filename>txt_ls3.png</filename>
                    <filename>txt_ls2.png</filename>
                    <filename>txt_ls1.png</filename>
                    <filename>txt_ls.png</filename>
                    <filename>txt_ll.png</filename>
                    <filename>txt_lj1.png</filename>
                    <filename>txt_lj.png</filename>
                    <filename>txt_ldxt.png</filename>
                    <filename>txt_ldhy.png</filename>
                    <filename>txt_ldfx.png</filename>
                    <filename>txt_lb.png</filename>
                    <filename>txt_kzmy.png</filename>
                    <filename>txt_kzltg.png</filename>
                    <filename>txt_kzljd.png</filename>
                    <filename>txt_kj.png</filename>
                    <filename>txt_jyrh.png</filename>
                    <filename>txt_jyqh.png</filename>
                    <filename>txt_jyhldj.png</filename>
                    <filename>txt_jydkltg.png</filename>
                    <filename>txt_jydkljd.png</filename>
                    <filename>txt_jy.png</filename>
                    <filename>txt_jwzt.png</filename>
                    <filename>txt_jw.png</filename>
                    <filename>txt_jt.png</filename>
                    <filename>txt_jszz.png</filename>
                    <filename>txt_jl2.png</filename>
                    <filename>txt_jl1.png</filename>
                    <filename>txt_jl.png</filename>
                    <filename>txt_jh.png</filename>
                    <filename>txt_jgzd.png</filename>
                    <filename>txt_jf.png</filename>
                    <filename>txt_jdx.png</filename>
                    <filename>txt_jdc.png</filename>
                    <filename>txt_jd.png</filename>
                    <filename>txt_jc.png</filename>
                    <filename>txt_jbzl.png</filename>
                    <filename>txt_hyzl.png</filename>
                    <filename>txt_hypz.png</filename>
                    <filename>txt_hymz.png</filename>
                    <filename>txt_hyms.png</filename>
                    <filename>txt_hxys.png</filename>
                    <filename>txt_hxcf.png</filename>
                    <filename>txt_hx.png</filename>
                    <filename>txt_hszz.png</filename>
                    <filename>txt_hs1.png</filename>
                    <filename>txt_hs.png</filename>
                    <filename>txt_hp.png</filename>
                    <filename>txt_hnsh.png</filename>
                    <filename>txt_hl2.png</filename>
                    <filename>txt_hl1.png</filename>
                    <filename>txt_hl.png</filename>
                    <filename>txt_hjzd.png</filename>
                    <filename>txt_hjsh.png</filename>
                    <filename>txt_hj.png</filename>
                    <filename>txt_hh.png</filename>
                    <filename>txt_hf.png</filename>
                    <filename>txt_hd.png</filename>
                    <filename>txt_hc.png</filename>
                    <filename>txt_hbxj.png</filename>
                    <filename>txt_hbhd.png</filename>
                    <filename>txt_gy.png</filename>
                    <filename>txt_gtsp.png</filename>
                    <filename>txt_gthd2.png</filename>
                    <filename>txt_gthd.png</filename>
                    <filename>txt_gsys.png</filename>
                    <filename>txt_gsxt.png</filename>
                    <filename>txt_gm1.png</filename>
                    <filename>txt_gd2.png</filename>
                    <filename>txt_gd1.png</filename>
                    <filename>txt_gd.png</filename>
                    <filename>txt_fz1.png</filename>
                    <filename>txt_fz.png</filename>
                    <filename>txt_fysl.png</filename>
                    <filename>txt_fx.png</filename>
                    <filename>txt_fsms.png</filename>
                    <filename>txt_fs1.png</filename>
                    <filename>txt_fs.png</filename>
                    <filename>txt_fryd.png</filename>
                    <filename>txt_fn.png</filename>
                    <filename>txt_fmdkltg.png</filename>
                    <filename>txt_fmdkljd.png</filename>
                    <filename>txt_fj.png</filename>
                    <filename>txt_fh.png</filename>
                    <filename>txt_fdzs.png</filename>
                    <filename>txt_fdh.png</filename>
                    <filename>txt_fd.png</filename>
                    <filename>txt_ewxd.png</filename>
                    <filename>txt_emyj.png</filename>
                    <filename>txt_dzmw.png</filename>
                    <filename>txt_dyqdtg.png</filename>
                    <filename>txt_dyqdjd.png</filename>
                    <filename>txt_dyltg.png</filename>
                    <filename>txt_dyljd.png</filename>
                    <filename>txt_dxys.png</filename>
                    <filename>txt_dxsh.png</filename>
                    <filename>txt_dsxys.png</filename>
                    <filename>txt_dlcd.png</filename>
                    <filename>txt_dh.png</filename>
                    <filename>txt_df.png</filename>
                    <filename>txt_ddfy.png</filename>
                    <filename>txt_cyzt.png</filename>
                    <filename>txt_cxy.png</filename>
                    <filename>txt_cszj.png</filename>
                    <filename>txt_cs1.png</filename>
                    <filename>txt_cs.png</filename>
                    <filename>txt_cr.png</filename>
                    <filename>txt_cpms.png</filename>
                    <filename>txt_cm.png</filename>
                    <filename>txt_chzf.png</filename>
                    <filename>txt_ch.png</filename>
                    <filename>txt_cf1.png</filename>
                    <filename>txt_cf.png</filename>
                    <filename>txt_cd.png</filename>
                    <filename>txt_ccg.png</filename>
                    <filename>txt_bzxt.png</filename>
                    <filename>txt_bz.png</filename>
                    <filename>txt_by.png</filename>
                    <filename>txt_bxys.png</filename>
                    <filename>txt_bsxys.png</filename>
                    <filename>txt_bsshtg.png</filename>
                    <filename>txt_bsshjd.png</filename>
                    <filename>txt_bskxtg.png</filename>
                    <filename>txt_bskxjd.png</filename>
                    <filename>txt_bq.png</filename>
                    <filename>txt_bn.png</filename>
                    <filename>txt_bjshtg.png</filename>
                    <filename>txt_bjshjd.png</filename>
                    <filename>txt_bjltg.png</filename>
                    <filename>txt_bjljd.png</filename>
                    <filename>txt_bjkxtg.png</filename>
                    <filename>txt_bjkxjd.png</filename>
                    <filename>txt_bj.png</filename>
                    <filename>txt_bh.png</filename>
                    <filename>txt_bfx.png</filename>
                    <filename>txt_bdqdtgpng.png</filename>
                    <filename>txt_bd.png</filename>
                    <filename>tat_zy.png</filename>
                    <filename>bg_zzl_di.png</filename>
                    <filename>bg_zlsz_di.png</filename>
                    <filename>bg_ptsh_di.png</filename>
                    <filename>bg_nqjl_di.png</filename>
                    <filename>bg_kz_di.png</filename>
                    <filename>bg_bj_di.png</filename>
                    <filename>txt_mh3.png</filename>
                    <filename>txt_tb.png</filename>
                    <filename>txt_smsh.png</filename>
                    <filename>txt_hm.png</filename>
                    <filename>txt_zyjz.png</filename>
                    <filename>txt_yysh.png</filename>
                    <filename>txt_ylzh.png</filename>
                    <filename>txt_ylxj.png</filename>
                    <filename>txt_ylxf.png</filename>
                    <filename>txt_yexj.png</filename>
                    <filename>txt_xrzt.png</filename>
                    <filename>txt_xdwx.png</filename>
                    <filename>txt_wjph.png</filename>
                    <filename>txt_tjzz.png</filename>
                    <filename>txt_tcdk.png</filename>
                    <filename>txt_qr.png</filename>
                    <filename>txt_pz.png</filename>
                    <filename>txt_pmhd.png</filename>
                    <filename>txt_mhxj.png</filename>
                    <filename>txt_lyk.png</filename>
                    <filename>txt_lwsxts.png</filename>
                    <filename>txt_lwsxjd.png</filename>
                    <filename>txt_ljhs.png</filename>
                    <filename>txt_kn.png</filename>
                    <filename>txt_jsym.png</filename>
                    <filename>txt_hxrh.png</filename>
                    <filename>txt_gtfc.png</filename>
                    <filename>txt_gl.png</filename>
                    <filename>txt_gjyd.png</filename>
                    <filename>txt_fzxj.png</filename>
                    <filename>txt_dq.png</filename>
                    <filename>txt_dlxj.png</filename>
                    <filename>txt_dcxj.png</filename>
                    <filename>txt_bqzz.png</filename>
                    <filename>txt_bl.png</filename>
                </array>
            </struct>
        </map>
        <key>ignoreFileList</key>
        <array/>
        <key>replaceList</key>
        <array/>
        <key>ignoredWarnings</key>
        <array/>
        <key>commonDivisorX</key>
        <uint>1</uint>
        <key>commonDivisorY</key>
        <uint>1</uint>
        <key>packNormalMaps</key>
        <false/>
        <key>autodetectNormalMaps</key>
        <true/>
        <key>normalMapFilter</key>
        <string></string>
        <key>normalMapSuffix</key>
        <string></string>
        <key>normalMapSheetFileName</key>
        <filename></filename>
        <key>exporterProperties</key>
        <map type="ExporterProperties"/>
    </struct>
</data>
