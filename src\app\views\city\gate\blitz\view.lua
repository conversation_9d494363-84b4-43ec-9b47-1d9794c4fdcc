﻿local ViewBase = cc.load("mvc").ViewBase
local Blitz = class("Blitz", ViewBase)

local chapterParentBg = "3q/gate/campaign/tab_2A.png"
local chapterChildrenBg = "3q/gate/campaign/tab_chapter.png"
local chapterChildrenSelectBg = "3q/gate/common/bg_tips_21.png"
local arrowDown = "3q/common/arrow_07.png"
local arrowUp = "3q/common/arrow_07_up.png"

local chapterEasy = 1
local chapterHard = 2
local chapterNightmare = 3
local tabTypeNotFinish = 4

local chapterTypes = {
    [chapterEasy] = { name = gLanguageCsv.chapterEasyName, chapterType = 1 },
    [chapterHard] = { name = gLanguageCsv.chapterHardName, chapterType = 2 },
    [chapterNightmare] = { name = gLanguageCsv.chapterNightmareName, chapterType = 3 },
    [tabTypeNotFinish] = { name = gLanguageCsv.chapterNotFinishName, chapterType = tabTypeNotFinish },
}

local function getDropItemData(gateID)
    local dropDatasMap = {}
    local dropDatas = {}
    local stableDrops = gStableDropsCsv[gateID]
    if stableDrops ~= nil and #stableDrops > 0 then
        for i, v in ipairs(stableDrops) do
            local existed = dropDatasMap[v.itemID]
            if existed == nil then
                table.insert(dropDatas, { key = v.itemID, num = 0 })
            end
            dropDatasMap[v.itemID] = true
        end
    end

    local randomDrops = gRandomDropsCsv[gateID]
    if randomDrops ~= nil and #randomDrops > 0 then
        for _, v in ipairs(randomDrops) do
            for itemID, _ in pairs(v.itemMap) do
                if itemID ~= "__size" then
                    local existed = dropDatasMap[itemID]
                    if existed == nil then
                        table.insert(dropDatas, { key = itemID, num = 0 })
                    end
                    dropDatasMap[itemID] = true
                end
            end
        end
    end

    table.sort(dropDatas, dataEasy.sortItemCmp)
    return dropDatas
end

local function initDropItemsBK(list, node, v)
    local listDrops = node:get("dropList")
    local lenDropsItem = #v.dropIDs
    listDrops:width(math.min(lenDropsItem, 4) * list.iconItem:width())

    bind.extend(list, listDrops, {
        class = "listview",
        props = {
            data = v.dropIDs,
            item = list.iconItem,
            dataOrderCmp = dataEasy.sortItemCmp,
            onItem = function(ll, node2, k, v2)
                local binds = {
                    class = "icon_key",
                    props = {
                        data = {
                            key = v2.key,
                            num = v2.num,
                        },
                    },
                }
                bind.extend(ll, node2, binds)
            end,
            asyncPreload = 6,
        }
    })
end

local function initDropItems(list, itemGate, v)
    local listDrops = itemGate:get("dropList")
    local lenDropsItem = #v.dropIDs
    listDrops:width(math.min(lenDropsItem, 4) * list.iconItem:width())

    bind.extend(list, listDrops, {
        class = "listview",
        props = {
            data = v.dropIDs,
            item = list.iconItem,
            dataOrderCmp = dataEasy.sortItemCmp,
            onItem = function(ll, node2, k, v2)
                local binds = {
                    class = "icon_key",
                    props = {
                        data = {
                            key = v2.key,
                            num = v2.num,
                        },
                    },
                }
                bind.extend(ll, node2, binds)
            end,
            asyncPreload = 6,
        }
    })
end

local function initBlitzGateItem(list, node, v)
    local itemGate = list.itemGate:clone()
    itemGate:addTo(node, 100, "itemGate")
    itemGate:xy(0, 0)

    local sceneCfg = csv.scene_conf[v.gateID]
    initDropItems(list, itemGate, v)
    itemGate:get("gateName"):text(v.name)
    local btnBlitz = itemGate:get("btnBlitz")
    local btnBlitzMax = itemGate:get("btnBlitzMax")

    local panelTimes = itemGate:get("panelTimes")
    local maxTimes = panelTimes:get("maxTimes")
    local leftTime = panelTimes:get("leftTime")
    local labelCount = panelTimes:get("labelCount")
    local btnBuy = panelTimes:get("btnBuy")
    bind.click(list, btnBuy, { method = functools.partial(list.onTimesBtnClick, v.gateID) })
    if v.surplusTimes > 50 then
        panelTimes:hide()
    end

    maxTimes:text("/" .. v.dayChallengeMax)
    leftTime:text(v.surplusTimes)
    if v.surplusTimes > 100 then
        panelTimes:hide()
    end

    adapt.oneLineCenterPos(cc.p(50, 0), { labelCount, leftTime, maxTimes, btnBuy }, cc.p(10, 0))

    local dayChallengeMaxMenu = math.min(v.dayChallengeMax, 10)
    local minChallenge = 1

    local sortDatas = idlers.newWithMap({
        { val = minChallenge, name = string.format(gLanguageCsv.sweepManyTimes, minChallenge) },
        { val = dayChallengeMaxMenu, name = string.format(gLanguageCsv.sweepManyTimes, dayChallengeMaxMenu) },
    })
    local showLeftSelected = idler.new(1)
    itemGate:get("btnBlitz"):get("textCostNum"):text(sceneCfg.staminaCost * minChallenge)
    itemGate:get("btnBlitz"):get("name"):text(string.format(gLanguageCsv.sweepTimes, minChallenge))
    bind.click(list, btnBlitz, { method = functools.partial(list.onBlitzBtnClick, v.gateID, minChallenge) })

    itemGate:get("btnBlitzMax"):get("textCostNum"):text(sceneCfg.staminaCost * dayChallengeMaxMenu)
    itemGate:get("btnBlitzMax"):get("name"):text(string.format(gLanguageCsv.sweepTimes, dayChallengeMaxMenu))
    bind.click(list, btnBlitzMax, { method = functools.partial(list.onBlitzBtnClick, v.gateID, dayChallengeMaxMenu) })

    local onSortMenusBtnClick = function(_, _, selv)
        itemGate:get("btnBlitz"):get("textCostNum"):text(sceneCfg.staminaCost * selv.val)
        bind.click(list, btnBlitz, { method = functools.partial(list.onBlitzBtnClick, v.gateID, selv.val) })
    end

    local binds = {
        class = "sort_menus3",
        props = {
            data = sortDatas,
            expandUp = false,
            btnType = 4,
            showSelected = showLeftSelected,
            btnClick = onSortMenusBtnClick,
            onNode = function(nodeSort)
                nodeSort:xy(-1120, -487):z(18)
            end,
        },
    }
    bind.extend(list, itemGate:get("panelSelect"), binds)
    itemGate:get("panelSelect"):hide()

    if v.isCompleted == false then
        itemGate:get("btnBlitz"):hide()
        itemGate:get("btnBlitzMax"):hide()
        itemGate:get("panelSelect"):hide()
        itemGate:get("panelTimes"):hide()
        cache.setShader(itemGate:get("gateName"), false, "hsl_gray")
        cache.setShader(itemGate:get("bg_title"), false, "hsl_gray")
        cache.setShader(itemGate:get("bg_item"), false, "hsl_gray")
    else
        cache.setShader(itemGate:get("gateName"), false, "normal")
        cache.setShader(itemGate:get("bg_title"), false, "normal")
        cache.setShader(itemGate:get("bg_item"), false, "normal")
    end

end

local function initBlitzGateNotFinishItem(list, node, v)
    local itemGateStar = list.itemGateStar:clone()
    itemGateStar:addTo(node, 100, "itemGateStar")
    itemGateStar:xy(0, 0)
    local sceneCfg = csv.scene_conf[v.gateID]
    itemGateStar:get("gateName"):text(v.name)
    if sceneCfg.isBoss then
        itemGateStar:get("spePanel"):get("imgTopBG"):texture(sceneCfg.bossAvatarBg)
        itemGateStar:get("spePanel"):get("imgIcon"):texture(sceneCfg.icon)
    end
    for i = 1, 3 do
        if i <= v.star then
            itemGateStar:get("imgStar" .. i):get("star"):visible(true)
        else
            itemGateStar:get("imgStar" .. i):get("star"):visible(false)
        end
    end
    bind.click(list, itemGateStar:get("btnGoto"), { method = functools.partial(list.onJumpToNightmareGateClick, v.chapterID, v.gateID) })
end

Blitz.RESOURCE_FILENAME = "battel_blitz.json"
Blitz.RESOURCE_BINDING = {
    ["btnExit"] = {
        varname = "btnExit",
        binds = {
            event = "touch",
            class = "listview",
            methods = { ended = bindHelper.self("onClose") },
        },
    },
    ["iconItem"] = "iconItem",
    ["itemGate"] = "itemGate",
    ["itemGate.btnBlitz.textCostNum"] = {
        binds = {
            {
                event = "effect",
                data = { color = cc.c4b(102, 189, 106, 255) }
            },
        }
    },
    ["itemChapter"] = "itemChapter",
    ["listChapter"] = {
        varname = "listChapter",
        binds = {
            event = "extend",
            class = "listview",
            props = {
                data = bindHelper.self("listChapterData"),
                item = bindHelper.self("itemChapter"),
                selectChapterID = bindHelper.self("selectChapterID"),
                margin = 5,
                onItem = function(list, node, k, v)
                    if v.isParent then
                        node:get("bg"):texture(chapterParentBg)
                        if v.isSelect then
                            node:get("arrow"):texture(arrowUp)
                        else
                            node:get("arrow"):texture(arrowDown)
                        end
                        node:get("name"):setTextColor(cc.c4b(101, 63, 0, 255))
                        bind.click(list, node, { method = functools.partial(list.onParentChapterClick, v.chapterType) })

                    else
                        node:get("arrow"):hide()
                        node:get("bg"):texture(chapterChildrenBg)
                        if v.chapterID == list.selectChapterID:read() then
                            list.onChapterClick(node, v)
                        end
                        bind.click(list, node, { method = functools.partial(list.onChapterClick, node, v) })
                    end

                    node:get("name"):text(v.name)
                end,
            },
            handlers = {
                onParentChapterClick = bindHelper.self("onParentChapterClick"),
                onChapterClick = bindHelper.self("onChapterClick"),
            },
        },
    },
    ["right.title"] = "title",
    ["itemGatePanel"] = "itemGatePanel",
    ["itemGateStar"] = "itemGateStar",
    ["right.listGate"] = {
        varname = "listGate",
        binds = {
            event = "extend",
            class = "listview2",
            props = {
                data = bindHelper.self("listGateData"),
                item = bindHelper.self("itemGatePanel"),
                itemGate = bindHelper.self("itemGate"),
                itemGateStar = bindHelper.self("itemGateStar"),
                iconItem = bindHelper.self("iconItem"),
                margin = 30,
                onItem = function(list, node, k, v)
                    if v.tabType == tabTypeNotFinish then
                        initBlitzGateNotFinishItem(list, node, v)
                    else
                        initBlitzGateItem(list, node, v)
                    end
                end,
                asyncPreload = 7,
            },
            handlers = {
                onBlitzBtnClick = bindHelper.self("onBlitzBtnClick"),
                onTimesBtnClick = bindHelper.self("onTimesBtnClick"),
                onJumpToNightmareGateClick = bindHelper.self("onJumpToNightmareGateClick"),
            },
        },
    },
}

function Blitz:onCreate(params)
    params = params or {}
    if params.chapterType == nil then
        params.chapterType = chapterEasy
    end
    self.chapterType = params.chapterType

    self:initModel()
    self:loadData(self.chapterType)
end

function Blitz:initModel()
    self.listChapterData = idlers.newWithMap({})
    self.listGateData = idlers.newWithMap({})
    self.selectChapterID = idler.new(0)

    self.buyHerogateTimes = idlereasy.assign(gGameModel.daily_record:getIdler("buy_herogate_times"), self.buyHerogateTimes)
    self.gateTimes = idlereasy.assign(gGameModel.daily_record:getIdler("gate_times"), self.gateTimes)
end

function Blitz:loadData(selectChapter)
    self.gateStar = idlereasy.assign(gGameModel.role:getIdler("gate_star"), self.gateStar)
    self:loadListChapter(selectChapter)
    self.title:setText(gLanguageCsv.blitzDescription)
end

function Blitz:getChaptersByType(chapterType)
    if not self.chapterIdtab then
        self.chapterIdtab = {}
        for i, v in orderCsvPairs(csv.world_map) do
            if v.chapterType then
                if not self.chapterIdtab[v.chapterType] then
                    self.chapterIdtab[v.chapterType] = {}
                end
                table.insert(self.chapterIdtab[v.chapterType], i)
            end
        end
        for k, v in pairs(self.chapterIdtab) do
            table.sort(v)
        end
    end

    return self.chapterIdtab[chapterType]
end

function Blitz:getChaptersNotFishedByType(chapterType)
    local chapterIds = self:getChaptersByType(chapterType)
    local gateStar = self.gateStar:read()
    local chapterIdsNotFished = {}

    for _, chapterID in ipairs(chapterIds) do
        local chapterCfg = csv.world_map[chapterID]
        local isFinished = true
        for _, v in ipairs(chapterCfg.seq) do
            local sceneCfg = csv.scene_conf[v]
            if sceneCfg.isBoss then
                local gateState = gateStar[v]
                --NOTE: enable for testing
                --if gateState ~= nil and gateState.star > 1 and gateState.star <= 3 then
                if gateState ~= nil and gateState.star > 1 and gateState.star < 3 then
                    isFinished = false
                    break
                end
            end
        end
        if not isFinished then
            table.insert(chapterIdsNotFished, chapterID)
        end
    end
    return chapterIdsNotFished
end

function Blitz:getBossSceneNotFinish(chapterID)
    local gateStar = self.gateStar:read()
    local chapterCfg = csv.world_map[chapterID]
    local notFinishedGate = {}
    self.selectChapterID:set(chapterID)

    for i, v in ipairs(chapterCfg.seq) do
        local sceneCfg = csv.scene_conf[v]
        if sceneCfg.isBoss then
            local gateState = gateStar[v]
            --NOTE: enable for testing
            --if gateState ~= nil and gateState.star > 1 and gateState.star <= 3 then
            if gateState ~= nil and gateState.star > 1 and gateState.star < 3 then
                table.insert(notFinishedGate, {
                    tabType = tabTypeNotFinish,
                    chapterID = chapterID,
                    gateID = v,
                    name = string.format(gLanguageCsv.roundNumber, i) .. " " .. sceneCfg.sceneName,
                    star = gateState.star,
                })
            end
        end
    end
    self.listGateData:update(notFinishedGate)
end

function Blitz:onParentChapterClick(_, chapterType)
    self:loadListChapter(chapterType)
end

function Blitz:loadListChapter(chapterType)
    if chapterType == chapterNightmare or chapterType == tabTypeNotFinish then
        self:loadListChapterNightMare(chapterType)
    else
        self:loadListChapterNormal(chapterType)
    end
end

function Blitz:loadListChapterNormal(chapterType)
    local listChapterData = {}
    local isSelectChapter = false
    if chapterType == self.curChapterType then
        self.curChapterType = ""
        chapterType = ""
    end
    local chapterHeroUnlock = dataEasy.getListenUnlock(gUnlockCsv.heroGate)

    table.insert(listChapterData, {
        chapterType = chapterEasy,
        isParent = true,
        name = chapterTypes[chapterEasy].name,
        isSelect = chapterType == chapterEasy,
    })
    if chapterType == chapterEasy then
        self.curChapterType = chapterEasy
        local chapters = self:getChaptersByType(chapterEasy)
        for i, v in ipairs(chapters) do
            local chapterCfg = csv.world_map[v]
            table.insert(listChapterData, {
                chapterID = v,
                chapterType = chapterEasy,
                name = string.format(gLanguageCsv.chapterWithNumber, i) .. " " .. chapterCfg.name,
            })
        end
        if #chapters > 0 then
            self:loadListGate(chapters[1])
            isSelectChapter = true
        end
    end

    if chapterHeroUnlock:read() == true then
        table.insert(listChapterData, {
            chapterType = chapterHard,
            isParent = true,
            name = chapterTypes[chapterHard].name,
            isSelect = chapterType == chapterHard,
        })
        if chapterType == chapterHard then
            self.curChapterType = chapterHard
            local chapters = self:getChaptersByType(chapterHard)
            for i, v in ipairs(chapters) do
                local chapterCfg = csv.world_map[v]
                table.insert(listChapterData, {
                    chapterID = v,
                    chapterType = chapterHard,
                    name = string.format(gLanguageCsv.chapterWithNumber, i) .. " " .. chapterCfg.name,
                })
            end
            if #chapters > 0 then
                self:loadListGate(chapters[1])
                isSelectChapter = true
            end
        end
    end

    self.listChapterData:update(listChapterData)
    if isSelectChapter == false then
        self.lastNode = nil
    end
    self.isSelectChapter = isSelectChapter

end

function Blitz:loadListChapterNightMare(tabType)
    print("Blitz:loadListChapterNightMare", tabType)
    local listChapterData = {}
    local isSelectChapter = false
    local chapterNightmareGate = dataEasy.getListenUnlock(gUnlockCsv.nightmareGate)

    table.insert(listChapterData, {
        chapterType = tabTypeNotFinish,
        isParent = true,
        name = chapterTypes[tabTypeNotFinish].name,
        isSelect = tabType == tabTypeNotFinish,
    })
    if tabType == tabTypeNotFinish then
        self.curChapterType = tabTypeNotFinish
        local chapters = self:getChaptersNotFishedByType(chapterNightmare)
        for i, v in ipairs(chapters) do
            local chapterCfg = csv.world_map[v]
            table.insert(listChapterData, {
                chapterID = v,
                chapterType = tabTypeNotFinish,
                name = string.format(gLanguageCsv.chapterWithNumber, i) .. " " .. chapterCfg.name,
            })
        end
        if #chapters > 0 then
            self:getBossSceneNotFinish(chapters[1])
            isSelectChapter = true
        end
    end

    if chapterNightmareGate:read() == true then
        table.insert(listChapterData, {
            chapterType = chapterNightmare,
            isParent = true,
            name = chapterTypes[chapterNightmare].name,
            isSelect = tabType == chapterNightmare,
        })
        if tabType == chapterNightmare then
            self.curChapterType = chapterNightmare
            local chapters = self:getChaptersByType(chapterNightmare)
            if #chapters > 0 then
                self:loadListNightmareDropInfo(chapters)
                isSelectChapter = true
            end
        end
    end

    self.listChapterData:update(listChapterData)
    if isSelectChapter == false then
        self.lastNode = nil
    end
    self.isSelectChapter = isSelectChapter
end

function Blitz:onChapterClick(_, node, v)
    if self.isSelectChapter and self.lastNode then
        self.lastNode:get("bg"):show()
        self.lastNode:get("bgSelect"):hide()
    end

    self.lastNode = node
    node:get("bg"):hide()
    node:get("bgSelect"):show()
    if v.chapterType == tabTypeNotFinish then
        self:getBossSceneNotFinish(v.chapterID)
    elseif v.chapterType == chapterNightmare then
        self:loadListChapterNightMare(v.chapterType)
    else
        self:loadListGate(v.chapterID)
    end
end

function Blitz:loadListGate(chapterID)
    print("Blitz:loadListGate", chapterID)
    local listGateData = {}
    self.selectChapterID:set(chapterID)
    local gateStar = self.gateStar:read()
    local chapterCfg = csv.world_map[chapterID]
    --if chapterCfg == nil then
    --    return
    --end

    for i, v in ipairs(chapterCfg.seq) do
        local gateState = gateStar[v]
        local star = 0
        if gateState ~= nil then
            star = gateState.star
        end

        local gateTimes = self.gateTimes:read()

        local sceneCsv = csv.scene_conf[v]
        if sceneCsv ~= nil then
            local dropIDs = getDropItemData(v)
            local dayChallengeMax = sceneCsv.dayChallengeMax
            local surplusTimes = dayChallengeMax - (gateTimes[v] or 0)

            table.insert(listGateData, {
                tabType = chapterCfg.chapterType,
                gateID = v,
                chapterID = chapterID,
                name = string.format(gLanguageCsv.levelChapterId, i) .. " " .. sceneCsv.sceneName,
                dropIDs = dropIDs,
                isCompleted = star > 0,
                surplusTimes = surplusTimes,
                dayChallengeMax = dayChallengeMax,
            })
        end
    end

    self.listGateData:update(listGateData)
end

function Blitz:loadListNightmareDropInfo(chapters)
    self.lastNode = nil
    local listGateData = {}
    for _, chapterID in ipairs(chapters) do
        local gateStar = self.gateStar:read()
        local chapterCfg = csv.world_map[chapterID]
        for i, v in ipairs(chapterCfg.seq) do
            local gateState = gateStar[v]
            local star = 0
            if gateState ~= nil then
                star = gateState.star
            end
            local sceneCsv = csv.scene_conf[v]

            if star >= 3 and sceneCsv ~= nil then
                local dropIDs = getDropItemData(v)
                if sceneCsv.isNightmareBlitz and dropIDs ~= nil and #dropIDs > 0 then
                    local gateTimes = self.gateTimes:read()
                    local dayChallengeMax = sceneCsv.dayChallengeMax
                    local surplusTimes = dayChallengeMax - (gateTimes[v] or 0)

                    table.insert(listGateData, {
                        tabType = chapterCfg.chapterType,
                        chapterID = chapterID,
                        gateID = v,
                        name = getL10nStr(sceneCsv, "nightmareBlitzName") or sceneCsv.nightmareBlitzName,
                        dropIDs = dropIDs,
                        isCompleted = star > 0,
                        surplusTimes = surplusTimes,
                        dayChallengeMax = dayChallengeMax,
                    })
                end
            end
        end
    end

    self.listGateData:update(listGateData)
end

function Blitz:checkSweep(gateID, blitzNum)
    print("gateID: ", gateID, "blitzNum: ", blitzNum)
    local staminaCost = csv.scene_conf[gateID].staminaCost
    local curStamina = dataEasy.getStamina()
    if curStamina < staminaCost * blitzNum then
        gGameUI:stackUI("common.gain_stamina")
        return false
    end

    return true
end

function Blitz:onTimesBtnClick(node, gateID)
    local sceneCsv = csv.scene_conf[gateID]
    local buyTimeMax = gVipCsv[gGameModel.role:read("vip_level")].buyHeroGateTimes
    local buyHerogateTimes = self.buyHerogateTimes:read()
    local gateTimes = self.gateTimes:read()

    if (buyHerogateTimes[gateID] or 0) >= buyTimeMax then
        gGameUI:showTip(gLanguageCsv.herogateBuyMax)
        return
    end
    local dayChallengeMax = sceneCsv.dayChallengeMax
    local surplusTimes = dayChallengeMax - (gateTimes[gateID] or 0)
    if surplusTimes > 0 then
        gGameUI:showTip(gLanguageCsv.haveChallengeTimesUnused)
        return
    end

    local roleRmb = gCostCsv.herogate_buy_cost[(buyHerogateTimes[gateID] or 0) + 1]
    if gGameModel.role:read("rmb") < roleRmb then
        uiEasy.showDialog("rmb")
        return
    end

    local strs = {
        "#C0x5b545b#" .. string.format(gLanguageCsv.resetNumberEliteLevels1, gCostCsv.herogate_buy_cost[(buyHerogateTimes[gateID] or 0) + 1]),
        "#C0x5b545b#" .. string.format(gLanguageCsv.resetNumberEliteLevels2, buyHerogateTimes[gateID] or 0, buyTimeMax)
    }
    gGameUI:showDialog({ content = strs, cb = function()
        gGameApp:requestServer("/game/role/hero_gate/buy", function()
            gGameUI:showTip(gLanguageCsv.resetSuccess)
            if self.chapterType == chapterNightmare or self.chapterType == tabTypeNotFinish then
                self:loadListChapterNightMare(self.chapterType)
            else
                self:loadListGate(self.selectChapterID:read())
            end

        end, gateID)
    end, btnType = 2, isRich = true, dialogParams = { clickClose = false } })
end

function Blitz:onJumpToNightmareGateClick(node, chapterID, gateID)
    gGameUI:stackUI("city.gate.nightmare_view", nil, { full = true }, { chapterID = chapterID, gateID = gateID })
end

function Blitz:onBlitzBtnClick(_, gateID, blitzNum)
    self:onSweepBtn(gateID, blitzNum)
end

function Blitz:onSweepBtn(gateID, blitzNum)
    if not self:checkSweep(gateID, blitzNum) then
        return
    end
    local oldCapture = gGameModel.capture:read("limit_sprites")
    local roleLv = gGameModel.role:getIdler("level"):read()
    gGameApp:requestServer("/game/saodang", function(tb)
        local items = tb.view.result
        table.insert(items, { exp = 0, items = tb.view.extra, isExtra = true })
        gGameUI:stackUI("city.gate.sweep", nil, nil, {
            sweepData = items,
            oldRoleLv = roleLv,
            cb = self:createHandler("onSweepBtn", gateID, blitzNum),
            checkCb = self:createHandler("checkSweep", gateID, blitzNum),
            hasExtra = true,
            from = "gate",
            oldCapture = oldCapture,
            isDouble = dataEasy.isGateIdDoubleDrop(gateID),
            gateId = gateID,
            catchup = tb.view.catchup
        })
        if self.chapterType == chapterNightmare or self.chapterType == tabTypeNotFinish then
            self:loadListChapterNightMare(chapterNightmare)
        else
            self:loadListGate(self.selectChapterID:read())
        end
    end, gateID, blitzNum)
end

return Blitz