--local Map1 = require "app.views.city.gate.mod.map_1"


local ViewBase = cc.load("mvc").ViewBase
local Map1 = class("Map1", ViewBase)

Map1.RESOURCE_FILENAME = "gate_easy.json"
Map1.RESOURCE_BINDING = {
    ["map"] = {
        varname = "map",
        binds = {
            event = "touch",
            scaletype = 0,
            methods = {
                ended = bindHelper.self("onEnd"),
                began = bindHelper.self("onBegan"),
                moved = bindHelper.self("onMove")
            }
        },
    },
    ["panelRight"] = "panelRight",
    ["panelMiniGame"] = "panelMiniGame",
    ["panelMiniGame.item1"] = {
        varname = "cauCaMap1",
        binds = {
            event = "touch",
            scaletype = 1,
            methods = { ended = bindHelper.self("onCauCaMap1") }
        },
    },
    
    ["panelRight.panelGoto.titleStart"] = {
        binds = {
            {
                event = "effect",
                data = { color = cc.c4b(230, 202, 136, 255), outline = { color = cc.c4b(81, 59, 57, 255) } }
            },
        }
    },
    ["panelRight.panelGoto"] = {
        varname = "panelGoto",
        binds = {
            event = "touch",
            scaletype = 1,
            methods = { ended = bindHelper.self("onClickGoto") }
        },
    },
    ["panelCity"] = "panelCity",
    ["map.CauCa"] =  {
        varname = "CauCa",
        binds = {
            event = "touch",
            methods = { ended = bindHelper.self("onCauCa") }
        },
    },
    ["map.CauCa.img"] = "CauCaimg",
    ["map.CauCa.img.textName"] = {
        binds = {
            {
                event = "effect",
                data = { color = cc.c4b(230, 202, 136, 255), outline = { color = cc.c4b(81, 59, 57, 255) } }
            },
        }
    },
}

local MAP_TYPE = {
    normal = 1,
    hero = 2,
    nightmare = 3,
}

local CHAPTER_NUM = {
    [MAP_TYPE.normal] = 10,
    [MAP_TYPE.hero] = 110,
    [MAP_TYPE.nightmare] = 210,
}



local isHeroRunning = false
local isMove = false
local oneTouchData
local oneTouchMove = false
local oldx, oldy = 0, 0



function Map1:goToHero()
    self.map:runAction(cc.Sequence:create( cc.MoveTo:create(0.5, cc.p(-9074, -5246))))
end

function Map1:onCreate(chapterType)
    self:enableSchedule()
    self:createHeroSprite()
    self:initModel(chapterType)
    self:loadData()
    gGameUI:setMultiTouches(true)
    self.mapDots = {}

    oldx, oldy = self:adaptMapPos(oldx, oldy)
    self:adaptUI()
    self.map:setPosition(cc.p(oldx, oldy))
    self:jumpToTargetInProcessCity()


    local function run(x, y)
        return cc.RepeatForever:create(
            cc.Sequence:create(
                cc.DelayTime:create(0.1),
                cc.MoveTo:create(0.8, cc.p(x, y + 20)),
                cc.DelayTime:create(0.1),
                cc.MoveTo:create(0.8, cc.p(x, y))
            )
        )
    end
    local x,y = self.CauCaimg:xy()
    self.CauCaimg:runAction(run(x, y))

	idlereasy.when(self.level, function(obj, level)
		local isUnlock = dataEasy.isUnlock(gUnlockCsv.fishing)
	    self.CauCa:visible(isUnlock == true)
        self.panelMiniGame:visible(isUnlock == true)
        
	end)

end

function Map1:adaptUI()
    adapt.centerWithScreen({ "left", nil, false }, { "right", nil, false }, nil, {
        { self.panelRight, "pos", "right" },
        { self.panelMiniGame, "pos", "right" },
    })

    
end
function Map1:onCauCa()
    gGameApp:requestServer("/game/fishing/prepare",function (tb)
        if 1 == game.FISHING_GAME then
            gGameApp:requestServer("/game/cross/fishing/rank",function (tb)
                gGameUI:stackUI("city.adventure.fishing.view", nil, {full = true}, 1, tb.view)
            end)
        else
            gGameUI:stackUI("city.adventure.fishing.view", nil, {full = true}, 1)
        end
    end, "scene", 1)
end

function Map1:jumpToTargetInProcessCity()
    local lastCities = nil
    for i, v in ipairs(self.cities) do
        local chapterInfo = self.chapterInfo[v.chapterID]
        if chapterInfo ~= nil and chapterInfo.isInProcess then
            if chapterInfo.passedGateCount > 0 then
                self:targetCity(v.chapterID, true)
                return
            end
            if lastCities ~= nil then
                self:targetCity(lastCities.chapterID, true)
                return
            end
        end
        lastCities = v
    end
    if #self.cities > 0 then
        self:targetCity(self.cities[1].chapterID, true)
    end
end

function Map1:initPanelGoTo()
    local chapterCount = #self.chapterOpen
    if chapterCount <= 0 then
        chapterCount = 1
    end
    self.panelGoto:get("name"):text(string.format("%s %s", gLanguageCsv.currentChapter, chapterCount))
end

function Map1:onClickGoto()
    local gateStar = self.gateStar:read()

    for i, v in ipairs(self.chapterOpen) do
        local passedGateCount = 0
        for _, gateID in ipairs(csv.world_map[v].seq) do
            local gateState = gateStar[gateID]
            local star = 0
            if gateState ~= nil then
                star = gateState.star
            end
            if star > 0 then
                passedGateCount = passedGateCount + 1
            end
        end

        if passedGateCount < #csv.world_map[v].seq then
            local city = self:getCityByChapterID(v)
            self:moveMapToCity(city)
            self:selectCity(i, v)
            return
        end
    end
end

function Map1:goToAi(id)
    local gateStar = self.gateStar:read()

    for i, v in ipairs(self.chapterOpen) do
        if v == id then
            local city = self:getCityByChapterID(v)
            self:moveMapToCity(city)
            self:selectCity(i, v)
            break
        end
    end
end

function Map1:getCityByChapterID(chapterID)
    for i, v in ipairs(self.cities) do
        if v.chapterID == chapterID then
            return v.node
        end
    end

    return nil
end

function Map1:initModel(chapterType)
    --oldx, oldy = -4358, -189
    self.isCanSelect = true
    self.selChapterID = nil
    self.chapterType = chapterType
    oldx, oldy = 0, 0
    self.mapScale = 1.6
    --self.mapScale = 1
    self.map:scale(self.mapScale)

    self.mapOpen = gGameModel.role:getIdler("map_open")
    self.level = gGameModel.role:getIdler("level") -- 等级
    self.gateStar = idlereasy.new()

    self.chapterOpen = {}
    self.chapterInfo = {}

    self.cities = {}

    local example = self.map:get("exampleCity")
    if example then
        example:hide()
    end
end

function Map1:initChapterOpen()
    local mapOpen = self.mapOpen:read()

    local chapterOpen = {}
    for i, v in ipairs(mapOpen) do
        local worldCfg = csv.world_map[v]
        local chapterType = worldCfg.chapterType
        if self.chapterType == chapterType then
            table.insert(chapterOpen, v)
        end
    end

    table.sort(chapterOpen)
    if #chapterOpen > 0 then
        local lastedChapterID = chapterOpen[#chapterOpen]
        if self:isFinishChapter(lastedChapterID) then
            local nextChapter = self:getNexChapter(lastedChapterID)
            if nextChapter ~= nil then
                table.insert(chapterOpen, nextChapter.id)
            end
        end
    end
    --
    -- OPEN_ALL_CHAPTER
    --for i, v in orderCsvPairs(csv.world_map) do
    --    if v.chapterType == self.chapterType then
    --        table.insert(chapterOpen, i)
    --    end
    --end

    self.chapterOpen = chapterOpen
end

function Map1:loadData()
    self.gateStar = idlereasy.assign(gGameModel.role:getIdler("gate_star"), self.gateStar)
    self:initChapterOpen()
    self:loadChaptersInfo()
    self:loadCities()
    self:initPanelGoTo()
    self:refreshRedHint()
end

function Map1:createHeroSprite()
    self.figureID = gGameModel.role:getIdler("figure")
    local chibiSpine = gRoleFigureCsv[self.figureID:read()].resChibiSpine
    if chibiSpine == nil then
        return
    end

    self.sprite = widget.addAnimationByKey(self.map, string.format(chibiSpine), "hero_sprite", "idle", 10)
                        :xy(500, 500)
                        :scale(1)
    self.sprite:play("idle", true)

    --self.sprite:setFlippedX(true)
end

function Map1:loadPath(from, to, way)
    local fromCity = csv.world_map[from]
    local toCity = csv.world_map[to]
    if fromCity == nil or toCity == nil then
        return
    end
    if way == nil then
        return
    end

    for i, v in ipairs(way) do
        widget.addAnimationByKey(self.map, "effect3q/point/pointmap_loop.skel", "point" .. v[1] .. v[2], "pointmap_loop",
                5):xy(v[1], v[2]):scale(0.7)
    end
end

function Map1:loadChaptersInfo()
    local chaptersInfo = {}
    local gateStar = self.gateStar:read()
    local isFirstProcess = true
    for i, v in ipairs(self.chapterOpen) do
        local seqInfo = {}
        local passedGateCount = 0
        for _, gateID in ipairs(csv.world_map[v].seq) do
            local gateState = gateStar[gateID]
            local star = 0
            if gateState ~= nil then
                star = gateState.star
            end
            if star > 0 then
                passedGateCount = passedGateCount + 1
            end

            table.insert(seqInfo, {
                gateID = gateID,
                star = star,
                isCompleted = star > 0,
            })
        end

        local p = {
            chapterID = v,
            passedGateCount = passedGateCount,
            isInProcess = isFirstProcess and passedGateCount < #csv.world_map[v].seq,
            seqInfo = seqInfo,
        }
        if isFirstProcess and p.isInProcess then
            isFirstProcess = false
        end

        chaptersInfo[v] = p
        if csv.world_map[v].ways ~= nil then
            for to, w in csvPairs(csv.world_map[v].ways) do
                local isFound = false
                for _, v2 in ipairs(self.chapterOpen) do
                    if to == v2 then
                        isFound = true
                        break
                    end
                end
                if isFound == true then
                    self:loadPath(v, to, w)
                end
            end
        end
    end
    self.chapterInfo = chaptersInfo
end

function Map1:loadCities()
    local currentCity = nil
    local lastedChapterID = nil
    local cities = {}
    for i, v in ipairs(self.chapterOpen) do
        currentCity = self:createCityNode(i, v)
        lastedChapterID = v
        table.insert(cities, { node = currentCity, chapterID = v })
    end

    self.cities = cities
end

function Map1:createCityNode(i, chapterID)
    local currentCity = nil
    local isCreated = false
    for _, v2 in ipairs(self.cities) do
        if v2.chapterID == chapterID then
            currentCity = v2.node
            isCreated = true
            break
        end
    end
    if isCreated == false then
        currentCity = self.panelCity:clone()
        -- TODO: remove this code, this code for testing
        --currentCity:setTouchEnabled(false)
        local chapterCfg = csv.world_map[chapterID]
        if chapterCfg.location == nil then
            chapterCfg.location = { x = 0, y = 0 }
        end
        if i == #self.chapterOpen then
            currentCity:name("city_target")
            local citySize = currentCity:size()

            self:setSelEffect(currentCity, cc.p(citySize.width / 2, 20))
        else
            currentCity:name("city" .. i)
        end

        currentCity:visible(true)
        currentCity:get("img"):texture(chapterCfg.cityImage)
        currentCity:xy(chapterCfg.location.x, chapterCfg.location.y)
        local cityName = currentCity:get("name")
        cityName:text(chapterCfg.name)
        local x, y = currentCity:get("bg"):getPosition()
        cityName:setPosition(cc.p(x, y + 5))
        text.addEffect(currentCity:get("name"),
                { color = cc.c4b(255, 242, 236, 255), outline = { color = cc.c4b(81, 59, 57, 255) } })
        bind.click(self, currentCity, {
            scaletype = 1,
            method = function()
                self:selectCity(i, chapterID)
            end
        })

        currentCity:addTo(self.map)
    end

    return currentCity
end

function Map1:refreshRedHint()
    for i, v in ipairs(self.cities) do
        local showRedHint = self:checkBoxCanReceived(v.chapterID)
        if showRedHint then
            local redHint = v.node:get("redHint")
            if redHint then
               
                redHint:show()
            else
               
                redHint = cc.Sprite:create("3q/icon_hongdian_s.png")
                redHint:setPosition(cc.p(130, 150))
                redHint:scale(0.7)
                redHint:addTo(v.node, 999, "redHint")
            end
        else
            local redHint = v.node:get("redHint")
            if redHint then
               
                redHint:hide()
            else
                
            end
        end
    end
end

function Map1:checkBoxCanReceived(chapterID)
    local chapterCfg = csv.world_map[chapterID]
    local gateStar = self.gateStar:read()

    for _, gateID in ipairs(chapterCfg.seq) do
        local gateState = gateStar[gateID]
        if gateState and gateState.chest == 1 then
            return true
        end
    end

    return false
end

function Map1:isFinishChapter(chapterID)
    local chapterCfg = csv.world_map[chapterID]

    local gateStar = self.gateStar:read()
    for _, gateID in ipairs(chapterCfg.seq) do
        local gateState = gateStar[gateID]
        if gateState == nil or gateState.star < 1 then
            return false
        end
    end

    return true
end

function Map1:getNexChapter(chapterID)
    local isNextChapter = false
    for i, v in orderCsvPairs(csv.world_map) do
        if isNextChapter then
            return v
        end
        if v.chapterType == self.chapterType and v.id == chapterID then
            isNextChapter = true
        end
    end
end

function Map1:targetCity(chapterID, runHero, cb)
    local city = nil
    for i, v in ipairs(self.cities) do
        if v.chapterID == chapterID then
            city = v.node
            break
        end
    end

    if city == nil then
        return
    end
    self:moveMapToCity(city)

    if runHero == true then
        self:runHeroSprite(self.selChapterID, chapterID, cb)
        self.selChapterID = chapterID
    end
end

function Map1:moveMapToCity(city)
    if city == nil then
        return
    end

    local x, y = city:getPosition()
    local citySize = city:size()

    --self:setSelEffect(city, cc.p(citySize.width / 2, 20))
    local boundary = self.map:getBoundingBox()
    local size = self.map:size()
    local scaleHeight = (boundary.height / size.height)
    local scaleWidth = (boundary.width / size.width)
    x = x * scaleWidth
    y = y * scaleHeight

    x = x * -1
    y = y * -1

    x = x + display.sizeInView.width / 2
    y = y + display.sizeInView.height / 2
    oldx, oldy = self:adaptMapPos(x, y)
    self.map:setPosition(cc.p(oldx, oldy))
end

function Map1:getWay(fromChapter, toChapter)
    if fromChapter.ways ~= nil and fromChapter.ways[toChapter.id] ~= nil then
        local way = dataEasy.cloneArray(fromChapter.ways[toChapter.id])
        table.insert(way, self:heroPositionInChapter(toChapter.id))
        return way
    end
    if toChapter.ways ~= nil and toChapter.ways[fromChapter.id] ~= nil then
        local way = dataEasy.reverseArray(toChapter.ways[fromChapter.id])
        table.insert(way, self:heroPositionInChapter(toChapter.id))
        return way
    end
    return nil
end

function Map1:runHeroSprite(fromChapterID, toChapterID, cb)
    if isHeroRunning then
        return
    end
    isHeroRunning = true
    local toChapter = csv.world_map[toChapterID]
    if fromChapterID == toChapterID or toChapter == nil then
        return
    end

    local fromChapter = csv.world_map[fromChapterID]
    if fromChapter == nil then
        self:heroTeleport(toChapterID, cb)
        isHeroRunning = false
        return
    end

    local way = self:getWay(fromChapter, toChapter)
    if way == nil then
        self:heroTeleport(toChapterID, cb)
        isHeroRunning = false
        return
    end

    local idx = 1

    local function onMoveDone()
        if idx < #way then
            idx = idx + 1
            self:runHeroTo(way[idx], onMoveDone)
            return
        end

        self.sprite:play("idle", true)
        isHeroRunning = false
        if cb then
            cb()
        end
    end

    self:runHeroTo(way[idx], onMoveDone)
end

function Map1:getPreviousChapter(chapterID)
    for i, v in ipairs(self.chapterOpen) do
        local cfg = csv.world_map[v]
        if cfg.ways ~= nil and cfg.ways[chapterID] ~= nil then
            return v
        end
    end
end

function Map1:heroPositionInChapter(toChapterID)
    local chapter = csv.world_map[toChapterID]
    local x, y = chapter.location.x, chapter.location.y
    y = y - 50
    local chapterCfg = csv.world_map[toChapterID]
    local gateStar = self.gateStar:read()
    for _, gateID in ipairs(chapterCfg.seq) do
        local gateState = gateStar[gateID]
        local star = 0
        if gateState ~= nil then
            star = gateState.star
        end
        if star > 0 then
            return { x, y }
        end
    end

    local pChapterID = self:getPreviousChapter(toChapterID)
    local pChapterCfg = csv.world_map[pChapterID]
    if pChapterCfg == nil or pChapterCfg.ways == nil then
        return { x, y }
    end

    local ways = pChapterCfg.ways[toChapterID]
    if ways ~= nil and #ways > 1 then
        return ways[#ways]
    end

    return { x, y }
end

function Map1:runHeroTo(des, doneCb)
    local cx, cy = self.sprite:getPosition()
    local dx = des[1] - cx
    local dy = des[2] - cy

    local distance = math.sqrt(dx * dx + dy * dy)
    local speed = 500 -- Đơn vị: pixel/giây
    local duration = distance / speed

    self.sprite:play("run")
    if cx < des[1] then
        self.sprite:setFlippedX(false)
    else
        self.sprite:setFlippedX(true)
    end
    if dy > 0 and math.abs(dy) > math.abs(dx) * 3 then
        self.sprite:play("run_b")
    end

    transition.executeSequence(self.sprite, true)
              :moveTo(duration, des[1], des[2])
              :func(doneCb)
              :done()
end

function Map1:heroTeleport(toChapterID, cb)
    local toChapter = csv.world_map[toChapterID]
    if toChapter == nil or toChapter.location == nil then
        return
    end
    local pos = self:heroPositionInChapter(toChapterID)
    self.sprite:xy(pos[1], pos[2])
    if not self.teleport then
        self.teleport = widget.addAnimationByKey(self.sprite, "effect3q/teleport/teleport.skel", "teleport_effect",
                "teleport", 9):xy(0, 30):scale(0.5)
    else
        self.teleport:play("teleport", false)
    end

    if cb then
        performWithDelay(self.map, function()
            cb()
        end, 0.8)
    end
end

-- 判断查询的章节 是否开启了前置条件 没开则检查是否需要开启前置条件
function Map1:checkPreGateIsPass(curChapterCsvId, isTips)
    -- 检查章节 开启条件
    local preChapterId = gNightmareForCsv[curChapterCsvId]
    if preChapterId then
        local preMapCsv = csv.world_map[preChapterId]
        if not itertools.include(self.chapterOpen, preChapterId) then
            local fmt = {
                [MAP_TYPE.normal] = gLanguageCsv.checkPreGateIsPass,
                [MAP_TYPE.hero] = gLanguageCsv.pleasePassGateDifficult,
                [MAP_TYPE.nightmare] = gLanguageCsv.pleasePassGateNightMare,
            }
            if isTips then
                gGameUI:showTip(string.format(fmt[preMapCsv.chapterType], preChapterId - CHAPTER_NUM[preMapCsv.chapterType]))
            end
            return false
        end
    else
        local curMapType = self.chapterType
        return curMapType ~= MAP_TYPE.nightmare
    end

    return true
end

function Map1:checkGateIsPass(curChapterCsvId)
    if not itertools.include(self.chapterOpen, curChapterCsvId) then
        local fmt = {
            [MAP_TYPE.normal] = gLanguageCsv.checkPreGateIsPass,
            [MAP_TYPE.hero] = gLanguageCsv.pleasePassGateDifficult,
            [MAP_TYPE.nightmare] = gLanguageCsv.pleasePassGateNightMare,
        }
        gGameUI:showTip(string.format(fmt[self.chapterType], curChapterCsvId - CHAPTER_NUM[self.chapterType]))
        return false
    end
    return true
end

function Map1:checkChapterOpen(chapterId)
    --等级判断
    local mapCsv = csv.world_map[chapterId]
    local openLv = mapCsv.openLevel
    if self.level:read() < openLv then
        gGameUI:showTip(string.format(gLanguageCsv.levelUnLock, openLv))
        return false
    end

    if not self:checkGateIsPass(chapterId) then
        return false
    end

    ---- 检查前置关卡是否开启
    --if not self:checkPreGateIsPass(chapterId, true) then
    --    return false
    --end

    return true
end

function Map1:selectCity(idx, chapterID)
    if not self.isCanSelect then
        return
    end
    self.isCanSelect = false

    if self.selChapterID ~= chapterID then
        local fromChapter = self.selChapterID
        local toChapter = chapterID

        self.selChapterID = chapterID
        self:runHeroSprite(fromChapter, toChapter, function()
            self:openChapterDetail(idx, chapterID)
            self.isCanSelect = true
        end)
        return
    end

    self:openChapterDetail(idx, chapterID)
    self.isCanSelect = true
end

function Map1:openChapterDetail(idx, chapterID)
    if self:checkChapterOpen(chapterID) == false then
        return
    end
    local cb = function()
        local loadData = self["loadData"]
        if loadData ~= nil then
            self:loadData()
        end
    end

    local params = {
        chapterID = chapterID,
        chapterNum = idx,
        cb = cb,
        mapSelf = self,
    }
    gGameUI:stackUI("city.gate.section_detail.chapter", nil, nil, params)
end

function Map1:onBegan(touches, event)
    self:getPositionInMap(event)

    oldx, oldy = self.map:getPosition()
    oneTouchData = nil
    oneTouchMove = false

    local touchPos = event
    local mapPos = self.map:convertToNodeSpace(cc.p(touchPos.x, touchPos.y))
    print(string.format("Clicked at map position x = %d, y = %d", math.floor(mapPos.x), math.floor(mapPos.y)))

    -- Add a red dot with pos xy at the clicked position (enable this line to see the red dot)
    --self:addRedDotAt(mapPos.x, mapPos.y)
    
    for i, v in ipairs(self.cities) do
        local cityPos = cc.p(csv.world_map[v.chapterID].location.x, csv.world_map[v.chapterID].location.y)
        local distance = cc.pGetDistance(mapPos, cityPos)
        if distance < 100 then
            print(string.format("Near city ID: %d, x = %d, y = %d", 
                  v.chapterID, cityPos.x, cityPos.y))
        end
    end

    return true
end

function Map1:addRedDotAt(x, y)
    local dot = cc.DrawNode:create()
    local radius = 10
    dot:drawSolidCircle(cc.p(0, 0), radius, 0, 30, 1.0, 1.0, cc.c4f(1.0, 0, 0, 1.0))
    
    local label = cc.Label:createWithTTF(string.format("%d,%d", math.floor(x), math.floor(y)), "font/youmi1.ttf", 18)
    label:setPosition(cc.p(0, radius + 10))
    label:setColor(cc.c3b(255, 255, 255))
    dot:addChild(label)

    text.addEffect(label, {outline = {color = cc.c4b(106, 65, 21, 255), size = 2}})
    
    dot:setPosition(cc.p(x, y))
    self.map:addChild(dot, 999)  
    
    table.insert(self.mapDots, dot)
    
    if #self.mapDots > 20 then
        local oldestDot = table.remove(self.mapDots, 1)
        oldestDot:removeFromParent()
    end
    
    return dot
end

function Map1:clearAllDots()
    for _, dot in ipairs(self.mapDots) do
        dot:removeFromParent()
    end
    self.mapDots = {}
end

function Map1:getPositionInMap(event)
    local x, y = self.map:getPosition()
    if x < 0 then
        x = x * -1
    end
    if y < 0 then
        y = y * -1
    end
    x = x + event.x
    y = y + event.y
    x = math.floor(x)
    y = math.floor(y)
end

function Map1:onMove(touches, event)
    isMove = true

    local info = touches:getTouchMovePosition()
    if not oneTouchData then
        oldx, oldy = self.map:getPosition()
        oneTouchData = info
    end

    local dx = info.x - oneTouchData.x
    local dy = info.y - oneTouchData.y
    local targetx = oldx + dx
    local targety = oldy + dy

    targetx, targety = self:adaptMapPos(targetx, targety)

    if not oneTouchMove then
        oneTouchMove = true
    end

    if oneTouchMove then
        self.map:setPosition(cc.p(targetx, targety))
    end
   
end

function Map1:onEnd(touches, event)
    if not isMove then
    else
        isMove = false
    end
end

function Map1:adaptMapPos(targetx, targety)
    if not targetx then
        targetx, targety = self.map:getPosition()
    end

    local size = self.map:getBoundingBox()
    local width = display.sizeInView.width
    local height = display.sizeInView.height

    local maxX = -30
    local minX = (size.width - width - 30) * -1

    targetx = math.max(minX, targetx)
    targetx = math.min(maxX, targetx)

    local maxY = -20
    local minY = (size.height - height - 20) * -1

    targety = math.max(minY, targety)
    targety = math.min(maxY, targety)

    return targetx, targety
end
function Map1:onCauCaMap1()
    local x,y = self:adaptMapPos(-9074, -5246)
    self.map:runAction(cc.Sequence:create( cc.MoveTo:create(0.5, cc.p(x, y))))
end
function Map1:onClose()
    self:clearAllDots()
    ViewBase.onClose(self)
end

function Map1:setSelEffect(item, pos)
    if not self.selEffect then
        self.selEffect = CSprite.new("effect3q/chosen.skel")
        self.selEffect:play("effect_loop")
        self.selEffect:visible(true)
        self.selEffect:retain()
        self.selEffect:scale(0.6)
    end

    if item and pos and self.selEffect then
        self.selEffect:removeFromParent()
        self.selEffect:addTo(item, 100, "selEffect")
        self.selEffect:xy(pos)
        self.selEffect:retain()
        self.selEffect:setLocalZOrder(9)

        return self.selEffect
    end
end

return Map1
