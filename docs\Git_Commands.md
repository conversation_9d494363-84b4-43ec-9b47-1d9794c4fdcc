# Hướng dẫn sử dụng Git - <PERSON><PERSON><PERSON> l<PERSON>nh cơ bản và ví dụ

---

## 1. Khởi tạo repo mới
```bash
git init
```
> Khởi tạo một repository Git mới trong thư mục hiện tại.

**Ví dụ:**
```bash
mkdir demo_project
cd demo_project
git init
```

## 2. <PERSON><PERSON> repo từ remote
```bash
git clone <url>
```
> T<PERSON>i một repo Git từ remote về local.

**Ví dụ:**
```bash
git clone https://github.com/user/project.git
```

## 3. Kiểm tra trạng thái
```bash
git status
```
> Xem các thay đổi đã staged, chưa staged hoặc file chưa tracked.

**Ví dụ:**
```bash
git status
```

## 4. Thêm file vào staged
```bash
git add <ten_file>
git add .
```
> Đ<PERSON>a file vào staging để chuẩn bị commit.

**Ví dụ:**
```bash
git add main.lua
git add .
```

## 5. Commit thay đổi
```bash
git commit -m "Lời nhắn commit"
```
> Ghi nhận thay đổi.

**Ví dụ:**
```bash
git commit -m "Them tinh nang dang nhap"
```

## 6. Xem log commit
```bash
git log
```
> Xem lịch sử commit.

**Ví dụ:**
```bash
git log --oneline
```

## 7. Chuyển nhánh
```bash
git checkout <ten_nhanh>
```

**Ví dụ:**
```bash
git checkout develop
```

## 8. Tạo nhánh mới
```bash
git checkout -b <ten_nhanh>
```

**Ví dụ:**
```bash
git checkout -b fix/login-error
```

## 9. Hòa nhánh
```bash
git merge <ten_nhanh>
```

**Ví dụ:**
```bash
git checkout main
git merge develop
```

## 10. Lưu tạm thay đổi (stash)
```bash
git stash push -m "mo ta"
```

**Ví dụ:**
```bash
git stash push -m "dang lam form dang ky"
```

## 11. Xem danh sách stash
```bash
git stash list
```

**Ví dụ:**
```bash
git stash list
```

## 12. Phục hồi stash
```bash
git stash apply stash@{0}
```

**Ví dụ:**
```bash
git stash apply stash@{1}
```

## 13. Xem diff thay đổi
```bash
git diff
```

**Ví dụ:**
```bash
git diff main.lua
```

## 14. Kéo code từ remote
```bash
git pull origin <ten_nhanh>
```

**Ví dụ:**
```bash
git pull origin main
```

## 15. Đẩy code lên remote
```bash
git push origin <ten_nhanh>
```

**Ví dụ:**
```bash
git push origin feature/ui-redesign
```

## 16. Reset thay đổi
```bash
git reset --hard
```
> Khôi phục lại trạng thái commit gần nhất, xoá thay đổi local.

**Ví dụ:**
```bash
git reset --hard
```

---

## Một số mẹo hữu ích:

### ❌ Hủy staging file
```bash
git reset HEAD <file>
```

### ❌ Hủy thay đổi file
```bash
git checkout -- <file>
```

### ❌ Xóa stash
```bash
git stash drop stash@{0}
```

---
