
csv['cards'] = {
	[1] = {
		id = 1,
		name = '<PERSON><PERSON>',
		name_vn = '<PERSON><PERSON> Cá<PERSON>',
		name_en = '<PERSON><PERSON>',
		name_th = 'เจียกั๋ตกว๋อ',
		name_idn = '<PERSON><PERSON>',
		name_cn = '诸葛果',
		name_kr = '제갈과',
		cardMarkID = 1,
		atkType = {8},
		zawakeID = 1,
		zawakeFragID = 50001,
		chaRecom = {22, 24, 18, 20},
		specValue = {45, 45, 49, 49, 65, 65, 318},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 2,
		unitID = 1,
		skinSkillMap = {[109] = {21, 22, 23, 26, 61151, 61152}, __size = 1},
		innateSkillID = 13,
		skillList = {11, 12, 13, 16, 61151, 61152},
		starSkillSeqID = 1,
		fragID = 20001,
		fragNumType = 2,
		star = 2,
		abilitySeqID = 20,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {11, 12, 13},
		introduction = 'Gia Cát Qu<PERSON>, con gái của Gia Cát Lượng, tư chất thông minh, thông hiểu đạo pháp. Về sau thành tiên, sự tích chỉ được ghi chép trong sách Thần Tiên Thông Giám.',
		introduction_vn = 'Gia Cát Quả, con gái của Gia Cát Lượng, tư chất thông minh, thông hiểu đạo pháp. Về sau thành tiên, sự tích chỉ được ghi chép trong sách Thần Tiên Thông Giám.',
		introduction_en = 'Zhuge Qiao, daughter of Zhuge Liang, gifted and versed in the way of the sages. Later became an immortal; her story is only recorded in the book Divine Immortal Chronicles.',
		introduction_th = 'เจียฉาเกอ ลูกสาวของเจียฉาเหลียง มีสติปัญญาและเข้าใจในปรัชญา ต่อมาได้เป็นเซียน เรื่องราวถูกบันทึกไว้ในหนังสือตำนานเซียน',
		introduction_idn = 'Zhuge Guo, putri dari Zhuge Liang, cerdas dan memahami ajaran Tao. Kemudian menjadi dewi, kisahnya hanya tercatat dalam buku Shén Xiān Tōng Jiàn.',
		introduction_cn = '诸葛果，诸葛亮之女，天资聪颖，通晓道法。后成仙，事迹仅载于《神仙通鉴》。',
		introduction_kr = '제갈과, 제갈량의 딸로서 총명하고 도법에 통달하였다. 이후 신선이 되었으며, 그 전설은 《신선통감》에 기록되어 있다.',
		location = 'Healing power',
		location_vn = 'Healing power',
		location_en = 'Healing Power',
		location_kr = '치유력',
		location_th = 'พลังการรักษา',
		location_idn = 'Kekuatan Penyembuhan',
		location_cn = '治疗能力'
	},
	[11] = {
		id = 11,
		name = 'Tào Tháo',
		name_vn = 'Tào Tháo',
		name_en = 'Cao Cao',
		name_th = 'เตียวโต๊ะ',
		name_idn = 'Cao Cao',
		name_cn = '曹操',
		name_kr = '조조',
		cardMarkID = 11,
		atkType = {8},
		zawakeID = 11,
		zawakeFragID = 50011,
		chaRecom = {6, 7, 18, 19},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {618, 619, 620, 602, 603},
		levelExpID = 2,
		unitID = 11,
		skinSkillMap = {[118] = {121, 122, 123, 126, 61101, 61102}, __size = 1},
		innateSkillID = 113,
		skillList = {111, 112, 113, 116, 61101, 61102},
		starSkillSeqID = 11,
		fragID = 20011,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 11,
		abilitySeqID = 16,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {111, 112, 113},
		introduction = 'Tào Tháo: Thà ta phụ người trong thiên hạ, chứ đừng để người trong thiên hạ phụ ta',
		introduction_vn = 'Tào Tháo: Thà ta phụ người trong thiên hạ, chứ đừng để người trong thiên hạ phụ ta',
		introduction_en = 'Cao Cao: I would rather betray others in the world than let others betray me.',
		introduction_th = 'เต้าตง: ข้าขอทรยศต่อผู้คนในแผ่นดิน ยังดีกว่าถูกทรยศโดยผู้คนในแผ่นดิน',
		introduction_idn = 'Cao Cao: Lebih baik aku mengkhianati orang di dunia, daripada membiarkan orang di dunia mengkhianatiku.',
		introduction_cn = '曹操：宁可我负天下人，休教天下人负我。',
		introduction_kr = '조조: 천하 사람을 배신하기보다는 차라리 내가 천하 사람을 배신하겠다',
		location = 'Flame Burn',
		location_vn = 'Flame Burn',
		location_en = 'Flame Burn',
		location_kr = '화염 연소',
		location_th = 'เปลวไฟไหม้',
		location_idn = 'Pembakaran Api',
		location_cn = '火焰灼烧'
	},
	[21] = {
		id = 21,
		name = 'Gia Cát Lượng',
		name_vn = 'Gia Cát Lượng',
		name_en = 'Zhuge Liang',
		name_th = 'เจียกั๋ตเหลียง',
		name_idn = 'Zhuge Liang',
		name_cn = '诸葛亮',
		name_kr = '제갈량',
		cardMarkID = 21,
		atkType = {8},
		zawakeID = 21,
		zawakeFragID = 50021,
		chaRecom = {24, 23, 14, 16},
		specValue = {44, 43, 48, 65, 80, 64, 314},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 2,
		unitID = 21,
		skinSkillMap = {[108] = {221, 222, 223, 226, 61151, 61152}, __size = 1},
		innateSkillID = 213,
		skillList = {211, 212, 213, 216, 61151, 61152},
		starSkillSeqID = 21,
		fragID = 20021,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 21,
		abilitySeqID = 17,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {211, 212, 213},
		introduction = 'Gia Cát Lượng là quân sự kiệt xuất của nước Thục. Trên thông thiên văn, dưới tường địa lý. Được Lưu Bị đích thân mời xuất sơn phò ta xây dựng nhà Thục Hán.',
		introduction_vn = 'Gia Cát Lượng là quân sự kiệt xuất của nước Thục. Trên thông thiên văn, dưới tường địa lý. Được Lưu Bị đích thân mời xuất sơn phò ta xây dựng nhà Thục Hán.',
		introduction_en = 'Zhuge Liang was the brilliant military strategist of Shu. Skilled in astronomy and geography. Personally summoned by Liu Bei to leave the mountains and help build the Shu Han dynasty.',
		introduction_th = 'เจียฉาเหลียงเป็นแม่ทัพผู้กล้าหาญแห่งรัฐฉู่ เชี่ยวชาญฟ้าดินและภูมิศาสตร์ ได้รับเชิญโดยลิ่วเป่ยให้ขึ้นเขาช่วยสร้างราชวงศ์ฉู่ฮั่น',
		introduction_idn = 'Zhuge Liang adalah jenderal ulung dari negara Shu. Menguasai astronomi di atas dan geografi di bawah. Secara pribadi diundang oleh Liu Bei untuk keluar dari pegunungan membantu membangun negara Shu Han.',
		introduction_cn = '诸葛亮是蜀汉的杰出军事家。上通天文，下晓地理。受刘备亲自请出山辅佐，助建蜀汉基业。',
		introduction_kr = '제갈량은 촉나라의 뛰어난 군사였다. 천문을 통달하고 지리를 꿰뚫었으며, 유비가 직접 초빙하여 촉한을 건설하는 데 힘썼다.',
		location = 'Protect the allies',
		location_vn = 'Protect the allies',
		location_en = 'Protect Allies',
		location_kr = '아군 보호',
		location_th = 'ปกป้องพันธมิตร',
		location_idn = 'Melindungi Sekutu',
		location_cn = '保护盟友'
	},
	[31] = {
		id = 31,
		name = 'Chung Hội',
		name_vn = 'Chung Hội',
		name_en = 'Zhong Hui',
		name_th = 'จงฮุย',
		name_idn = 'Zhong Hui',
		name_cn = '钟会',
		name_kr = '충회',
		cardMarkID = 31,
		atkType = {7},
		zawakeFragID = 50031,
		chaRecom = {6, 8, 14, 16},
		specValue = {45, 45, 30, 35, 20, 20, 195},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 2,
		unitID = 31,
		innateSkillID = 313,
		skillList = {311, 312, 313, 316, 61101, 61102},
		starSkillSeqID = 31,
		fragID = 20031,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 31,
		abilitySeqID = 14,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {311, 312, 313},
		introduction = 'Chung Hội nổi tiếng tinh nhanh, giỏi giang và có mưu lược cao siêu. Sau khi diệt Thục, vì nổi dã tâm nên cuối cùng Chung Hội thân bại danh liệt. ',
		introduction_vn = 'Chung Hội nổi tiếng tinh nhanh, giỏi giang và có mưu lược cao siêu. Sau khi diệt Thục, vì nổi dã tâm nên cuối cùng Chung Hội thân bại danh liệt. ',
		introduction_en = 'Zhong Hui was famously sharp, talented, and highly strategic. After the conquest of Shu, his ambition led to his ultimate downfall and disgrace.',
		introduction_th = 'จงหัวมีชื่อเสียงในด้านไหวพริบ ปราดเปรียว และมีแผนการสูงส่ง หลังจากทำลายฉู่ลง คิดทรยศในใจ จึงสิ้นสุดด้วยความพ่ายแพ้และเสื่อมเสียชื่อเสียง',
		introduction_idn = 'Zhong Hui terkenal cerdas, mahir, dan memiliki strategi tinggi. Setelah menghancurkan Shu, karena ambisi liar akhirnya Zhong Hui jatuh dan hancur reputasinya.',
		introduction_cn = '钟会以机智灵巧、才干出众、谋略高深著称。灭蜀后因野心膨胀，终身败名裂。',
		introduction_kr = '종회는 명석하고 능란하며 고도의 책략을 구사하였다. 촉을 멸한 후 야심을 품어 결국 신용을 잃고 몰락하였다.',
		location = 'Great reduction in speed',
		location_vn = 'Great reduction in speed',
		location_en = 'Great Speed Reduction',
		location_kr = '큰 속도 감소',
		location_th = 'ลดความเร็วอย่างมาก',
		location_idn = 'Pengurangan Kecepatan Besar',
		location_cn = '大幅降低速度'
	},
	[41] = {
		id = 41,
		name = 'Lãng Nhân',
		name_vn = 'Lãng Nhân',
		name_en = 'Vagabond',
		name_th = 'ลางเหริน',
		name_idn = 'Lang Ren',
		name_cn = '浪人',
		name_kr = '낭인',
		cardMarkID = 41,
		atkType = {7},
		zawakeFragID = 50041,
		chaRecom = {24, 25, 11, 12},
		specValue = {65, 75, 90, 40, 45, 80, 395},
		feelItems = {609, 610, 611, 602, 603},
		unitID = 43,
		innateSkillID = 433,
		skillList = {431, 432, 433, 436, 61101, 61102},
		starSkillSeqID = 41,
		equipsList = {11001, 11101, 11201, 11301},
		fragID = 20041,
		star = 2,
		starEffectIndex = 41,
		abilitySeqID = 11,
		fetterList = {411, 412, 413},
		introduction = 'Beedrill, the Poison Bee Heroes, is an evolved form of Kakuna. Its sting is highly poisonous.',
		introduction_vn = 'Beedrill, the Poison Bee Heroes, is an evolved form of Kakuna. Its sting is highly poisonous.',
		introduction_en = 'Beedrill, the Poison Bee Hero, is an evolved form of Kakuna. Its sting is highly poisonous.',
		introduction_th = 'บีดดริล ฮีโร่ผึ้งพิษ เป็นวิวัฒนาการของกาคูน่า หนามพิษของมันมีฤทธิ์รุนแรงมาก',
		introduction_idn = 'Beedrill, Pahlawan Lebah Racun, adalah bentuk evolusi dari Kakuna. Sengatannya sangat beracun.',
		introduction_cn = '铁刺蛾，毒蜂英雄，是铁壳蛹的进化形态。其毒针剧毒无比。',
		introduction_kr = '독벌레 영웅 비드릴은 단단벌레의 진화형이다. 그 독침은 매우 치명적이다.',
		location = 'Normal damage: Single-target healing reduction',
		location_vn = 'Normal damage: Single-target healing reduction',
		location_en = 'Normal Damage: Single-target Healing Reduction',
		location_kr = '일반 피해: 단일 대상 치유 감소',
		location_th = 'ความเสียหายปกติ: ลดการรักษาเป้าหมายเดียว',
		location_idn = 'Kerusakan Normal: Pengurangan Penyembuhan Sasaran Tunggal',
		location_cn = '普通伤害：单体治疗减益'
	},
	[51] = {
		id = 51,
		name = 'Tư Mã Ý',
		name_vn = 'Tư Mã Ý',
		name_en = 'Sima Yi',
		name_th = 'ซื่อหม่าอี๋',
		name_idn = 'Sima Yi',
		name_cn = '司马懿',
		name_kr = '사마의',
		cardMarkID = 51,
		atkType = {7},
		zawakeID = 51,
		zawakeFragID = 50051,
		chaRecom = {8, 9, 13, 12},
		specValue = {40, 56, 35, 40, 45, 35, 251},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {606, 607, 608, 602, 603},
		levelExpID = 2,
		unitID = 51,
		innateSkillID = 513,
		skillList = {511, 512, 513, 516, 61101, 61102},
		starSkillSeqID = 51,
		fragID = 20051,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 51,
		abilitySeqID = 19,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {511, 512, 513},
		introduction = 'Tư Mã Ý, quân sư bậc nhất của Tào Ngụy, có tài năng quân sự vượt trội và giỏi phòng thủ.',
		introduction_vn = 'Tư Mã Ý, quân sư bậc nhất của Tào Ngụy, có tài năng quân sự vượt trội và giỏi phòng thủ.',
		introduction_en = 'Sima Yi, the top strategist of Cao Wei, possessed superior military talent and excelled in defense.',
		introduction_th = 'ซื่อหม่าอี้ ขุนพลทัพใหญ่แห่งเต้าหวี่ มีความสามารถทางยุทธศาสตร์และเชี่ยวชาญการป้องกัน',
		introduction_idn = 'Sima Yi, penasihat militer terbaik dari Cao Wei, memiliki bakat militer luar biasa dan ahli dalam pertahanan.',
		introduction_cn = '司马懿，曹魏首席军师，军事才能卓越，善于防御。',
		introduction_kr = '사마의는 조위 최고의 군사 전략가로 탁월한 군사 재능과 방어 능력을 지녔다.',
		location = 'Increased speed damage: Increase damage quickly',
		location_vn = 'Increased speed damage: Increase damage quickly',
		location_en = 'Increased Speed Damage: Rapid Damage Increase',
		location_kr = '속도 증가 피해: 빠르게 피해 증가',
		location_th = 'ความเสียหายเพิ่มความเร็ว: เพิ่มความเสียหายอย่างรวดเร็ว',
		location_idn = 'Kerusakan dengan Kecepatan Meningkat: Meningkatkan Kerusakan dengan Cepat',
		location_cn = '加速伤害：快速提升伤害'
	},
	[61] = {
		id = 61,
		name = 'Đặng Ngải',
		name_vn = 'Đặng Ngải',
		name_en = 'Deng Ai',
		name_th = 'เต็งไห่',
		name_idn = 'Deng Ai',
		name_cn = '邓艾',
		name_kr = '등예',
		cardMarkID = 61,
		atkType = {7},
		zawakeFragID = 50061,
		chaRecom = {8, 9, 13, 12},
		specValue = {30, 72, 56, 35, 25, 35, 253},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {606, 607, 608, 602, 603},
		levelExpID = 2,
		unitID = 61,
		innateSkillID = 613,
		skillList = {611, 612, 613, 616, 61101, 61102},
		starSkillSeqID = 61,
		fragID = 20061,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 61,
		abilitySeqID = 11,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {611, 612, 613},
		introduction = 'Đặng Ngải, đại tướng trứ danh của Tào Ngụy thời Tam Quốc, văn võ song toàn và giỏi binh pháp.',
		introduction_vn = 'Đặng Ngải, đại tướng trứ danh của Tào Ngụy thời Tam Quốc, văn võ song toàn và giỏi binh pháp.',
		introduction_en = 'Deng Ai, a renowned general of Cao Wei during the Three Kingdoms period, was skilled in both civil and military affairs and proficient in military tactics.',
		introduction_th = 'เติ้งไห่ นายทหารผู้มีชื่อเสียงของเต้าหวี่ในยุคสามก๊ก มีความรู้ทั้งด้านกวีนิพนธ์และยุทธศาสตร์',
		introduction_idn = 'Deng Ai, jenderal ternama dari Cao Wei pada zaman Tiga Kerajaan, mahir dalam seni perang dan memiliki kemampuan kombinasi ilmu dan seni.',
		introduction_cn = '邓艾，三国时期曹魏名将，文武双全，精通兵法。',
		introduction_kr = '등애는 삼국시대 조위의 유명한 대장으로 문무겸전하며 병법에 능했다.',
		location = 'Savage damage: 100% Critical Hit',
		location_vn = 'Savage damage: 100% Critical Hit',
		location_en = 'Savage Damage: 100% Critical Hit',
		location_kr = '광폭 피해: 100% 치명타',
		location_th = 'ความเสียหายดุร้าย: โอกาสคริติคอล 100%',
		location_idn = 'Kerusakan Buas: Serangan Kritis 100%',
		location_cn = '狂暴伤害：100%暴击'
	},
	[71] = {
		id = 71,
		name = 'Đao Nhân',
		name_vn = 'Đao Nhân',
		name_en = 'Saber Soldier',
		name_th = 'เตาเหริน',
		name_idn = 'Dao Ren',
		name_cn = '刀人',
		name_kr = '도인',
		cardMarkID = 71,
		atkType = {7},
		zawakeFragID = 50071,
		chaRecom = {8, 9, 13, 12},
		specValue = {40, 70, 60, 30, 31, 31, 262},
		feelItems = {606, 607, 608, 602, 603},
		unitID = 71,
		innateSkillID = 713,
		skillList = {711, 712, 713, 716, 61101, 61102},
		starSkillSeqID = 71,
		equipsList = {11001, 11101, 11201, 11301},
		fragID = 20071,
		star = 2,
		starEffectIndex = 71,
		abilitySeqID = 14,
		fetterList = {711, 712, 713},
		introduction = 'Unlike Pidgey, Spearow has a terrible attitude. It is very wild and will sometimes attack other Heroes and humans.',
		introduction_vn = 'Unlike Pidgey, Spearow has a terrible attitude. It is very wild and will sometimes attack other Heroes and humans.',
		introduction_en = 'Unlike Pidgey, Spearow has a terrible attitude. It is very wild and will sometimes attack other Heroes and humans.',
		introduction_th = 'ต่างจากพิดจี้ สเปียโรว์มีนิสัยรุนแรงมาก เป็นสัตว์ป่าที่ดุร้าย บางครั้งจะโจมตีฮีโร่และมนุษย์',
		introduction_idn = 'Berbeda dengan Pidgey, Spearow memiliki sikap buruk. Ia sangat liar dan kadang menyerang pahlawan dan manusia lain.',
		introduction_cn = '与波波不同，比比鸟态度恶劣。性情狂野，有时会攻击其他英雄和人类。',
		introduction_kr = '피죤과 달리 스피어로는 성격이 험악하다. 매우 야생적이며 때로는 다른 영웅과 인간을 공격하기도 한다.',
		location = 'Normal damage: Increase damage quickly',
		location_vn = 'Normal damage: Increase damage quickly',
		location_en = 'Normal Damage: Rapid Damage Increase',
		location_kr = '일반 피해: 빠르게 피해 증가',
		location_th = 'ความเสียหายปกติ: เพิ่มความเสียหายอย่างรวดเร็ว',
		location_idn = 'Kerusakan Normal: Meningkatkan Kerusakan dengan Cepat',
		location_cn = '普通伤害：快速提升伤害'
	},
	[72] = {
		id = 72,
		name = 'Cự Nhân Khăn Vàng',
		name_vn = 'Cự Nhân Khăn Vàng',
		name_en = 'Yellow Turban Giant',
		name_th = 'มนุษย์ผ้าขาวเหลือง',
		name_idn = 'Orang Kain Kuning',
		name_cn = '黄巾巨人',
		name_kr = '거인건황',
		cardMarkID = 72,
		atkType = {7},
		zawakeFragID = 50071,
		chaRecom = {8, 9, 13, 12},
		specValue = {65, 100, 90, 65, 61, 61, 442},
		feelItems = {606, 607, 608, 602, 603},
		unitID = 72,
		innateSkillID = 723,
		skillList = {721, 722, 723, 726, 61101, 61102},
		starSkillSeqID = 72,
		equipsList = {11001, 11101, 11201, 11301},
		fragID = 20071,
		star = 2,
		starEffectIndex = 72,
		abilitySeqID = 14,
		fetterList = {711, 712, 713},
		introduction = 'Fearow, the Beak Hero. The evolved form of Spearow. Its large, powerful wings allow it to fly for an entire day. The crushing power of its beak is tremendous.',
		introduction_vn = 'Fearow, the Beak Hero. The evolved form of Spearow. Its large, powerful wings allow it to fly for an entire day. The crushing power of its beak is tremendous.',
		introduction_en = 'Fearow, the Beak Hero. The evolved form of Spearow. Its large, powerful wings allow it to fly for an entire day. The crushing power of its beak is tremendous.',
		introduction_th = 'เฟียโรว์ โปเกมอนปากแหลม เป็นวิวัฒนาการจากสเปียโรว์ ปีกใหญ่และแข็งแรงช่วยให้บินได้นานทั้งวัน กำลังบดขยี้ของปากแข็งแกร่งมาก',
		introduction_idn = 'Fearow, Hero Paruh. Bentuk evolusi dari Spearow. Sayap besarnya yang kuat memungkinkan terbang sepanjang hari. Kekuatan menghancurkan paruhnya luar biasa.',
		introduction_cn = '大嘴雀，嘴巴宝可梦，是比比鸟的进化形态。其巨大有力的翅膀能够整天飞行。其喙的破坏力极强。',
		introduction_kr = '피죤투는 부리 포켓몬으로 스피어로의 진화형이다. 크고 강력한 날개로 하루 종일 날 수 있으며, 부리의 파괴력은 엄청나다.',
		location = 'Normal damage: Increase damage quickly',
		location_vn = 'Normal damage: Increase damage quickly',
		location_en = 'Normal Damage: Rapid Damage Increase',
		location_kr = '일반 피해: 빠르게 피해 증가',
		location_th = 'ความเสียหายปกติ: เพิ่มความเสียหายอย่างรวดเร็ว',
		location_idn = 'Kerusakan Normal: Meningkatkan Kerusakan dengan Cepat',
		location_cn = '普通伤害：快速提升伤害'
	},
	[81] = {
		id = 81,
		name = 'Thương Binh',
		name_vn = 'Thương Binh',
		name_en = 'Spearman',
		name_th = 'ทหารเจ็บ',
		name_idn = 'Prajurit Tombak',
		name_cn = '伤兵',
		name_kr = '상병',
		cardMarkID = 81,
		atkType = {7},
		zawakeFragID = 50081,
		chaRecom = {7, 8, 9, 12},
		specValue = {35, 55, 60, 44, 40, 54, 288},
		feelItems = {621, 622, 623, 602, 603},
		unitID = 81,
		innateSkillID = 813,
		skillList = {811, 812, 813, 816, 61101, 61102},
		starSkillSeqID = 81,
		equipsList = {11001, 11101, 11201, 11301},
		fragID = 20081,
		star = 2,
		starEffectIndex = 81,
		abilitySeqID = 11,
		fetterList = {811, 812, 813},
		introduction = 'The older it gets, the longer it grows. At night, it wraps its long body around tree branches to rest.',
		introduction_vn = 'The older it gets, the longer it grows. At night, it wraps its long body around tree branches to rest.',
		introduction_en = 'The older it gets, the longer it grows. At night, it wraps its long body around tree branches to rest.',
		introduction_th = 'ยิ่งโตยิ่งยาว ในเวลากลางคืนจะพันร่างยาวรอบกิ่งไม้เพื่อพักผ่อน',
		introduction_idn = 'Semakin tua, semakin panjang tubuhnya. Pada malam hari, ia melilitkan tubuh panjangnya di cabang pohon untuk beristirahat.',
		introduction_cn = '年龄越大，身体越长。夜晚时用长身缠绕树枝休息。',
		introduction_kr = '나이가 들수록 몸이 길어진다. 밤에는 긴 몸을 나뭇가지에 감고 휴식한다.',
		location = 'Normal damage: Afraid of the strong, and thrash the weak',
		location_vn = 'Normal damage: Afraid of the strong, and thrash the weak',
		location_en = 'Normal Damage: Fear the Strong, Crush the Weak',
		location_kr = '일반 피해: 강자를 두려워하고 약자를 난타함',
		location_th = 'ความเสียหายปกติ: กลัวผู้แข็งแกร่ง และทำร้ายผู้ที่อ่อนแอ',
		location_idn = 'Kerusakan Normal: Takut pada yang Kuat, dan Menghajar yang Lemah',
		location_cn = '普通伤害：惧强凌弱'
	},
	[82] = {
		id = 82,
		name = 'Đao Khăn Vàng',
		name_vn = 'Đao Khăn Vàng',
		name_en = 'Yellow Turban Swordsman',
		name_th = 'ดาบผ้าขาวเหลือง',
		name_idn = 'Pedang Kain Kuning',
		name_cn = '黄巾刀',
		name_kr = '도건황',
		cardMarkID = 82,
		atkType = {7},
		zawakeFragID = 50081,
		chaRecom = {7, 8, 9, 12},
		specValue = {60, 80, 95, 69, 65, 79, 448},
		feelItems = {621, 622, 623, 602, 603},
		unitID = 82,
		innateSkillID = 823,
		skillList = {821, 822, 823, 826, 61101, 61102},
		starSkillSeqID = 82,
		equipsList = {11001, 11101, 11201, 11301},
		fragID = 20081,
		star = 2,
		starEffectIndex = 82,
		abilitySeqID = 11,
		fetterList = {811, 812, 813},
		introduction = 'The pattern on its belly appears to be a frightening face. Weak foes will flee just at the sight of the pattern.',
		introduction_vn = 'The pattern on its belly appears to be a frightening face. Weak foes will flee just at the sight of the pattern.',
		introduction_en = 'The pattern on its belly appears to be a frightening face. Weak foes will flee just at the sight of the pattern.',
		introduction_th = 'ลวดลายบนท้องดูเหมือนใบหน้าที่น่ากลัว ศัตรูที่อ่อนแอมักจะหนีเมื่อเห็นลวดลายนี้',
		introduction_idn = 'Pola di perutnya tampak seperti wajah menakutkan. Musuh yang lemah akan lari hanya dengan melihat pola tersebut.',
		introduction_cn = '腹部的花纹像一张恐怖的脸。弱小的敌人看到花纹会惊恐逃跑。',
		introduction_kr = '배의 무늬는 무서운 얼굴처럼 보인다. 약한 적들은 이 무늬만 보고도 도망친다.',
		location = 'Normal damage: Afraid of the strong, and thrash the weak',
		location_vn = 'Normal damage: Afraid of the strong, and thrash the weak',
		location_en = 'Normal Damage: Fear the Strong, Crush the Weak',
		location_kr = '일반 피해: 강자를 두려워하고 약자를 난타함',
		location_th = 'ความเสียหายปกติ: กลัวผู้แข็งแกร่ง และทำร้ายผู้ที่อ่อนแอ',
		location_idn = 'Kerusakan Normal: Takut pada yang Kuat, dan Menghajar yang Lemah',
		location_cn = '普通伤害：惧强凌弱'
	},
	[91] = {
		id = 91,
		name = 'Phan Phượng',
		name_vn = 'Phan Phượng',
		name_en = 'Fan Feng',
		name_th = 'ฟานฟง',
		name_idn = 'Fan Feng',
		name_cn = '潘凤',
		name_kr = '번봉',
		cardMarkID = 91,
		atkType = {8},
		zawakeID = 91,
		zawakeFragID = 50091,
		chaRecom = {6, 7, 18, 19},
		specValue = {20, 60, 40, 15, 35, 35, 205},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {612, 613, 614, 602, 603},
		levelExpID = 2,
		unitID = 91,
		innateSkillID = 913,
		skillList = {911, 912, 913, 916, 61101, 61102},
		starSkillSeqID = 91,
		fragID = 20091,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 91,
		abilitySeqID = 18,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {911, 912, 913},
		introduction = 'Thuộc hạ của Hàn Phức, có sở trường dùng búa lớn, cuối cùng bị Hoa Hùng tiêu diệt.',
		introduction_vn = 'Thuộc hạ của Hàn Phức, có sở trường dùng búa lớn, cuối cùng bị Hoa Hùng tiêu diệt.',
		introduction_en = 'A subordinate of Han Fu, skilled in wielding a large hammer, ultimately killed by Hua Xiong.',
		introduction_th = 'ผู้ใต้บังคับบัญชาของฮั่นฟู มีความชำนาญใช้ค้อนใหญ่ สุดท้ายถูกฮัวหงทำลาย',
		introduction_idn = 'Bawahan Han Fu, ahli menggunakan palu besar, akhirnya dibunuh oleh Hua Xiong.',
		introduction_cn = '韩馥部下，擅长使用大锤，最终被华雄击败。',
		introduction_kr = '한복의 부하로 대형 망치를 잘 사용했으나 결국 화웅에게 제거당했다.',
		location = 'Paralysis Status Condition',
		location_vn = 'Paralysis Status Condition',
		location_en = 'Paralysis Status Effect',
		location_kr = '마비 상태 이상',
		location_th = 'สถานะถูกช็อก',
		location_idn = 'Kondisi Status Kelumpuhan',
		location_cn = '麻痹状态异常'
	},
	[101] = {
		id = 101,
		name = 'Cung Binh',
		name_vn = 'Cung Binh',
		name_en = 'Archer',
		name_th = 'ทหารธนู',
		name_idn = 'Prajurit Busur',
		name_cn = '弓兵',
		name_kr = '궁병',
		cardMarkID = 101,
		atkType = {7},
		zawakeFragID = 50101,
		chaRecom = {15, 16, 11, 12},
		specValue = {50, 40, 75, 85, 20, 30, 300},
		feelItems = {609, 610, 611, 602, 603},
		unitID = 101,
		innateSkillID = 1013,
		skillList = {1011, 1012, 1013, 1016, 61151, 61152},
		starSkillSeqID = 101,
		equipsList = {11001, 11101, 11201, 11301},
		fragID = 20101,
		star = 2,
		starEffectIndex = 101,
		abilitySeqID = 12,
		fetterList = {1011, 1012, 1013},
		introduction = 'Its body is dry. When it gets cold at night, its hide is said to become coated with a fine dew.',
		introduction_vn = 'Its body is dry. When it gets cold at night, its hide is said to become coated with a fine dew.',
		introduction_en = 'Its body is dry. When it gets cold at night, its hide is said to become coated with a fine dew.',
		introduction_th = 'ร่างกายแห้ง เมื่อกลางคืนอากาศหนาว หนังของมันจะขึ้นน้ำค้างละเอียด',
		introduction_idn = 'Tubuhnya kering. Saat dingin di malam hari, kulitnya dikatakan tertutup embun halus.',
		introduction_cn = '身体干燥。夜晚寒冷时，皮肤会覆盖一层细微的露珠。',
		introduction_kr = '몸이 건조하다. 밤에 추워지면 피부에 고운 이슬이 맺힌다고 한다.',
		location = 'Taunt shield',
		location_vn = 'Taunt shield',
		location_en = 'Taunt Shield',
		location_kr = '도발 방패',
		location_th = 'โล่ล่อเป้า',
		location_idn = 'Perisai Provokasi',
		location_cn = '嘲讽护盾'
	},
	[102] = {
		id = 102,
		name = 'Chuỳ Khăn Vàng',
		name_vn = 'Chuỳ Khăn Vàng',
		name_en = 'Yellow Turban Mace',
		name_th = 'ค้อนผ้าขาวเหลือง',
		name_idn = 'Palu Kain Kuning',
		name_cn = '黄巾锤',
		name_kr = '추건황',
		cardMarkID = 102,
		atkType = {7},
		zawakeFragID = 50101,
		chaRecom = {15, 16, 11, 12},
		specValue = {75, 65, 100, 110, 45, 55, 450},
		feelItems = {609, 610, 611, 602, 603},
		unitID = 102,
		innateSkillID = 1023,
		skillList = {1021, 1022, 1023, 1026, 61151, 61152},
		starSkillSeqID = 102,
		equipsList = {11001, 11101, 11201, 11301},
		fragID = 20101,
		star = 2,
		starEffectIndex = 102,
		abilitySeqID = 12,
		fetterList = {1011, 1012, 1013},
		introduction = 'Sandslash, the Mouse Hero. The evolved form of Sandshrew. It attacks opponents with the quills on its back.',
		introduction_vn = 'Sandslash, the Mouse Hero. The evolved form of Sandshrew. It attacks opponents with the quills on its back.',
		introduction_en = 'Sandslash, the Mouse Hero. The evolved form of Sandshrew. It attacks opponents with the quills on its back.',
		introduction_th = 'แซนด์สแล็ช โปเกมอนหนู วิวัฒนาการจากแซนด์ชรู โจมตีศัตรูด้วยหนามที่หลัง',
		introduction_idn = 'Sandslash, Hero Tikus. Bentuk evolusi dari Sandshrew. Ia menyerang lawan dengan duri di punggungnya.',
		introduction_cn = '穿山鼠，是沙鼠的进化形态。用背上的刺攻击敌人。',
		introduction_kr = '모래두지의 진화형 모래땅쥐는 등의 가시로 적을 공격한다.',
		location = 'Taunt shield',
		location_vn = 'Taunt shield',
		location_en = 'Taunt Shield',
		location_kr = '도발 방패',
		location_th = 'โล่ล่อเป้า',
		location_idn = 'Perisai Provokasi',
		location_cn = '嘲讽护盾'
	},
	[111] = {
		id = 111,
		name = 'Cung Thủ Khăn Vàng',
		name_vn = 'Cung Thủ Khăn Vàng',
		name_en = 'Yellow Turban Archer',
		name_th = 'นักธนูผ้าขาวเหลือง',
		name_idn = 'Pemanah Kain Kuning',
		name_cn = '黄巾弓手',
		name_kr = '궁수건황',
		cardMarkID = 111,
		atkType = {7},
		zawakeFragID = 50111,
		gender = 2,
		chaRecom = {15, 16, 11, 12},
		specValue = {55, 41, 47, 52, 40, 40, 275},
		feelItems = {621, 622, 623, 602, 603},
		unitID = 111,
		innateSkillID = 1113,
		skillList = {1111, 1112, 1113, 1116, 61151, 61152},
		starSkillSeqID = 111,
		equipsList = {11001, 11101, 11201, 11301},
		fragID = 20111,
		star = 2,
		starEffectIndex = 111,
		abilitySeqID = 12,
		fetterList = {1111, 1112, 1113},
		introduction = 'Nidoran♀. Its poison pin is quite powerful for its size, but its horn is smaller than a male\'s.',
		introduction_vn = 'Nidoran♀. Its poison pin is quite powerful for its size, but its horn is smaller than a male\'s.',
		introduction_en = 'Nidoran♀. Its poison pin is quite powerful for its size, but its horn is smaller than a male\'s.',
		introduction_th = 'นิโดแรน♀ หนามพิษของมันมีพลังแรงเกินตัว แต่เขาของมันเล็กกว่าตัวผู้',
		introduction_idn = 'Nidoran♀. Sengat racunnya cukup kuat untuk ukurannya, tapi tanduknya lebih kecil dari jantan.',
		introduction_cn = '毒针女，毒针虽小但强大，角比雄性小。',
		introduction_kr = '니드런♀. 몸집에 비해 독침이 꽤 강력하지만 뿔은 수컷보다 작다.',
		location = 'Damage reflection tank: AOE Poisoning',
		location_vn = 'Damage reflection tank: AOE Poisoning',
		location_en = 'Damage Reflection Tank: AOE Poison',
		location_kr = '피해 반사 탱크: 광역 중독',
		location_th = 'แท็งค์สะท้อนความเสียหาย: พิษแบบวงกว้าง',
		location_idn = 'Tank Pemantul Kerusakan: Racun AOE',
		location_cn = '伤害反弹坦克：范围毒伤'
	},
	[121] = {
		id = 121,
		name = 'Kỵ Binh',
		name_vn = 'Kỵ Binh',
		name_en = 'Cavalry',
		name_th = 'ทหารม้า',
		name_idn = 'Kavaleri',
		name_cn = '骑兵',
		name_kr = '기병',
		cardMarkID = 121,
		atkType = {7},
		zawakeFragID = 50121,
		gender = 1,
		chaRecom = {7, 8, 9, 12},
		specValue = {46, 50, 57, 40, 40, 40, 273},
		feelItems = {621, 622, 623, 602, 603},
		unitID = 121,
		innateSkillID = 1213,
		skillList = {1211, 1212, 1213, 1216, 61101, 61102},
		starSkillSeqID = 121,
		equipsList = {11001, 11101, 11201, 11301},
		fragID = 20121,
		star = 2,
		starEffectIndex = 121,
		abilitySeqID = 11,
		fetterList = {1211, 1212, 1213},
		introduction = 'Nidoran ♂, Poison Pin Heroes. Nidoran♂. Its horn is larger than the female\'s and its Horn Attack is quite powerful.',
		introduction_vn = 'Nidoran ♂, Poison Pin Heroes. Nidoran♂. Its horn is larger than the female\'s and its Horn Attack is quite powerful.',
		introduction_en = 'Nidoran♂, Poison Pin Hero. Nidoran♂. Its horn is larger than the female\'s and its Horn Attack is quite powerful.',
		introduction_th = 'นิโดแรน♂ ฮีโร่หนามพิษ เขาของมันใหญ่กว่าตัวเมียและการโจมตีด้วยเขามีพลังมาก',
		introduction_idn = 'Nidoran♂, Pahlawan Sengat Racun. Tanduk Nidoran♂ lebih besar dari betina dan Serangan Tanduknya cukup kuat.',
		introduction_cn = '毒针男，毒针英雄。角比雌性大，角攻击威力强大。',
		introduction_kr = '니드런♂, 독침 영웅. 수컷의 뿔이 암컷보다 크고 뿔 공격이 매우 강력하다.',
		location = 'Savage damage: Increase damage when poisoned',
		location_vn = 'Savage damage: Increase damage when poisoned',
		location_en = 'Savage Damage: Increased Damage When Poisoned',
		location_kr = '광폭 피해: 중독 시 피해 증가',
		location_th = 'ความเสียหายดุร้าย: เพิ่มความเสียหายเมื่อถูกพิษ',
		location_idn = 'Kerusakan Buas: Meningkatkan Kerusakan Saat Diracun',
		location_cn = '狂暴伤害：中毒时提升伤害'
	},
	[131] = {
		id = 131,
		name = 'Trương Xuân Hoa',
		name_vn = 'Trương Xuân Hoa',
		name_en = 'Zhang Xuanhua',
		name_th = 'จางซวนฮวา',
		name_idn = 'Zhang Xuanhua',
		name_cn = '张春华',
		name_kr = '장춘화',
		cardMarkID = 131,
		atkType = {8},
		zawakeID = 131,
		zawakeFragID = 50131,
		chaRecom = {22, 24, 18, 20},
		specValue = {50, 15, 25, 28, 45, 55, 218},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {618, 619, 620, 602, 603},
		levelExpID = 2,
		unitID = 131,
		innateSkillID = 1313,
		skillList = {1311, 1312, 1313, 1316, 61151, 61152},
		starSkillSeqID = 131,
		fragID = 20131,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 131,
		abilitySeqID = 18,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {1311, 1312, 1313},
		introduction = 'Trương Xuân Hoa là vợ chính của Tư Mã Ý. Bà là mẹ của Tư Mã Sư và Tư Mã Chiêu.',
		introduction_vn = 'Trương Xuân Hoa là vợ chính của Tư Mã Ý. Bà là mẹ của Tư Mã Sư và Tư Mã Chiêu.',
		introduction_en = 'Zhang Xuanhua is the principal wife of Sima Yi. She is the mother of Sima Shi and Sima Zhao.',
		introduction_th = 'จางซวนฮวา ภรรยาหลักของซื่อหม่าอี้ เป็นมารดาของซื่อหม่าเสวี่ยและซื่อหม่าเจี๋ยว',
		introduction_idn = 'Zhang Xuanhua adalah istri utama Sima Yi. Ia adalah ibu dari Sima Shi dan Sima Zhao.',
		introduction_cn = '张春华是司马懿的正妻，司马师与司马昭之母。',
		introduction_kr = '장춘화는 사마의의 정실부인으로, 사마사와 사마소의 어머니이다.',
		location = 'Debuff support: Weaken damage',
		location_vn = 'Debuff support: Weaken damage',
		location_en = 'Debuff Support: Damage Weakening',
		location_kr = '디버프 지원: 피해 약화',
		location_th = 'สนับสนุนดีบัฟ: ทำให้ความเสียหายอ่อนแอ',
		location_idn = 'Dukungan Debuff: Melemahkan Kerusakan',
		location_cn = '减益辅助：削弱伤害'
	},
	[151] = {
		id = 151,
		name = 'Bộ Luyện Sư',
		name_vn = 'Bộ Luyện Sư',
		name_en = 'Infantry Trainer',
		name_th = 'ครูฝึกทหารราบ',
		name_idn = 'Pelatih Infanteri',
		name_cn = '步炼师',
		name_kr = '보련사',
		cardMarkID = 151,
		atkType = {8},
		zawakeID = 151,
		zawakeFragID = 50151,
		chaRecom = {18, 19, 20, 21},
		specValue = {90, 15, 30, 15, 40, 20, 210},
		starTypeID = 2,
		advanceTypeID = 11,
		feelItems = {606, 607, 608, 602, 603},
		unitID = 151,
		innateSkillID = 1513,
		skillList = {1511, 1512, 1513, 1516, 61101, 61102},
		starSkillSeqID = 151,
		fragID = 20151,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 151,
		abilitySeqID = 18,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {1511, 1512, 1513},
		introduction = 'Bộ Luyện Sư, hay còn gọi là Bộ phu nhân, vợ của Tôn Quyền - hoàng đế sáng lập của nhà nước Đông Ngô trong thời Tam Quốc.',
		introduction_vn = 'Bộ Luyện Sư, hay còn gọi là Bộ phu nhân, vợ của Tôn Quyền - hoàng đế sáng lập của nhà nước Đông Ngô trong thời Tam Quốc.',
		introduction_en = 'Bu Lian Shi, also known as Bu Madam, was the wife of Sun Quan, the founding emperor of Eastern Wu during the Three Kingdoms period.',
		introduction_th = 'ปู่เหลียนซือ หรือเรียกว่าปู่ฟูหนาน ภรรยาของซุ่นชวน ราชาและผู้ก่อตั้งรัฐตงงูในยุคสามก๊ก',
		introduction_idn = 'Bu Lian Shi, juga dikenal sebagai Ibu, istri Sun Quan - kaisar pendiri negara Wu Timur pada masa Tiga Kerajaan.',
		introduction_cn = '步练师，又称步夫人，是孙权——东吴开国皇帝的妻子。',
		introduction_kr = '보련서, 또는 보부인으로도 불리며, 동오 초대 황제 손권의 부인이다.',
		location = 'Sweet Voice&Hypnosis Effect',
		location_vn = 'Sweet Voice&Hypnosis Effect',
		location_en = 'Sweet Voice & Hypnosis Effect',
		location_kr = '감미로운 목소리&최면 효과',
		location_th = 'เสียงหวาน & ผลงีบหลับ',
		location_idn = 'Suara Merdu & Efek Hipnosis',
		location_cn = '甜美之声&催眠效果'
	},
	[161] = {
		id = 161,
		name = 'Tuân Úc',
		name_vn = 'Tuân Úc',
		name_en = 'Xun Yu',
		name_th = 'ต้วนอุ๊ค',
		name_idn = 'Xun Yu',
		name_cn = '荀彧',
		name_kr = '손욱',
		cardMarkID = 161,
		atkType = {7},
		zawakeID = 161,
		zawakeFragID = 50161,
		chaRecom = {8, 9, 13, 12},
		specValue = {40, 55, 30, 35, 45, 40, 245},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 3,
		unitID = 161,
		skinSkillMap = {[120] = {1621, 1622, 1623, 1626, 61101, 61102}, __size = 1},
		innateSkillID = 1613,
		skillList = {1611, 1612, 1613, 1616, 61101, 61102},
		starSkillSeqID = 161,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20161,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 161,
		abilitySeqID = 4,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {1611, 1612, 1613, 1614},
		introduction = 'Tuân Úc, một mưu sĩ tài ba thời Đông Hán, nổi tiếng tao nhã thanh thoát và có con mắt chiến lược giúp Tào Tháo gây dựng sự nghiệp. ',
		introduction_vn = 'Tuân Úc, một mưu sĩ tài ba thời Đông Hán, nổi tiếng tao nhã thanh thoát và có con mắt chiến lược giúp Tào Tháo gây dựng sự nghiệp. ',
		introduction_en = 'Xun Yu, a brilliant strategist during the late Eastern Han, known for his elegance and strategic insight which helped Cao Cao establish his power.',
		introduction_th = 'ฉวนอวี่ นักวางกลยุทธ์ผู้มีความสามารถในยุคตงฮั่น มีชื่อเสียงในความเรียบร้อยและสายตายุทธศาสตร์ ช่วยเต้าตงสร้างอาณาจักร',
		introduction_idn = 'Xun Yu, seorang penasihat cerdas pada akhir Dinasti Han Timur, terkenal karena keanggunan dan penglihatan strategis yang membantu Cao Cao membangun karirnya.',
		introduction_cn = '荀彧，东汉末年谋士，文雅超脱，有战略眼光，助曹操立业。',
		introduction_kr = '손욱은 동한 말기의 재능 있는 책사로 우아하고 담백하며, 조조가 세력을 쌓는 데 전략적 안목으로 기여하였다.',
		location = 'Assist, Prolong Poison',
		location_vn = 'Assist, Prolong Poison',
		location_en = 'Assist: Prolong Poison',
		location_kr = '지원, 중독 지속 연장',
		location_th = 'ช่วยเหลือ, ยืดระยะเวลาพิษ',
		location_idn = 'Membantu, Memperpanjang Racun',
		location_cn = '协助，延长中毒时间'
	},
	[171] = {
		id = 171,
		name = 'Pháp Chính',
		name_vn = 'Pháp Chính',
		name_en = 'Fa Zheng',
		name_th = 'ฝ่าฉิง',
		name_idn = 'Pejabat Hukum',
		name_cn = '法正',
		name_kr = '법정',
		cardMarkID = 171,
		atkType = {8},
		zawakeFragID = 50171,
		chaRecom = {22, 24, 18, 20},
		specValue = {45, 30, 50, 55, 75, 65, 320},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 2,
		unitID = 171,
		innateSkillID = 1713,
		skillList = {1711, 1712, 1713, 1716, 61151, 61152},
		starSkillSeqID = 171,
		fragID = 20171,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 171,
		abilitySeqID = 11,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {11, 12, 13},
		introduction = 'Pháp Chính là thủ hạ của Lưu Chương. Sau này, khi Lưu Bị vào đất Thục, Pháp Chính về làm mưu thần cho Lưu Bị, rất được tín nhiệm và kính trọng.',
		introduction_vn = 'Pháp Chính là thủ hạ của Lưu Chương. Sau này, khi Lưu Bị vào đất Thục, Pháp Chính về làm mưu thần cho Lưu Bị, rất được tín nhiệm và kính trọng.',
		introduction_en = 'Fa Zheng was a subordinate of Liu Zhang. Later, when Liu Bei entered Shu, Fa Zheng became his trusted strategist, earning great respect and trust.',
		introduction_th = 'ฟาฉิงเป็นผู้ใต้บังคับบัญชาของหลิวจวง ต่อมาหลิวเป่ยเข้าดินแดนฉู่ ฟาฉิงรับใช้เป็นที่ปรึกษา มีความน่าเชื่อถือและเคารพอย่างสูง',
		introduction_idn = 'Fa Zheng adalah bawahan Liu Zhang. Setelah Liu Bei masuk ke wilayah Shu, Fa Zheng menjadi penasihat Liu Bei, sangat dipercaya dan dihormati.',
		introduction_cn = '法正是刘璋部下，后来刘备入蜀，法正归顺刘备，成为其谋士，备受信任与尊敬。',
		introduction_kr = '법정은 유장의 부하였으며, 이후 유비가 촉으로 들어가자 유비의 책사로 중용되어 신임과 존경을 받았다.',
		location = 'Poison-type damage',
		location_vn = 'Poison-type damage',
		location_en = 'Poison-type Damage',
		location_kr = '독 속성 피해',
		location_th = 'ความเสียหายประเภทพิษ',
		location_idn = 'Kerusakan Tipe Racun',
		location_cn = '毒系伤害'
	},
	[191] = {
		id = 191,
		name = 'Thị Nữ',
		name_vn = 'Thị Nữ',
		name_en = 'Maid',
		name_th = 'หญิงรับใช้',
		name_idn = 'Dayang',
		name_cn = '侍女',
		name_kr = '시녀',
		cardMarkID = 191,
		atkType = {7},
		zawakeFragID = 50191,
		chaRecom = {24, 25, 11, 12},
		specValue = {60, 45, 55, 50, 40, 55, 305},
		feelItems = {609, 610, 611, 602, 603},
		unitID = 191,
		innateSkillID = 1913,
		skillList = {1911, 1912, 1913, 1916, 61101, 61102},
		starSkillSeqID = 191,
		equipsList = {11001, 11101, 11201, 11301},
		fragID = 20191,
		star = 2,
		starEffectIndex = 191,
		abilitySeqID = 14,
		fetterList = {1911, 1912, 1913},
		introduction = 'Venonat, an Insect Heroes. Its eyes function as radar, allowing it to see in the dark.',
		introduction_vn = 'Venonat, an Insect Heroes. Its eyes function as radar, allowing it to see in the dark.',
		introduction_en = 'Venonat, an Insect Hero. Its eyes function as radar, allowing it to see in the dark.',
		introduction_th = 'เวนนอนัต ฮีโร่แมลง ตาของมันทำหน้าที่เหมือนเรดาร์ ช่วยให้มองเห็นในที่มืด',
		introduction_idn = 'Venonat, Pahlawan Serangga. Matanya berfungsi sebagai radar, memungkinkan ia melihat dalam gelap.',
		introduction_cn = '毛球，虫宝可梦。其眼睛如雷达，能在黑暗中视物。',
		introduction_kr = '독눈충은 곤충 영웅으로, 눈이 레이더처럼 작동하여 어두운 곳도 볼 수 있다.',
		location = 'Poison-type damage: Stealing both attack stats',
		location_vn = 'Poison-type damage: Stealing both attack stats',
		location_en = 'Poison-type Damage: Steal Both Attack Stats',
		location_kr = '독 속성 피해: 공격 능력치 둘 다 흡수',
		location_th = 'ความเสียหายประเภทพิษ: ขโมยสถิติการโจมตีทั้งสอง',
		location_idn = 'Kerusakan Tipe Racun: Mencuri Kedua Statistik Serangan',
		location_cn = '毒系伤害：偷取双攻击属性'
	},
	[231] = {
		id = 231,
		name = 'Thích Khách',
		name_vn = 'Thích Khách',
		name_en = 'Assassin',
		name_th = 'นักฆ่า',
		name_idn = 'Pembunuh Bayaran',
		name_cn = '刺客',
		name_kr = '척객',
		cardMarkID = 231,
		atkType = {7},
		zawakeFragID = 50231,
		chaRecom = {7, 8, 9, 12},
		specValue = {40, 70, 80, 35, 35, 45, 305},
		feelItems = {612, 613, 614, 602, 603},
		unitID = 231,
		innateSkillID = 2313,
		skillList = {2311, 2312, 2313, 2316, 61101, 61102},
		starSkillSeqID = 231,
		equipsList = {11001, 11101, 11201, 11301},
		fragID = 20231,
		star = 2,
		starEffectIndex = 231,
		abilitySeqID = 11,
		fetterList = {2311, 2312, 2313},
		introduction = 'Mankey, a Pig Monkey Heroes of the Fighting-type. Known for its superior footwork, it packs a powerful punch. Although Mankey is usually calm, it does have a bad temper. Once Mankey begins to Thrash, stopping it is impossible.',
		introduction_vn = 'Mankey, a Pig Monkey Heroes of the Fighting-type. Known for its superior footwork, it packs a powerful punch. Although Mankey is usually calm, it does have a bad temper. Once Mankey begins to Thrash, stopping it is impossible.',
		introduction_en = 'Mankey, a Fighting-type Pig Monkey Hero. Known for its superior footwork, it packs a powerful punch. Although usually calm, Mankey has a bad temper. Once it begins to Thrash, stopping it is impossible.',
		introduction_th = 'แมนคีย์ ฮีโร่ลิงหมูประเภทต่อสู้ มีฝีเท้าดีและหมัดหนัก แม้ปกติแมนคีย์จะใจเย็น แต่มีอารมณ์รุนแรง เมื่อเริ่มถล่มโจมตีหยุดไม่ได้',
		introduction_idn = 'Mankey, Pahlawan Monyet Babi tipe Petarung. Dikenal dengan gerakan kaki unggul, pukulannya kuat. Meskipun biasanya tenang, Mankey memiliki sifat pemarah. Setelah mulai mengamuk, ia sulit dihentikan.',
		introduction_cn = '猴怪，格斗属性的猪猴英雄。以敏捷脚步和强力拳击著称。虽通常冷静，但脾气暴躁。一旦发狂，难以制止。',
		introduction_kr = '괴력몬은 격투 타입의 돼지원숭이 영웅이다. 뛰어난 발놀림과 강력한 주먹을 지녔으며, 평소에는 온순하지만 성격이 난폭하다. 한번 격분하면 멈출 수 없다.',
		location = 'Burst damage: attacks both allies and enemies',
		location_vn = 'Burst damage: attacks both allies and enemies',
		location_en = 'Burst Damage: Hits Allies and Enemies',
		location_kr = '폭발 피해: 아군과 적 모두 공격',
		location_th = 'ความเสียหายระเบิด: โจมตีทั้งพันธมิตรและศัตรู',
		location_idn = 'Kerusakan Ledakan: Menyerang Sekutu dan Musuh',
		location_cn = '爆发伤害：攻击友军与敌军'
	},
	[241] = {
		id = 241,
		name = 'Tôn Sách',
		name_vn = 'Tôn Sách',
		name_en = 'Sun Ce',
		name_th = 'ซุนซือ',
		name_idn = 'Sun Ce',
		name_cn = '孙策',
		name_kr = '손책',
		cardMarkID = 241,
		atkType = {7},
		zawakeID = 241,
		zawakeFragID = 50241,
		chaRecom = {10, 11, 13, 9},
		specValue = {55, 60, 70, 45, 70, 50, 350},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {618, 619, 620, 602, 603},
		levelExpID = 3,
		unitID = 241,
		innateSkillID = 2413,
		skillList = {2411, 2412, 2413, 2416, 61101, 61102},
		starSkillSeqID = 241,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20241,
		fragNumType = 2,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 241,
		effortSeqID = 3,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {2411, 2412, 2413, 2414},
		introduction = 'Tôn Sách là người đặt nền móng quan trọng trong việc hình thành nên Đông Ngô, đã được người em trai Tôn Quyền kế thừa và hoàn thiện sau khi ông mất.',
		introduction_vn = 'Tôn Sách là người đặt nền móng quan trọng trong việc hình thành nên Đông Ngô, đã được người em trai Tôn Quyền kế thừa và hoàn thiện sau khi ông mất.',
		introduction_en = 'Sun Ce laid the important foundation for the establishment of Eastern Wu, which was inherited and perfected by his younger brother Sun Quan after his death.',
		introduction_th = 'ซุ่นซาเป็นผู้วางรากฐานสำคัญในการก่อตั้งตงงู โดยได้รับการสืบทอดและพัฒนาต่อโดยน้องชายซุ่นชวนหลังจากเขาเสียชีวิต',
		introduction_idn = 'Sun Ce adalah pendiri penting dalam pembentukan Wu Timur, yang kemudian diwariskan dan disempurnakan oleh adiknya Sun Quan setelah kematiannya.',
		introduction_cn = '孙策为东吴奠基人，逝后由弟弟孙权继承完善基业。',
		introduction_kr = '손책은 동오 건국의 기초를 다진 인물로, 형 손권이 그의 뜻을 이어 완성하였다.',
		location = 'Savage damage: Burning life',
		location_vn = 'Savage damage: Burning life',
		location_en = 'Savage Damage: Life Burn',
		location_kr = '광폭 피해: 생명 불태우기',
		location_th = 'ความเสียหายดุร้าย: เผาผลาญชีวิต',
		location_idn = 'Kerusakan Buas: Membakar Kehidupan',
		location_cn = '狂暴伤害：燃烧生命'
	},
	[261] = {
		id = 261,
		name = 'Điêu Thuyền',
		name_vn = 'Điêu Thuyền',
		name_en = 'Diao Chan',
		name_th = 'เตียวเทียน',
		name_idn = 'Diao Chan',
		name_cn = '貂蝉',
		name_kr = '조운',
		cardMarkID = 261,
		atkType = {8},
		zawakeID = 261,
		zawakeFragID = 50261,
		chaRecom = {6, 7, 18, 19},
		specValue = {25, 90, 20, 15, 105, 55, 310},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {606, 607, 608, 602, 603},
		levelExpID = 3,
		unitID = 261,
		innateSkillID = 2613,
		skillList = {2611, 2612, 2613, 2616, 61101, 61102},
		starSkillSeqID = 261,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20261,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 261,
		abilitySeqID = 3,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {2611, 2612, 2613, 2614},
		introduction = 'Điêu Thuyền, một trong tứ đại mỹ nhân, nghĩa nữ của Vương Doãn, để đền ơn nghĩa phụ đã tình nguyện hiến thân hoàn thành liên hoàn kế diệt Đổng Trác.',
		introduction_vn = 'Điêu Thuyền, một trong tứ đại mỹ nhân, nghĩa nữ của Vương Doãn, để đền ơn nghĩa phụ đã tình nguyện hiến thân hoàn thành liên hoàn kế diệt Đổng Trác.',
		introduction_en = 'Diao Chan, one of the Four Beauties, adopted daughter of Wang Yun, voluntarily sacrificed herself to carry out the chain plan to eliminate Dong Zhuo out of gratitude to her foster father.',
		introduction_th = 'เตียวเทียน หนึ่งในสี่สาวงามผู้รับเลี้ยงโดยหวังหยวน เพื่อทดแทนบุญคุณพ่อเลี้ยง ได้อาสามอบตัวทำแผนลับสังหารตงจั๋ว',
		introduction_idn = 'Diao Chan, salah satu dari Empat Wanita Cantik Besar, anak angkat Wang Yun, demi membalas budi ayah angkatnya rela mengorbankan diri untuk melaksanakan rencana berantai menghancurkan Dong Zhuo.',
		introduction_cn = '貂蝉，四大美女之一，王允义女，为报答恩情自愿献身，完成连环计灭董卓。',
		introduction_kr = '조비는 중국 4대 미녀 중 하나로, 왕윤의 양녀이다. 은혜를 갚기 위해 연환계를 완성하여 동탁을 제거하는 데 헌신하였다.',
		location = 'One speed hard control: AOE Disable',
		location_vn = 'One speed hard control: AOE Disable',
		location_en = 'Single Speed Hard Control: AOE Disable',
		location_kr = '단일 속도 강제 제어: 광역 무력화',
		location_th = 'การควบคุมความเร็วสูงเพียงหนึ่งเดียว: ปิดการใช้งานแบบวงกว้าง',
		location_idn = 'Kontrol Keras Kecepatan Tunggal: Disable AOE',
		location_cn = '单速硬控：范围禁用'
	},
	[271] = {
		id = 271,
		name = 'Trương Bao',
		name_vn = 'Trương Bao',
		name_en = 'Zhang Bao',
		name_th = 'จางเป่า',
		name_idn = 'Zhang Bao',
		name_cn = '张宝',
		name_kr = '장포',
		cardMarkID = 271,
		atkType = {7},
		zawakeID = 271,
		zawakeFragID = 50271,
		chaRecom = {11, 12, 15, 16},
		specValue = {70, 35, 80, 50, 35, 35, 305},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {612, 613, 614, 602, 603},
		levelExpID = 2,
		unitID = 271,
		skinSkillMap = {[110] = {2721, 2722, 2723, 2726, 61101, 61102}, __size = 1},
		innateSkillID = 2713,
		skillList = {2711, 2712, 2713, 2716, 61101, 61102},
		starSkillSeqID = 271,
		fragID = 20271,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 271,
		abilitySeqID = 16,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {2711, 2712, 2713},
		introduction = 'Trương Bao là con trai cả của Trương Phi, danh tướng nhà Thục Hán thời Tam Quốc.',
		introduction_vn = 'Trương Bao là con trai cả của Trương Phi, danh tướng nhà Thục Hán thời Tam Quốc.',
		introduction_en = 'Zhang Bao was the eldest son of Zhang Fei, a famous general of Shu Han during the Three Kingdoms.',
		introduction_th = 'จางเป่า บุตรชายคนโตของจางเฟย ขุนพลชื่อดังแห่งรัฐฉู่ฮั่นในยุคสามก๊ก',
		introduction_idn = 'Zhang Bao adalah putra sulung Zhang Fei, jenderal terkenal dari Shu Han pada masa Tiga Kerajaan.',
		introduction_cn = '张苞，张飞长子，蜀汉名将。',
		introduction_kr = '장포는 장비의 장남으로, 촉한의 명장이다.',
		location = 'Abandon Defense & Extreme Attack',
		location_vn = 'Abandon Defense & Extreme Attack',
		location_en = 'Abandon Defense & Extreme Offense',
		location_kr = '방어 포기 & 극한 공격',
		location_th = 'ทิ้งการป้องกัน & โจมตีอย่างสุดขีด',
		location_idn = 'Mengabaikan Pertahanan & Serangan Ekstrem',
		location_cn = '舍弃防御&极限攻击'
	},
	[281] = {
		id = 281,
		name = 'Tôn Lỗ Ban',
		name_vn = 'Tôn Lỗ Ban',
		name_en = 'Sun Luban',
		name_th = 'ซุนลู่ปาน',
		name_idn = 'Sun Luban',
		name_cn = '孙鲁班',
		name_kr = '손로반',
		cardMarkID = 281,
		atkType = {7},
		zawakeID = 281,
		zawakeFragID = 50281,
		chaRecom = {10, 11, 13, 9},
		specValue = {50, 40, 75, 35, 70, 30, 300},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 2,
		unitID = 281,
		skinSkillMap = {[128] = {2821, 2822, 2823, 2826, 61101, 61102}, __size = 1},
		innateSkillID = 2813,
		skillList = {2811, 2812, 2813, 2816, 61151, 61152},
		starSkillSeqID = 281,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20281,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 281,
		abilitySeqID = 16,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {2811, 2812, 2813},
		introduction = 'Tôn Lỗ Ban là con gái lớn của Tôn Quyền và Bộ Luyện Sư. Tôn Lỗ Ban từng liên hợp Tôn Tuấn bài trừ thế lực đối lập. Về sau, việc bại lộ nên bị lưu đày.',
		introduction_vn = 'Tôn Lỗ Ban là con gái lớn của Tôn Quyền và Bộ Luyện Sư. Tôn Lỗ Ban từng liên hợp Tôn Tuấn bài trừ thế lực đối lập. Về sau, việc bại lộ nên bị lưu đày.',
		introduction_en = 'Sun Luban was the eldest daughter of Sun Quan and Bu Lianshi. She once allied with Sun Jun to eliminate opposing factions. Later, after the plot was exposed, she was exiled.',
		introduction_th = 'ซุ่นลู่ปาน ลูกสาวคนโตของซุ่นชวนและปู่เหลียนซือ เคยร่วมมือกับซุ่นจวินกำจัดฝ่ายตรงข้าม หลังจากแผนถูกเปิดเผยจึงถูกเนรเทศ',
		introduction_idn = 'Sun Luban adalah putri tertua Sun Quan dan Bu Lian Shi. Sun Luban pernah bekerja sama dengan Sun Jun untuk menyingkirkan kekuatan oposisi. Setelah rencana terbongkar, ia diasingkan.',
		introduction_cn = '孙鲁班，孙权与步练师长女。曾联手孙峻铲除异己，后败露被流放。',
		introduction_kr = '손로반은 손권과 보련서의 큰딸로, 손준과 연합하여 반대 세력을 제거했으나 발각되어 유배당했다.',
		location = 'Damage debuff',
		location_vn = 'Damage debuff',
		location_en = 'Damage Debuff',
		location_kr = '피해 디버프',
		location_th = 'ลดความเสียหาย',
		location_idn = 'Debuff Kerusakan',
		location_cn = '伤害减益'
	},
	[301] = {
		id = 301,
		name = 'Đinh Phụng',
		name_vn = 'Đinh Phụng',
		name_en = 'Ding Feng',
		name_th = 'ติ่งเฟิง',
		name_idn = 'Ding Feng',
		name_cn = '丁凤',
		name_kr = '정봉',
		cardMarkID = 301,
		atkType = {7},
		zawakeID = 301,
		zawakeFragID = 50301,
		chaRecom = {15, 16, 11, 12},
		specValue = {40, 20, 80, 100, 30, 30, 300},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {615, 616, 617, 602, 603},
		levelExpID = 2,
		unitID = 301,
		innateSkillID = 3013,
		skillList = {3011, 3012, 3013, 3016, 61151, 61152},
		starSkillSeqID = 301,
		fragID = 20301,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 301,
		abilitySeqID = 17,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {3011, 3012, 3013},
		introduction = 'Đinh Phụng là tướng lĩnh suốt 4 đời vua Đông Ngô, chứng kiến toàn bộ quá trình hưng vong của Tam Quốc.',
		introduction_vn = 'Đinh Phụng là tướng lĩnh suốt 4 đời vua Đông Ngô, chứng kiến toàn bộ quá trình hưng vong của Tam Quốc.',
		introduction_en = 'Ding Feng was a general who served through four generations of Eastern Wu rulers, witnessing the entire rise and fall of the Three Kingdoms.',
		introduction_th = 'ติ่งเฟิง นายทหารตงงูที่รับใช้สี่ยุคราชวงศ์ เห็นการล่มสลายของยุคสามก๊กทั้งหมด',
		introduction_idn = 'Ding Feng adalah jenderal yang bertugas selama 4 generasi raja Wu Timur, menyaksikan seluruh proses kebangkitan dan kejatuhan Tiga Kerajaan.',
		introduction_cn = '丁奉，东吴四代君主的将领，见证三国兴衰。',
		introduction_kr = '정봉은 동오 4대 왕을 섬긴 장수로 삼국의 흥망성쇠를 목격했다.',
		location = 'Taunt shield',
		location_vn = 'Taunt shield',
		location_en = 'Taunt Shield',
		location_kr = '도발 방패',
		location_th = 'โล่ล่อเป้า',
		location_idn = 'Perisai Provokasi',
		location_cn = '嘲讽护盾'
	},
	[331] = {
		id = 331,
		name = 'Phục Thọ',
		name_vn = 'Phục Thọ',
		name_en = 'Fu Shou',
		name_th = 'ฟู่โถว',
		name_idn = 'Fu Shou',
		name_cn = '复寿',
		name_kr = '복수',
		cardMarkID = 331,
		atkType = {8},
		zawakeID = 331,
		zawakeFragID = 50331,
		gender = 0,
		chaRecom = {18, 20, 15, 14},
		specValue = {25, 45, 35, 70, 95, 55, 325},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {612, 613, 614, 602, 603},
		levelExpID = 3,
		unitID = 331,
		innateSkillID = 3313,
		skillList = {3311, 3312, 3313, 3316, 61101, 61102},
		starSkillSeqID = 331,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20331,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 331,
		abilitySeqID = 4,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {3311, 3312, 3313, 3314},
		introduction = 'Phục Thọ, Phục Hoảng Hậu, người vợ đầu tiên của Hán Hiến Đế Lưu Hiệp.',
		introduction_vn = 'Phục Thọ, Phục Hoảng Hậu, người vợ đầu tiên của Hán Hiến Đế Lưu Hiệp.',
		introduction_en = 'Empress Fu Shou, first wife of Emperor Xian of Han, Liu Xie.',
		introduction_th = 'ฝู่โสว ฝู่หวงโฮ่ว ภรรยาคนแรกของฮั่นเซียนตี้ หลิวเสี้ยะ',
		introduction_idn = 'Fu Shou, Permaisuri Fu Huang, istri pertama Kaisar Han Xian Di Liu Xie.',
		introduction_cn = '伏寿，伏皇后，汉献帝刘协的第一任妻子。',
		introduction_kr = '복수, 복황후는 한 헌제 유협의 첫 번째 부인이다.',
		location = 'Self-desteuction bomb',
		location_vn = 'Self-desteuction bomb',
		location_en = 'Self-Destruction Bomb',
		location_kr = '자폭 폭탄',
		location_th = 'ระเบิดทำลายตัวเอง',
		location_idn = 'Bom Penghancur Diri',
		location_cn = '自爆炸弹'
	},
	[341] = {
		id = 341,
		name = 'Đổng Bạch',
		name_vn = 'Đổng Bạch',
		name_en = 'Dong Bai',
		name_th = 'ตงป๋า',
		name_idn = 'Dong Bai',
		name_cn = '董白',
		name_kr = '동백',
		cardMarkID = 341,
		atkType = {7},
		zawakeID = 341,
		zawakeFragID = 50341,
		chaRecom = {8, 9, 13, 12},
		specValue = {52, 60, 90, 55, 58, 62, 377},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {606, 607, 608, 602, 603},
		levelExpID = 2,
		unitID = 341,
		innateSkillID = 3413,
		skillList = {3411, 3412, 3413, 3416, 61101, 61102},
		starSkillSeqID = 341,
		fragID = 20341,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 341,
		abilitySeqID = 16,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {3411, 3412, 3413},
		introduction = 'Đổng Bạch, cháu gái Đổng Trác, rất được sủng ái, được phong làm Vị Dương Quân.',
		introduction_vn = 'Đổng Bạch, cháu gái Đổng Trác, rất được sủng ái, được phong làm Vị Dương Quân.',
		introduction_en = 'Dong Bai, niece of Dong Zhuo, was highly favored and titled Lady Weiyang.',
		introduction_th = 'ตงป๋า หลานสาวของตงจั๋ว ได้รับความโปรดปรานอย่างมาก ได้รับตำแหน่งวีหยางจวิน',
		introduction_idn = 'Dong Bai, keponakan perempuan Dong Zhuo, sangat dimanjakan, diberi gelar Vi Dương Quân.',
		introduction_cn = '董白，董卓侄女，深受宠爱，被封为魏阳君。',
		introduction_kr = '동백은 동탁의 조카딸로 총애받아 위양군에 봉해졌다.',
		location = 'Sweet Meat',
		location_vn = 'Sweet Meat',
		location_en = 'Sweet Meat',
		location_kr = '달콤한 고기',
		location_th = 'เนื้อหวาน',
		location_idn = 'Daging Manis',
		location_cn = '甜美肉块'
	},
	[351] = {
		id = 351,
		name = 'Vệ Binh',
		name_vn = 'Vệ Binh',
		name_en = 'Guard',
		name_th = 'ทหารรักษาพระองค์',
		name_idn = 'Pengawal',
		name_cn = '卫兵',
		name_kr = '위병',
		cardMarkID = 351,
		atkType = {7},
		zawakeFragID = 50351,
		chaRecom = {7, 8, 9, 12},
		specValue = {35, 75, 85, 45, 35, 35, 310},
		feelItems = {606, 607, 608, 602, 603},
		unitID = 351,
		innateSkillID = 3513,
		skillList = {3511, 3512, 3513, 3516, 61101, 61102},
		starSkillSeqID = 351,
		equipsList = {11001, 11101, 11201, 11301},
		fragID = 20351,
		star = 2,
		starEffectIndex = 351,
		abilitySeqID = 11,
		fetterList = {3511, 3512, 3513},
		introduction = 'Doduo, the Twin Bird Heroes. It runs faster than it can fly.',
		introduction_vn = 'Doduo, the Twin Bird Heroes. It runs faster than it can fly.',
		introduction_en = 'Doduo, the Twin Bird Hero. It runs faster than it can fly.',
		introduction_th = 'โดดู ฮีโร่นกคู่ วิ่งเร็วกว่าบิน',
		introduction_idn = 'Doduo, Pahlawan Burung Kembar. Ia berlari lebih cepat daripada terbang.',
		introduction_cn = '嘟嘟，双鸟宝可梦。奔跑速度快于飞行速度。',
		introduction_kr = '두두는 쌍둥이 새 영웅으로, 날기보다 달리기가 빠르다.',
		location = 'Damage based on appearance',
		location_vn = 'Damage based on appearance',
		location_en = 'Damage Based on Appearance',
		location_kr = '외모 기반 피해',
		location_th = 'ความเสียหายขึ้นอยู่กับรูปลักษณ์',
		location_idn = 'Kerusakan Berdasarkan Penampilan',
		location_cn = '基于外观的伤害'
	},
	[371] = {
		id = 371,
		name = 'Giả Hủ',
		name_vn = 'Giả Hủ',
		name_en = 'Jia Xu',
		name_th = 'เกี๋ยฮู้',
		name_idn = 'Jia Xu',
		name_cn = '贾诩',
		name_kr = '가호',
		cardMarkID = 371,
		atkType = {7},
		zawakeID = 371,
		zawakeFragID = 50371,
		chaRecom = {11, 12, 15, 16},
		specValue = {80, 25, 40, 50, 80, 50, 325},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 2,
		unitID = 371,
		innateSkillID = 3713,
		skillList = {3711, 3712, 3713, 3716, 61151, 61152},
		starSkillSeqID = 371,
		fragID = 20371,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 371,
		abilitySeqID = 19,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {3711, 3712, 3713},
		introduction = 'Giả Hủ ban đầu theo phò trợ Đổng Trác, sau khi Đổng Trác bị tiêu diệt đã hàng Tào Tháo và trở thành quân sư thân cận của Tào Tháo.',
		introduction_vn = 'Giả Hủ ban đầu theo phò trợ Đổng Trác, sau khi Đổng Trác bị tiêu diệt đã hàng Tào Tháo và trở thành quân sư thân cận của Tào Tháo.',
		introduction_en = 'Jia Xu initially served Dong Zhuo. After Dong Zhuo\'s death, he surrendered to Cao Cao and became one of Cao Cao’s close strategists.',
		introduction_th = 'เก้าหู่ในตอนแรกรับใช้ตงจั๋ว หลังจากตงจั๋วถูกทำลาย ได้สวามิภักดิ์เต้าตงและกลายเป็นที่ปรึกษาใกล้ชิด',
		introduction_idn = 'Jia Xu awalnya mendukung Dong Zhuo, setelah Dong Zhuo tewas ia menyerah kepada Cao Cao dan menjadi penasihat dekatnya.',
		introduction_cn = '贾诩最初辅佐董卓，董卓败后投降曹操，成为其亲近军师。',
		introduction_kr = '가호는 처음에 동탁을 보좌하다 동탁이 제거된 후 조조에게 귀순하여 조조의 밀접한 책사가 되었다.',
		location = 'Debuff support: Reduces DEF when poisoned',
		location_vn = 'Debuff support: Reduces DEF when poisoned',
		location_en = 'Debuff Support: DEF Reduction When Poisoned',
		location_kr = '디버프 지원: 중독 시 방어력 감소',
		location_th = 'สนับสนุนดีบัฟ: ลดค่าป้องกันเมื่อถูกพิษ',
		location_idn = 'Dukungan Debuff: Mengurangi DEF Saat Diracun',
		location_cn = '减益辅助：中毒时降低防御'
	},
	[381] = {
		id = 381,
		name = 'Trương Hợp',
		name_vn = 'Trương Hợp',
		name_en = 'Zhang He',
		name_th = 'จางเหอ',
		name_idn = 'Zhang He',
		name_cn = '张郃',
		name_kr = '장합',
		cardMarkID = 381,
		atkType = {8},
		zawakeFragID = 50381,
		chaRecom = {15, 16, 11, 12},
		specValue = {30, 40, 65, 100, 45, 25, 305},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 3,
		unitID = 381,
		innateSkillID = 3813,
		skillList = {3811, 3812, 3813, 3816, 61151, 61152},
		starSkillSeqID = 381,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20381,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 381,
		abilitySeqID = 2,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {3811, 3812, 3813, 3814},
		introduction = 'Trương Hợp là một trong Ngũ Hổ Tướng của Tào Ngụy. Trương Hợp tử trận trong lần Bắc Phạt thứ tư của Gia Cát Lượng khi bị trúng mai phục.',
		introduction_vn = 'Trương Hợp là một trong Ngũ Hổ Tướng của Tào Ngụy. Trương Hợp tử trận trong lần Bắc Phạt thứ tư của Gia Cát Lượng khi bị trúng mai phục.',
		introduction_en = 'Zhang He was one of the Five Tiger Generals of Cao Wei. He died in battle during Zhuge Liang’s fourth northern campaign after falling into an ambush.',
		introduction_th = 'จางเหอหนึ่งในห้าผู้กล้าหาญของเต้าหวี่ เสียชีวิตในสมรภูมิเบยฝ่าครั้งที่สี่ของเจียฉาเหลียง หลังถูกซุ่มโจมตี',
		introduction_idn = 'Zhang He adalah salah satu dari Lima Harimau Jenderal Cao Wei. Zhang He gugur dalam serangan keempat Zhuge Liang ke utara akibat serangan jebakan.',
		introduction_cn = '张郃，曹魏五虎将之一。第四次北伐中遭伏击阵亡。',
		introduction_kr = '장합은 조위의 오호 장군 중 하나이다. 제갈량의 네 번째 북벌 때 매복에 걸려 전사하였다.'
	},
	[391] = {
		id = 391,
		name = 'Trương Giác',
		name_vn = 'Trương Giác',
		name_en = 'Zhang Jiao',
		name_th = 'จางเจี๋ย',
		name_idn = 'Zhang Jue',
		name_cn = '张角',
		name_kr = '장각',
		cardMarkID = 391,
		atkType = {8},
		zawakeID = 391,
		zawakeFragID = 50391,
		chaRecom = {6, 7, 18, 19},
		specValue = {30, 80, 35, 30, 100, 35, 310},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 3,
		unitID = 391,
		innateSkillID = 3913,
		skillList = {3911, 3912, 3913, 3916, 61101, 61102},
		starSkillSeqID = 391,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20391,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 391,
		abilitySeqID = 103,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {3911, 3912, 3913, 3914},
		introduction = 'Trương Giác là thủ lĩnh cuộc khởi nghĩa Khăn Vàng, hay còn gọi là quân Khăn Vàng vào cuối thời kỳ nhà Đông Hán.',
		introduction_vn = 'Trương Giác là thủ lĩnh cuộc khởi nghĩa Khăn Vàng, hay còn gọi là quân Khăn Vàng vào cuối thời kỳ nhà Đông Hán.',
		introduction_en = 'Zhang Jiao was the leader of the Yellow Turban Rebellion, also known as the Yellow Turban Army, at the end of the Eastern Han period.',
		introduction_th = 'จางเจี๋ย ผู้นำการกบฏผ้าผืนเหลือง หรือที่เรียกว่ากองทัพผ้าผืนเหลืองในช่วงปลายยุคตงฮั่น',
		introduction_idn = 'Zhang Jiao adalah pemimpin pemberontakan Kepala Kuning, juga dikenal sebagai pasukan Kepala Kuning pada akhir Dinasti Han Timur.',
		introduction_cn = '张角，黄巾军首领，东汉末年黄巾军起义领导者。',
		introduction_kr = '장각은 황건적의 수령으로, 동한 말기의 황건적 군대를 이끌었다.',
		location = 'Dealing strong yet soft status condition: AOE flinch',
		location_vn = 'Dealing strong yet soft status condition: AOE flinch',
		location_en = 'Strong yet Subtle Status: AOE Flinch',
		location_kr = '강하지만 부드러운 상태 이상 부여: 광역 움찔',
		location_th = 'สถานะอ่อนแอที่รุนแรงแต่เบา: ทำให้ถอยหลังแบบวงกว้าง',
		location_idn = 'Menimbulkan Kondisi Status Kuat namun Lembut: Flinch AOE',
		location_cn = '强而柔的状态异常：范围畏缩'
	},
	[401] = {
		id = 401,
		name = 'Mạnh Hoạch',
		name_vn = 'Mạnh Hoạch',
		name_en = 'Meng Huo',
		name_th = 'เหม่งเหว่ย',
		name_idn = 'Meng Huo',
		name_cn = '孟获',
		name_kr = '맹획',
		cardMarkID = 401,
		atkType = {7},
		zawakeID = 401,
		zawakeFragID = 50401,
		chaRecom = {6, 8, 14, 16},
		specValue = {35, 70, 45, 160, 30, 45, 385},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {615, 616, 617, 602, 603},
		levelExpID = 3,
		unitID = 401,
		innateSkillID = 4013,
		skillList = {4011, 4012, 4013, 4016, 61151, 61152},
		starSkillSeqID = 401,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20401,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 401,
		abilitySeqID = 2,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {4011, 4012, 4013, 4014},
		introduction = 'Mạnh Hoạch, thủ lĩnh của các bộ lạc ở phía nam đất Thục, dũng mãnh kiên cường.',
		introduction_vn = 'Mạnh Hoạch, thủ lĩnh của các bộ lạc ở phía nam đất Thục, dũng mãnh kiên cường.',
		introduction_en = 'Meng Huo, leader of the southern tribes in Shu territory, was fierce and resilient.',
		introduction_th = 'มั่งเหอ ผู้นำชนเผ่าทางใต้ของฉู่ มีความกล้าหาญและอดทน',
		introduction_idn = 'Meng Huo, pemimpin suku di selatan Shu, kuat dan tangguh.',
		introduction_cn = '孟获，蜀地南方部族首领，勇猛坚韧。',
		introduction_kr = '맹획은 촉 남부 부족의 우두머리로 용맹하고 강인하다.',
		location = 'Steel Counter-attack Shield',
		location_vn = 'Steel Counter-attack Shield',
		location_en = 'Steel Counterattack Shield',
		location_kr = '강철 반격 방패',
		location_th = 'โล่ตอบโต้เหล็กกล้า',
		location_idn = 'Perisai Balasan Baja',
		location_cn = '钢铁反击护盾'
	},
	[451] = {
		id = 451,
		name = 'Đao Binh',
		name_vn = 'Đao Binh',
		name_en = 'Swordsman',
		name_th = 'ทหารดาบ',
		name_idn = 'Prajurit Pedang',
		name_cn = '刀兵',
		name_kr = '도병',
		cardMarkID = 451,
		atkType = {7},
		zawakeFragID = 50451,
		chaRecom = {15, 16, 11, 12},
		specValue = {50, 35, 50, 95, 40, 50, 320},
		feelItems = {609, 610, 611, 602, 603},
		unitID = 451,
		innateSkillID = 4513,
		skillList = {4511, 4512, 4513, 4516, 61101, 61102},
		starSkillSeqID = 451,
		equipsList = {11001, 11101, 11201, 11301},
		fragID = 20451,
		star = 2,
		starEffectIndex = 451,
		abilitySeqID = 13,
		fetterList = {4511, 4512, 4513},
		introduction = 'Wears the skull of its deceased mother. When it becomes lonesome, it is said to cry loudly.',
		introduction_vn = 'Wears the skull of its deceased mother. When it becomes lonesome, it is said to cry loudly.',
		introduction_en = 'Wears the skull of its deceased mother. When it becomes lonesome, it is said to cry loudly.',
		introduction_th = 'สวมกะโหลกแม่ที่ตายแล้ว เมื่อเหงาเศร้าจะร้องไห้เสียงดัง',
		introduction_idn = 'Memakai tengkorak ibunya yang sudah meninggal. Saat kesepian, dikatakan ia menangis keras.',
		introduction_cn = '戴着亡母的头骨。孤独时会大声哭泣。',
		introduction_kr = '죽은 어머니의 두개골을 쓰고 다니며, 외로울 때는 크게 운다고 한다.',
		location = 'Single target stun',
		location_vn = 'Single target stun',
		location_en = 'Single-target Stun',
		location_kr = '단일 대상 기절',
		location_th = 'ทำให้เป้าหมายเดียวช็อก',
		location_idn = 'Stun Sasaran Tunggal',
		location_cn = '单体眩晕'
	},
	[461] = {
		id = 461,
		name = 'Tôn Quyền',
		name_vn = 'Tôn Quyền',
		name_en = 'Sun Quan',
		name_th = 'ซุนกว้าน',
		name_idn = 'Sun Quan',
		name_cn = '孙权',
		name_kr = '손권',
		cardMarkID = 461,
		atkType = {7},
		zawakeFragID = 50461,
		gender = 1,
		chaRecom = {7, 8, 9, 12},
		specValue = {35, 35, 35, 35, 35, 35, 210},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {612, 613, 614, 602, 603},
		levelExpID = 2,
		unitID = 461,
		innateSkillID = 4613,
		skillList = {4611, 4612, 4613, 4616, 61101, 61102},
		starSkillSeqID = 461,
		fragID = 20461,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 461,
		abilitySeqID = 16,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {4611, 4612, 4613},
		introduction = 'Tôn Quyền kế nghiệp Giang Đông từ tay huynh trưởng Tôn Sách. Sau cùng Lưu Bị thiết lập Liên minh Tôn Lưu đánh bại vạn đại quân Tào Tháo',
		introduction_vn = 'Tôn Quyền kế nghiệp Giang Đông từ tay huynh trưởng Tôn Sách. Sau cùng Lưu Bị thiết lập Liên minh Tôn Lưu đánh bại vạn đại quân Tào Tháo',
		introduction_en = 'Sun Quan succeeded leadership of Jiangdong from his elder brother Sun Ce. Eventually, Liu Bei formed an alliance with Sun Quan to defeat Cao Cao’s massive army.',
		introduction_th = 'ซุ่นชวนสืบทอดราชบัลลังก์เจียงตงจากพี่ชายซุ่นซา สุดท้ายหลิวเป่ยจัดตั้งพันธมิตรซุ่น-หลิว ทำลายกองทัพเต้าตง',
		introduction_idn = 'Sun Quan mewarisi Jiangdong dari kakaknya Sun Ce. Akhirnya Liu Bei membentuk aliansi Sun-Liu untuk mengalahkan tentara Cao Cao yang sangat besar.',
		introduction_cn = '孙权继承江东基业，最终刘备与孙权结盟击败曹操百万大军。',
		introduction_kr = '손권은 손책의 뒤를 이어 강동을 계승하였고, 결국 유비와 손권 연합으로 조조의 만대군을 격파하였다.',
		location = 'Fancy boxer',
		location_vn = 'Fancy boxer',
		location_en = 'Fancy Boxer',
		location_kr = '화려한 복서',
		location_th = 'นักมวยหรู',
		location_idn = 'Petinju Gaya',
		location_cn = '花哨拳师'
	},
	[471] = {
		id = 471,
		name = 'Hạ Hầu Đôn',
		name_vn = 'Hạ Hầu Đôn',
		name_en = 'Xiahou Dun',
		name_th = 'ฮ่าฝูตุน',
		name_idn = 'Xiahou Dun',
		name_cn = '夏侯惇',
		name_kr = '하후돈',
		cardMarkID = 471,
		atkType = {7},
		zawakeID = 471,
		zawakeFragID = 50471,
		chaRecom = {24, 23, 14, 16},
		specValue = {90, 30, 55, 75, 60, 75, 385},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {606, 607, 608, 602, 603},
		levelExpID = 2,
		unitID = 471,
		innateSkillID = 4713,
		skillList = {4711, 4712, 4713, 4716, 61151, 61152},
		starSkillSeqID = 471,
		fragID = 20471,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 471,
		abilitySeqID = 17,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {4711, 4712, 4713},
		introduction = 'Hạ Hầu Đôn là công thần khai quốc nhà Tào Ngụy. Tính tình chính trực nhưng nóng nảy.',
		introduction_vn = 'Hạ Hầu Đôn là công thần khai quốc nhà Tào Ngụy. Tính tình chính trực nhưng nóng nảy.',
		introduction_en = 'Xiahou Dun was a founding general of Cao Wei. Known for his integrity but quick temper.',
		introduction_th = 'ฮ่าฮ่าวตุน ขุนพลผู้ร่วมก่อตั้งเต้าหวี่ มีนิสัยซื่อสัตย์แต่ใจร้อน',
		introduction_idn = 'Xiahou Dun adalah tokoh pendiri negara Cao Wei. Berwatak jujur tapi mudah marah.',
		introduction_cn = '夏侯惇，曹魏开国功臣，性格正直但急躁。',
		introduction_kr = '하후돈은 조위 개국 공신으로, 성격은 정직하지만 다혈질이다.',
		location = 'Damage taken by front line: Paralysis in rows',
		location_vn = 'Damage taken by front line: Paralysis in rows',
		location_en = 'Front Line Damage Taken: Paralysis in Rows',
		location_kr = '전열 피해 흡수: 행 마비',
		location_th = 'ความเสียหายที่ได้รับโดยแนวหน้า: อัมพาตเป็นแนว',
		location_idn = 'Kerusakan yang Diterima Barisan Depan: Kelumpuhan di Barisan',
		location_cn = '前排所受伤害：行列麻痹'
	},
	[501] = {
		id = 501,
		name = 'Trần Cung',
		name_vn = 'Trần Cung',
		name_en = 'Chen Gong',
		name_th = 'เฉินกง',
		name_idn = 'Chen Gong',
		name_cn = '陈宫',
		name_kr = '진공',
		cardMarkID = 501,
		atkType = {7},
		zawakeFragID = 50501,
		gender = 2,
		chaRecom = {6, 8, 22, 25},
		specValue = {100, 30, 5, 5, 15, 65, 220},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {606, 607, 608, 602, 603},
		levelExpID = 3,
		unitID = 501,
		innateSkillID = 5013,
		skillList = {5011, 5012, 5013, 5016, 61151, 61152},
		starSkillSeqID = 501,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20501,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 501,
		abilitySeqID = 5,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {5011, 5012, 5013, 5014},
		introduction = 'Trần Cung từng theo Tào Tháo, sau làm mưu sĩ cho Lữ Bố. Về sau vì không chịu hàng Tào nên Tào Tháo đành gạt nước mắt mà xử tử.',
		introduction_vn = 'Trần Cung từng theo Tào Tháo, sau làm mưu sĩ cho Lữ Bố. Về sau vì không chịu hàng Tào nên Tào Tháo đành gạt nước mắt mà xử tử.',
		introduction_en = 'Chen Gong initially followed Cao Cao, then served as strategist for Lü Bu. Later, because he refused to surrender to Cao Cao, Cao Cao reluctantly executed him.',
		introduction_th = 'จั่นกงเคยรับใช้เต้าตง ต่อมาทำหน้าที่ที่ปรึกษาให้ลู่ปู๋ หลังไม่ยอมสวามิภักดิ์เต้าตง จึงถูกประหารด้วยน้ำตา',
		introduction_idn = 'Chen Gong pernah mengikuti Cao Cao, kemudian menjadi penasihat Lü Bu. Karena menolak menyerah kepada Cao, Cao Cao dengan berat hati harus mengeksekusinya.',
		introduction_cn = '陈宫曾跟随曹操，后为吕布谋士。不愿投降曹操，被曹操含泪处死。',
		introduction_kr = '진궁은 조조를 따랐으며 이후 여포의 책사가 되었다. 조조에게 항복하지 않아 눈물로 처형당했다.'
	},
	[541] = {
		id = 541,
		name = 'Mã Vân Lục',
		name_vn = 'Mã Vân Lục',
		name_en = 'Ma Yunlu',
		name_th = 'ม้าเหวินลู่',
		name_idn = 'Ma Yunlu',
		name_cn = '马云禄',
		name_kr = '마운록',
		cardMarkID = 541,
		atkType = {7},
		zawakeFragID = 50541,
		chaRecom = {7, 8, 9, 12},
		specValue = {45, 63, 67, 60, 35, 50, 320},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 2,
		unitID = 541,
		innateSkillID = 5413,
		skillList = {5411, 5412, 5413, 5416, 61151, 61152},
		starSkillSeqID = 541,
		fragID = 20541,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 541,
		abilitySeqID = 14,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {5411, 5412, 5413},
		introduction = 'Mã Vân Lộc là con gái Mã Đằng, thê tử Triệu Vân. Tính tình ngây thơ thẳng thắn, am hiểu thương thuật.',
		introduction_vn = 'Mã Vân Lộc là con gái Mã Đằng, thê tử Triệu Vân. Tính tình ngây thơ thẳng thắn, am hiểu thương thuật.',
		introduction_en = 'Ma Yunlu was the daughter of Ma Teng and wife of Zhao Yun. She was innocent and straightforward, with knowledge of martial medicine.',
		introduction_th = 'ม้าอวิ้นหลู่ ลูกสาวม้าเต็ง ภรรยาของเจ้าอวี้ มีนิสัยบริสุทธิ์และซื่อสัตย์ เชี่ยวชาญการแพทย์',
		introduction_idn = 'Ma Yunlu adalah putri Ma Teng, istri Zhao Yun. Sifatnya polos dan terus terang, menguasai seni pengobatan.',
		introduction_cn = '马云禄，马腾之女，赵云妻子。性格纯真直率，精通伤医。',
		introduction_kr = '마운록은 마등의 딸이자 조조의 부인이다. 순수하고 솔직하며 상술에 능하다.',
		location = 'Probably just a lazybum',
		location_vn = 'Probably just a lazybum',
		location_en = 'Probably Just a Lazybones',
		location_kr = '아마도 그냥 게으름뱅이',
		location_th = 'อาจจะเป็นแค่คนขี้เกียจ',
		location_idn = 'Mungkin Hanya Pemalas',
		location_cn = '可能只是个懒汉'
	},
	[551] = {
		id = 551,
		name = 'Tả Từ',
		name_vn = 'Tả Từ',
		name_en = 'Zuo Ci',
		name_th = 'ซ้ายซื่อ',
		name_idn = 'Zuo Ci',
		name_cn = '左慈',
		name_kr = '좌사',
		cardMarkID = 551,
		atkType = {8},
		zawakeID = 551,
		zawakeFragID = 50551,
		gender = 0,
		chaRecom = {6, 7, 18, 19},
		specValue = {30, 85, 45, 55, 70, 55, 340},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 2,
		unitID = 551,
		skinSkillMap = {[116] = {5521, 5522, 5523, 5526, 61151, 61152}, __size = 1},
		innateSkillID = 5513,
		skillList = {5511, 5512, 5513, 5516, 61151, 61152},
		starSkillSeqID = 551,
		fragID = 20551,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 551,
		abilitySeqID = 19,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {5511, 5512, 5513},
		introduction = 'Tả Từ tinh thông kỳ môn độn giáp, có thể sai khiến quỷ thần, sau lên núi luyện linh đơn.',
		introduction_vn = 'Tả Từ tinh thông kỳ môn độn giáp, có thể sai khiến quỷ thần, sau lên núi luyện linh đơn.',
		introduction_en = 'Zuo Ci was skilled in mystic arts and talisman magic, capable of commanding spirits, later retreating to the mountains to cultivate elixirs.',
		introduction_th = 'ซ้ายซื่อเชี่ยวชาญวิชาคาถา สามารถบังคับผีปีศาจ ต่อมาไปฝึกปรมาจารย์บนภูเขา',
		introduction_idn = 'Zuo Ci mahir dalam ilmu sihir dan alkimia, mampu mengendalikan roh dan dewa, kemudian berlatih ramuan di gunung.',
		introduction_cn = '左慈精通奇门遁甲，可驱使鬼神，后隐居山中炼灵丹。',
		introduction_kr = '좌사로, 기문둔갑에 능하며 귀신을 부릴 수 있고, 후에 산으로 가 영단을 수련했다.',
		location = 'Buff support: Dispel negative effect',
		location_vn = 'Buff support: Dispel negative effect',
		location_en = 'Buff Support: Dispel Negative Effects',
		location_kr = '버프 지원: 부정 효과 해제',
		location_th = 'สนับสนุนบัฟ: ขจัดผลเสีย',
		location_idn = 'Dukungan Buff: Menghilangkan Efek Negatif',
		location_cn = '增益辅助：驱散负面效果'
	},
	[571] = {
		id = 571,
		name = 'Quan Vũ',
		name_vn = 'Quan Vũ',
		name_en = 'Guan Yu',
		name_th = 'กวนอู',
		name_idn = 'Guan Yu',
		name_cn = '关羽',
		name_kr = '관우',
		cardMarkID = 571,
		atkType = {7},
		zawakeID = 571,
		zawakeFragID = 50571,
		chaRecom = {7, 8, 9, 12},
		specValue = {70, 105, 110, 80, 55, 80, 500},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 3,
		unitID = 571,
		skinSkillMap = {[112] = {5721, 5722, 5723, 5726, 61101, 61102}, __size = 1},
		innateSkillID = 5713,
		skillList = {5711, 5712, 5713, 5716, 61101, 61102},
		starSkillSeqID = 571,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20571,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 571,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {5711, 5712, 5713, 5714},
		introduction = 'Quan Vũ đứng đầu trong Ngũ Hổ Thượng Tướng. Nổi tiếng trung nghĩa, anh dũng nên sau này được thiên hạ tôn làm Võ Thánh.',
		introduction_vn = 'Quan Vũ đứng đầu trong Ngũ Hổ Thượng Tướng. Nổi tiếng trung nghĩa, anh dũng nên sau này được thiên hạ tôn làm Võ Thánh.',
		introduction_en = 'Guan Yu was the leader of the Five Tiger Generals. Famous for loyalty and bravery, he was later revered as the Martial Saint.',
		introduction_th = 'กวนอูผู้นำห้าขุนพลผู้กล้าหาญ มีชื่อเสียงในความซื่อสัตย์และกล้าหาญ จึงได้รับการยกย่องเป็นเทพเจ้าแห่งสงคราม',
		introduction_idn = 'Guan Yu memimpin lima jenderal harimau. Dikenal karena kesetiaan dan keberanian, kemudian dihormati sebagai Dewa Perang.',
		introduction_cn = '关羽，五虎上将之首。以忠义英勇著称，后被尊为武圣。',
		introduction_kr = '관우는 오호상장 중 으뜸으로 충성과 용맹이 뛰어나 후에 무성으로 추존되었다.',
		location = 'Steel-type fort: Taunts the back line',
		location_vn = 'Steel-type fort: Taunts the back line',
		location_en = 'Steel-type Fort: Taunts Back Line',
		location_kr = '강철 속성 요새: 후열 도발',
		location_th = 'ป้อมประเภทเหล็ก: ล่อเป้าแนวหลัง',
		location_idn = 'Benteng Tipe Baja: Memprovokasi Barisan Belakang',
		location_cn = '钢属性堡垒：嘲讽后排'
	},
	[591] = {
		id = 591,
		name = 'Khương Duy',
		name_vn = 'Khương Duy',
		name_en = 'Jiang Wei',
		name_th = 'ควางวุ่ย',
		name_idn = 'Jiang Wei',
		name_cn = '蒋琬',
		name_kr = '강유',
		cardMarkID = 591,
		atkType = {7},
		zawakeID = 591,
		zawakeFragID = 50591,
		chaRecom = {10, 11, 13, 9},
		specValue = {45, 95, 63, 37, 65, 55, 360},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {612, 613, 614, 602, 603},
		levelExpID = 3,
		unitID = 591,
		innateSkillID = 5913,
		skillList = {5911, 5912, 5913, 5916, 61101, 61102},
		starSkillSeqID = 591,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20591,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 591,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {5911, 5912, 5913, 5914},
		introduction = 'Khương Duy đi theo Gia cát Lượng về làm tướng nhà Thục Hán. Sau khi Khổng Minh qua đời, Khương Duy tiếp tục thống lĩnh đại quân Bắc Phạt tấn công nước Ngụy nhiều lần.',
		introduction_vn = 'Khương Duy đi theo Gia cát Lượng về làm tướng nhà Thục Hán. Sau khi Khổng Minh qua đời, Khương Duy tiếp tục thống lĩnh đại quân Bắc Phạt tấn công nước Ngụy nhiều lần.',
		introduction_en = 'Jiang Wei followed Zhuge Liang to serve as a general of Shu Han. After Kongming’s death, Jiang Wei continued to lead northern campaigns, attacking Wei multiple times.',
		introduction_th = 'ควงดุยติดตามเจียฉาเหลียงเป็นแม่ทัพแห่งรัฐฉู่ฮั่น หลังขงเบ้งเสียชีวิต ควงดุยยังคงนำทัพบุกโจมตีน้ำเต้าหลายครั้ง',
		introduction_idn = 'Jiang Wei mengikuti Zhuge Liang menjadi jenderal Shu Han. Setelah kematian Kongming, Jiang Wei terus memimpin pasukan menyerang Wei utara berkali-kali.',
		introduction_cn = '姜维追随诸葛亮为蜀汉将领。诸葛亮逝世后，姜维继续率军北伐，多次攻打魏国。',
		introduction_kr = '강유는 제갈량을 따라 촉한의 장수가 되었으며, 제갈량 사후 여러 차례 북벌을 계속 지휘하였다.',
		location = 'Savage damage: Charge electricity and increase damage',
		location_vn = 'Savage damage: Charge electricity and increase damage',
		location_en = 'Savage Damage: Charge Electricity and Increase Damage',
		location_kr = '광폭 피해: 전기 충전 후 피해 증가',
		location_th = 'ความเสียหายดุร้าย: ชาร์จไฟฟ้าและเพิ่มความเสียหาย',
		location_idn = 'Kerusakan Buas: Mengisi Listrik dan Meningkatkan Kerusakan',
		location_cn = '狂暴伤害：蓄电提升伤害'
	},
	[601] = {
		id = 601,
		name = 'Chu Du',
		name_vn = 'Chu Du',
		name_en = 'Zhou Yu',
		name_th = 'จูตู้',
		name_idn = 'Zhou Yu',
		name_cn = '周瑜',
		name_kr = '주유',
		cardMarkID = 601,
		atkType = {8},
		zawakeID = 601,
		zawakeFragID = 50601,
		chaRecom = {18, 19, 20, 21},
		specValue = {45, 83, 75, 37, 70, 55, 365},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {618, 619, 620, 602, 603},
		levelExpID = 3,
		unitID = 601,
		innateSkillID = 6013,
		skillList = {6011, 6012, 6013, 6016, 61101, 61102},
		starSkillSeqID = 601,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20601,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 601,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {6011, 6012, 6013, 6014},
		introduction = 'Chu Du là danh tướng và khai quốc công thần của nước Đông Ngô. Chu Du nổi tiếng với chiến thắng ở Trận Xích Bích trước quân Tào Tháo',
		introduction_vn = 'Chu Du là danh tướng và khai quốc công thần của nước Đông Ngô. Chu Du nổi tiếng với chiến thắng ở Trận Xích Bích trước quân Tào Tháo',
		introduction_en = 'Zhou Yu was a famous general and founding hero of Eastern Wu. Celebrated for his victory at the Battle of Red Cliffs against Cao Cao.',
		introduction_th = 'จูตู้ ขุนพลชื่อดังและผู้ร่วมก่อตั้งตงงู มีชื่อเสียงจากชัยชนะในยุทธการฉีผีต่อกองทัพเต้าตง',
		introduction_idn = 'Zhou Yu adalah jenderal terkenal dan tokoh pendiri negara Wu Timur. Zhou Yu terkenal dengan kemenangan di Pertempuran Chibi melawan tentara Cao Cao.',
		introduction_cn = '周瑜，东吴名将和开国功臣。以赤壁之战大破曹操军闻名。',
		introduction_kr = '주유는 동오의 명장 겸 개국공신으로, 적벽대전에서 조조군을 크게 격파한 것으로 유명하다.',
		location = 'Burst damage: Ignite burn',
		location_vn = 'Burst damage: Ignite burn',
		location_en = 'Burst Damage: Ignite Burn',
		location_kr = '폭발 피해: 점화 연소',
		location_th = 'ความเสียหายระเบิด: จุดไฟเผาไหม้',
		location_idn = 'Kerusakan Ledakan: Membakar Api',
		location_cn = '爆发伤害：点燃灼烧'
	},
	[611] = {
		id = 611,
		name = 'Hoa Man',
		name_vn = 'Hoa Man',
		name_en = 'Hua Man',
		name_th = 'ฮว๋าม่าน',
		name_idn = 'Hua Man',
		name_cn = '花蛮',
		name_kr = '화만',
		cardMarkID = 611,
		atkType = {7},
		chaRecom = {24, 25, 11, 12},
		specValue = {65, 85, 125, 100, 55, 70, 500},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 3,
		unitID = 611,
		innateSkillID = 6113,
		skillList = {6111, 6112, 6113, 6116, 61101, 61102},
		starSkillSeqID = 611,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20611,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 611,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {6111, 6112, 6113, 6114},
		introduction = 'Hoa Man, còn được gọi là Hoa Trung Tú, Con gái của Vua Mạnh Hoạch và Chúc Dung.',
		introduction_vn = 'Hoa Man, còn được gọi là Hoa Trung Tú, Con gái của Vua Mạnh Hoạch và Chúc Dung.',
		introduction_en = 'Hua Man, also known as Hua Zhongtu, daughter of King Meng Huo and Zhu Rong.',
		introduction_th = 'ฮว้าม่าน หรือเรียกว่าฮวาชงถู ลูกสาวของเจ้ามั่งเหอและจู้หย่ง',
		introduction_idn = 'Hua Man, juga dikenal sebagai Hua Zhong Su, putri Raja Meng Huo dan Zhu Rong.',
		introduction_cn = '花鬘，又名花仲秀，孟获与祝融之女。',
		introduction_kr = '화만, 또는 화중수라 불리며, 맹획과 축용의 딸이다.'
	},
	[631] = {
		id = 631,
		name = 'Quan Ngân Bình',
		name_vn = 'Quan Ngân Bình',
		name_en = 'Quan Yinping',
		name_th = 'กวนหยินผิง',
		name_idn = 'Guan Yinping',
		name_cn = '关银屏',
		name_kr = '관은평',
		cardMarkID = 631,
		atkType = {7},
		zawakeID = 631,
		zawakeFragID = 50631,
		chaRecom = {24, 25, 11, 12},
		specValue = {95, 81, 125, 79, 60, 100, 540},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 3,
		unitID = 632,
		skinSkillMap = {[127] = {96321, 96322, 96323, 96326, 61101, 61102}, __size = 1},
		innateSkillID = 6323,
		skillList = {6321, 6322, 6323, 6326, 61101, 61102},
		starSkillSeqID = 631,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20631,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 631,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {6311, 6312, 6313, 6314},
		introduction = 'Quan Ngân Bình là con gái Quan Vũ . Dù thân là nữ nhi nhưng 18 tuổi cô đã sớm được Gia Cát Lượng tin dùng, đem theo hỗ trợ phạt thảo Mạnh Hoạch.',
		introduction_vn = 'Quan Ngân Bình là con gái Quan Vũ . Dù thân là nữ nhi nhưng 18 tuổi cô đã sớm được Gia Cát Lượng tin dùng, đem theo hỗ trợ phạt thảo Mạnh Hoạch.',
		introduction_en = 'Quan Yinping was the daughter of Guan Yu. Although a woman, by age 18 she was already trusted by Zhuge Liang and accompanied him to suppress Meng Huo’s rebellion.',
		introduction_th = 'กวนเงินผิง ลูกสาวกวนอู แม้เป็นหญิงแต่ตอนอายุ 18 ปี ได้รับความไว้วางใจจากเจียฉาเหลียง พาไปช่วยปราบปรามมั่งเหอ',
		introduction_idn = 'Guan Yinping adalah putri Guan Yu. Meskipun perempuan, pada usia 18 tahun ia sudah dipercaya oleh Zhuge Liang dan dibawa untuk membantu menumpas Meng Huo.',
		introduction_cn = '关银屏，关羽之女。虽为女子，18岁即获诸葛亮重用，随军讨伐孟获。',
		introduction_kr = '관은빈은 관우의 딸로, 여성임에도 불구하고 18세에 제갈량의 신임을 받아 맹획 정벌에 지원하였다.',
		location = 'Savage damage: Sacrifice sanity',
		location_vn = 'Savage damage: Sacrifice sanity',
		location_en = 'Savage Damage: Sacrifice Sanity',
		location_kr = '광폭 피해: 정신력 희생',
		location_th = 'ความเสียหายดุร้าย: สละสติปัญญา',
		location_idn = 'Kerusakan Buas: Mengorbankan Kewarasan',
		location_cn = '狂暴伤害：牺牲理智'
	},
	[641] = {
		id = 641,
		name = 'Vương Nguyên Cơ',
		name_vn = 'Vương Nguyên Cơ',
		name_en = 'Wang Yuanji',
		name_th = 'หวังหยวนขี',
		name_idn = 'Wang Yuanji',
		name_cn = '王元姬',
		name_kr = '왕원기',
		cardMarkID = 641,
		atkType = {8},
		zawakeID = 641,
		zawakeFragID = 50641,
		chaRecom = {18, 20, 22, 24},
		nValueRnd = 2,
		specValue = {130, 60, 85, 80, 85, 95, 535},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 3,
		unitID = 641,
		innateSkillID = 6413,
		skillList = {6411, 6412, 6413, 6416, 61151, 61152},
		starSkillSeqID = 641,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20641,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 641,
		abilitySeqID = 2,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {6411, 6412, 6413, 6414},
		introduction = 'Vương Nguyên Cơ được biết đến với tài năng, sự thông minh và tầm nhìn sâu rộng. Thể hiện trong việc duy trì quyền lực của gia tộc Tư Mã trong triều đình nhà Ngụy.',
		introduction_vn = 'Vương Nguyên Cơ được biết đến với tài năng, sự thông minh và tầm nhìn sâu rộng. Thể hiện trong việc duy trì quyền lực của gia tộc Tư Mã trong triều đình nhà Ngụy.',
		introduction_en = 'Wang Yuanji was known for her talent, intelligence, and far-reaching vision, demonstrated in maintaining the power of the Sima family in the Wei court.',
		introduction_th = 'หวังหยวนกี มีชื่อเสียงด้านความสามารถ ความเฉลียวฉลาด และวิสัยทัศน์ ก่อร่างสร้างอำนาจให้ตระกูลซื่อหม่าในราชสำนักเต้าหวี่',
		introduction_idn = 'Sima Yuanji dikenal dengan bakat, kecerdasan, dan visi jauh ke depan. Terlihat dari kemampuannya memelihara kekuasaan keluarga Sima dalam pemerintahan Wei.',
		introduction_cn = '王元姬以才智与远见著称，助司马家族巩固魏朝权力。',
		introduction_kr = '왕원기, 탁월한 재능과 지혜, 그리고 깊은 통찰력으로 조위 왕조 내에서 사마 가문의 권력 유지를 도왔다.',
		location = 'HP Store',
		location_vn = 'HP Store',
		location_en = 'HP Store',
		location_kr = 'HP 저장',
		location_th = 'เก็บพลังชีวิต',
		location_idn = 'Penyimpanan HP',
		location_cn = '生命储存'
	},
	[691] = {
		id = 691,
		name = 'Cao Thuận',
		name_vn = 'Cao Thuận',
		name_en = 'Cao Shun',
		name_th = 'เกาเสี้ยน',
		name_idn = 'Cao Shun',
		name_cn = '高顺',
		name_kr = '고순',
		cardMarkID = 691,
		atkType = {7},
		zawakeID = 691,
		zawakeFragID = 50691,
		chaRecom = {15, 16, 11, 12},
		specValue = {30, 55, 80, 90, 55, 45, 355},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {615, 616, 617, 602, 603},
		levelExpID = 2,
		unitID = 691,
		innateSkillID = 6913,
		skillList = {6911, 6912, 6913, 6916, 61151, 61152},
		starSkillSeqID = 691,
		fragID = 20691,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 691,
		abilitySeqID = 16,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {6911, 6912, 6913},
		introduction = 'Cao Thuận là một viên võ tướng phục vụ dưới trướng của Lã Bố . Cao Thuận được biết đến là bộ tướng mạnh nhất trong lực lượng quân của Lã Bố.',
		introduction_vn = 'Cao Thuận là một viên võ tướng phục vụ dưới trướng của Lã Bố . Cao Thuận được biết đến là bộ tướng mạnh nhất trong lực lượng quân của Lã Bố.',
		introduction_en = 'Gao Shun was a martial general serving under Lü Bu. Known as the strongest commander in Lü Bu’s forces.',
		introduction_th = 'เกาเสวียน ขุนพลรับใช้ลู่ปู๋ มีชื่อเสียงเป็นแม่ทัพที่แข็งแกร่งที่สุดในกองทัพลู่ปู๋',
		introduction_idn = 'Gao Shun adalah jenderal militer di bawah komando Lü Bu. Dikenal sebagai panglima terkuat dalam pasukan Lü Bu.',
		introduction_cn = '高顺，吕布麾下武将，号称吕布军中最强。',
		introduction_kr = '고순은 여포 휘하의 무장으로, 여포군 내에서 가장 강력한 부장으로 알려져 있다.',
		location = 'Armor penetration damage',
		location_vn = 'Armor penetration damage',
		location_en = 'Armor Penetration Damage',
		location_kr = '방어력 관통 피해',
		location_th = 'ความเสียหายเจาะเกราะ',
		location_idn = 'Kerusakan Penetrasi Armor',
		location_cn = '破甲伤害'
	},
	[701] = {
		id = 701,
		name = 'Mã Đại',
		name_vn = 'Mã Đại',
		name_en = 'Ma Dai',
		name_th = 'ม้าไต้',
		name_idn = 'Ma Dai',
		name_cn = '马岱',
		name_kr = '마대',
		cardMarkID = 701,
		atkType = {7},
		zawakeFragID = 50701,
		chaRecom = {8, 9, 13, 12},
		specValue = {80, 130, 105, 65, 60, 75, 515},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {615, 616, 617, 602, 603},
		levelExpID = 3,
		unitID = 701,
		innateSkillID = 7013,
		skillList = {7011, 7012, 7013, 7016, 61101, 61102},
		starSkillSeqID = 701,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20701,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 701,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {7011, 7012, 7013, 7014},
		introduction = 'Trước đây thuộc gia tộc hùng cứ vùng Tây Lương. Sau cùng Mã Siêu đầu quân cho Lưu Bị, phục vụ cho nhà Thục. ',
		introduction_vn = 'Trước đây thuộc gia tộc hùng cứ vùng Tây Lương. Sau cùng Mã Siêu đầu quân cho Lưu Bị, phục vụ cho nhà Thục. ',
		introduction_en = 'Originally from a powerful clan ruling Western Liang. Eventually, Ma Chao joined Liu Bei to serve Shu Han.',
		introduction_th = 'เดิมเป็นตระกูลผู้ปกครองแถวไซเหลียง สุดท้ายม้าไซเข้าร่วมกับหลิวเป่ย รับใช้รัฐฉู่',
		introduction_idn = 'Dahulu berasal dari keluarga yang menguasai wilayah Xiliang. Akhirnya Ma Chao bergabung dengan Liu Bei, melayani negara Shu.',
		introduction_cn = '曾属西凉世族，后马超归顺刘备，效力蜀汉。',
		introduction_kr = '원래는 서량 지역의 군벌 가문 소속이었으나, 마초가 유비에게 귀순하여 촉한에 복무하였다.',
		location = 'DMG dealer: Bloodthirst Tearing',
		location_vn = 'DMG dealer: Bloodthirst Tearing',
		location_en = 'Damage Dealer: Bloodthirsty Tear',
		location_kr = '피해 딜러: 탐욕스러운 찢기',
		location_th = 'ผู้สร้างความเสียหาย: ฉีกกระหายเลือด',
		location_idn = 'Penghasil DMG: Robekan Haus Darah',
		location_cn = '输出手：嗜血撕裂'
	},
	[711] = {
		id = 711,
		name = 'Hứa Chử',
		name_vn = 'Hứa Chử',
		name_en = 'Xu Chu',
		name_th = 'เสวียนจู',
		name_idn = 'Xu Chu',
		name_cn = '许褚',
		name_kr = '허처',
		cardMarkID = 711,
		atkType = {7},
		zawakeID = 711,
		zawakeFragID = 50711,
		chaRecom = {24, 25, 11, 12},
		specValue = {135, 5, 85, 40, 40, 85, 390},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {606, 607, 608, 602, 603},
		levelExpID = 3,
		unitID = 711,
		innateSkillID = 7113,
		skillList = {7111, 7112, 7113, 7116, 61151, 61152},
		starSkillSeqID = 711,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20711,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 711,
		abilitySeqID = 2,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {7111, 7112, 7113, 7114},
		introduction = 'Hứa Chử là tướng hầu cận bên Tào Tháo. Nổi tiếng không chỉ với sức khỏe phi thường mà còn tận tụy, hết lòng vì chủ nên được Tào Tháo coi trọng.',
		introduction_vn = 'Hứa Chử là tướng hầu cận bên Tào Tháo. Nổi tiếng không chỉ với sức khỏe phi thường mà còn tận tụy, hết lòng vì chủ nên được Tào Tháo coi trọng.',
		introduction_en = 'Xu Chu was a close general under Cao Cao. Famous not only for his extraordinary strength but also his loyalty and dedication, earning Cao Cao’s great trust.',
		introduction_th = 'เสี่ยจู้ ผู้ติดตามใกล้ชิดเต้าตง มีชื่อเสียงไม่เพียงแต่สุขภาพแข็งแรง แต่ยังทุ่มเทและซื่อสัตย์ จึงได้รับความไว้วางใจจากเต้าตง',
		introduction_idn = 'Xu Chu adalah pengawal dekat Cao Cao. Terkenal tidak hanya karena kekuatan luar biasa tetapi juga setia dan berdedikasi, sehingga sangat dihargai oleh Cao Cao.',
		introduction_cn = '许褚，曹操近侍，因非凡体力与忠诚获曹操重用。',
		introduction_kr = '허저는 조조의 측근 장수로, 비범한 체력과 헌신적인 충성심으로 조조의 신임을 받았다.',
		location = 'Front line damage absorption: HP recovery during sleep',
		location_vn = 'Front line damage absorption: HP recovery during sleep',
		location_en = 'Front Line Damage Absorption: HP Recovery During Sleep',
		location_kr = '전열 피해 흡수: 수면 중 HP 회복',
		location_th = 'ดูดซับความเสียหายแนวหน้า: ฟื้นฟูพลังชีวิตขณะหลับ',
		location_idn = 'Penyerapan Kerusakan Barisan Depan: Pemulihan HP Saat Tidur',
		location_cn = '前排伤害吸收：睡眠中回复生命'
	},
	[721] = {
		id = 721,
		name = 'Lưu Bị',
		name_vn = 'Lưu Bị',
		name_en = 'Liu Bei',
		name_th = 'หลิวเป่ย',
		name_idn = 'Liu Bei',
		name_cn = '刘备',
		name_kr = '유비',
		cardMarkID = 721,
		atkType = {8},
		zawakeID = 721,
		zawakeFragID = 50721,
		gender = 0,
		chaRecom = {24, 23, 14, 16},
		nValueRnd = 2,
		specValue = {90, 85, 85, 100, 95, 125, 580},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {615, 616, 617, 602, 603},
		levelExpID = 3,
		unitID = 721,
		innateSkillID = 7213,
		skillList = {7211, 7212, 7213, 7216, 61101, 61102},
		starSkillSeqID = 721,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20721,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 721,
		abilitySeqID = 3,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {7211, 7212, 7213, 7214},
		introduction = 'Lưu Bị là người nhân hậu, biết trọng dụng người tài. Kết nghĩa đào viên với Trương Phi, Quan Vũ, rồi lập bá nghiệp, khai quốc Thục Hán.',
		introduction_vn = 'Lưu Bị là người nhân hậu, biết trọng dụng người tài. Kết nghĩa đào viên với Trương Phi, Quan Vũ, rồi lập bá nghiệp, khai quốc Thục Hán.',
		introduction_en = 'Liu Bei was benevolent and valued talented people. Swore brotherhood with Zhang Fei and Guan Yu, then founded the Shu Han kingdom.',
		introduction_th = 'หลิวเป่ยเป็นคนใจบุญ รู้จักใช้คน ได้พี่น้องสาบานกับจางเฟยและกวนอู สร้างอาณาจักรและก่อตั้งรัฐฉู่ฮั่น',
		introduction_idn = 'Liu Bei adalah orang yang baik hati dan menghargai bakat. Berteman dengan Zhang Fei dan Guan Yu dalam ikatan persaudaraan, kemudian mendirikan kerajaan Shu Han.',
		introduction_cn = '刘备仁厚重用贤才，与张飞、关羽桃园结义，建立蜀汉基业。',
		introduction_kr = '유비는 인자하며 인재 등용에 능했다. 장비, 관우와 도원결의를 맺고 제왕의 업을 세워 촉한을 개국하였다.',
		location = 'The Ice Legendary Bird & The Core of the Ice-type',
		location_vn = 'The Ice Legendary Bird & The Core of the Ice-type',
		location_en = 'The Ice Legendary Bird & Core of the Ice Type',
		location_kr = '얼음 전설의 새 & 얼음 속성 핵심',
		location_th = 'นกในตำนานน้ำแข็ง & แกนหลักของประเภทน้ำแข็ง',
		location_idn = 'Burung Legendaris Es & Inti Tipe Es',
		location_cn = '冰之神鸟&冰属性核心'
	},
	[731] = {
		id = 731,
		name = 'Mã Siêu',
		name_vn = 'Mã Siêu',
		name_en = 'Ma Chao',
		name_th = 'ม้าเฉียว',
		name_idn = 'Ma Chao',
		name_cn = '马超',
		name_kr = '마초',
		cardMarkID = 731,
		atkType = {8},
		zawakeID = 731,
		zawakeFragID = 50731,
		gender = 0,
		chaRecom = {6, 7, 18, 19},
		nValueRnd = 2,
		specValue = {90, 100, 90, 85, 125, 90, 580},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {612, 613, 614, 602, 603},
		levelExpID = 3,
		unitID = 731,
		innateSkillID = 7313,
		skillList = {7311, 7312, 7313, 7316, 61101, 61102},
		starSkillSeqID = 731,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20731,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 731,
		abilitySeqID = 4,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {7311, 7312, 7313, 7314},
		introduction = 'Mã Siêu, một trong Ngũ Hổ Thượng Tướng của Thục Hán. Vô cùng dũng mãnh, từng nhiều lần đánh bại quân Tào',
		introduction_vn = 'Mã Siêu, một trong Ngũ Hổ Thượng Tướng của Thục Hán. Vô cùng dũng mãnh, từng nhiều lần đánh bại quân Tào',
		introduction_en = 'Ma Chao, one of the Five Tiger Generals of Shu Han. Extremely brave, he defeated Cao Cao’s forces many times.',
		introduction_th = 'ม้าไซ หนึ่งในห้าขุนพลผู้กล้าหาญแห่งฉู่ฮั่น มีความกล้าหาญมาก เคยหลายครั้งเอาชนะกองทัพเต้าตง',
		introduction_idn = 'Ma Chao, salah satu dari Lima Jenderal Harimau Shu Han. Sangat pemberani, sering mengalahkan pasukan Cao.',
		introduction_cn = '马超，蜀汉五虎上将之一，勇猛无比，多次击败曹军。',
		introduction_kr = '마초는 촉한의 오호상장 중 하나로 매우 용맹하며 여러 차례 조조군을 격파하였다.',
		location = 'The Electric Legendary Bird & The Core of the Electric-type',
		location_vn = 'The Electric Legendary Bird & The Core of the Electric-type',
		location_en = 'The Electric Legendary Bird & Core of the Electric Type',
		location_kr = '전기 전설의 새 & 전기 속성 핵심',
		location_th = 'นกในตำนานไฟฟ้า & แกนหลักของประเภทไฟฟ้า',
		location_idn = 'Burung Legendaris Listrik & Inti Tipe Listrik',
		location_cn = '电之神鸟&电属性核心'
	},
	[741] = {
		id = 741,
		name = 'Lữ Mông',
		name_vn = 'Lữ Mông',
		name_en = 'Lu Meng',
		name_th = 'หลู่เมิ่ง',
		name_idn = 'Lu Meng',
		name_cn = '吕蒙',
		name_kr = '여몽',
		cardMarkID = 741,
		atkType = {8},
		branch = 1,
		zawakeID = 741,
		zawakeFragID = 50741,
		gender = 0,
		chaRecom = {18, 19, 20, 21},
		nValueRnd = 2,
		specValue = {90, 90, 100, 90, 125, 85, 580},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {618, 619, 620, 602, 603},
		levelExpID = 3,
		unitID = 741,
		innateSkillID = 7413,
		skillList = {7411, 7412, 7413, 7416, 61101, 61102},
		starSkillSeqID = 741,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20741,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 741,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {7411, 7412, 7413, 7414},
		introduction = 'Lữ Mông là danh tướng cuối thời Đông Hán, chiến tích nổi tiếng nhất là hạ Quan Vũ, lấy Kinh Châu',
		introduction_vn = 'Lữ Mông là danh tướng cuối thời Đông Hán, chiến tích nổi tiếng nhất là hạ Quan Vũ, lấy Kinh Châu',
		introduction_en = 'Lü Meng was a renowned general at the end of the Eastern Han; his most famous achievement was defeating Guan Yu and capturing Jing Province.',
		introduction_th = 'ลู่หมง ขุนพลชื่อดังปลายยุคตงฮั่น มีผลงานที่โดดเด่นคือการปราบกวนอูและยึดครองจิงโจว',
		introduction_idn = 'Lu Meng adalah jenderal terkenal pada akhir Dinasti Han Timur, prestasi paling terkenal adalah mengalahkan Guan Yu dan merebut Jingzhou.',
		introduction_cn = '吕蒙，东汉末名将，最著名战功为擒关羽，夺取荆州。',
		introduction_kr = '여몽은 동한 말기의 명장으로, 가장 유명한 전과는 관우를 격파하고 경주를 점령한 것이다.',
		location = 'The Fire Legendary Bird & The Core of the Fire-type',
		location_vn = 'The Fire Legendary Bird & The Core of the Fire-type',
		location_en = 'The Fire Legendary Bird & Core of the Fire Type',
		location_kr = '불 전설의 새 & 불 속성 핵심',
		location_th = 'นกในตำนานไฟ & แกนหลักของประเภทไฟ',
		location_idn = 'Burung Legendaris Api & Inti Tipe Api',
		location_cn = '火之神鸟&火属性核心'
	},
	[761] = {
		id = 761,
		name = 'Đan Linh Tôn Thượng Hương',
		name_vn = 'Đan Linh Tôn Thượng Hương',
		name_en = 'Dan Ling Sun Shangxiang',
		name_th = 'ตันหลิงซุนเฉิงเสียง',
		name_idn = 'Danling Zunshangxiang',
		name_cn = '丹灵孙尚香',
		name_kr = '단령손상향',
		cardMarkID = 761,
		atkType = {8},
		zawakeID = 761,
		zawakeFragID = 50761,
		gender = 0,
		chaRecom = {6, 7, 18, 19},
		nValueRnd = 3,
		specValue = {106, 130, 154, 90, 110, 90, 680},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {606, 607, 608, 604, 605},
		levelExpID = 4,
		unitID = 761,
		skinSkillMap = {[123] = {7621, 7622, 7623, 7626, 61101, 61102}, __size = 1},
		innateSkillID = 7613,
		skillList = {7611, 7612, 7613, 7616, 61101, 61102},
		starSkillSeqID = 761,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 20761,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 761,
		abilitySeqID = 6,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {7611, 7612, 7613, 7614},
		introduction = 'Tôn Thượng Hương có hôn nhân được sắp đặt với Lưu Bị nhằm củng cố liên minh giữa Đông Ngô và Thục Hán để chống lại Tào Ngụy, một trong ba thế lực chính của Tam Quốc.',
		introduction_vn = 'Tôn Thượng Hương có hôn nhân được sắp đặt với Lưu Bị nhằm củng cố liên minh giữa Đông Ngô và Thục Hán để chống lại Tào Ngụy, một trong ba thế lực chính của Tam Quốc.',
		introduction_en = 'Sun Shangxiang’s marriage was arranged with Liu Bei to strengthen the alliance between Eastern Wu and Shu Han against Cao Wei, one of the three main powers of the Three Kingdoms.',
		introduction_th = 'ซุ่นส่วงเสียง มีการแต่งงานกับหลิวเป่ยเพื่อเสริมสร้างพันธมิตรตงงู-ฉู่ฮั่น ต่อต้านเต้าหวี่ หนึ่งในสามอำนาจหลักยุคสามก๊ก',
		introduction_idn = 'Sun Shangxiang memiliki pernikahan yang diatur dengan Liu Bei untuk memperkuat aliansi antara Wu Timur dan Shu Han melawan Cao Wei, salah satu dari tiga kekuatan utama Tiga Kerajaan.',
		introduction_cn = '孙尚香为巩固东吴与蜀汉联盟，与刘备有婚约，共抗曹魏三国势力。',
		introduction_kr = '손상향은 동오와 촉한 간의 동맹을 강화하기 위해 유비와 혼인을 약속받은 인물로, 삼국시대의 주요 세력 중 하나인 조위를 견제하였다.',
		location = 'Live for the battle & Overpowering psychic abilities',
		location_vn = 'Live for the battle & Overpowering psychic abilities',
		location_en = 'Live for Battle & Overpowering Psychic Powers',
		location_kr = '전투를 위한 삶 & 강력한 심리 능력',
		location_th = 'มีชีวิตเพื่อการต่อสู้ & พลังจิตที่เหนือชั้น',
		location_idn = 'Hidup untuk Pertempuran & Kekuatan Psikis yang Dominan',
		location_cn = '为战而生&强大心灵能力'
	},
	[771] = {
		id = 771,
		name = 'Tuân Du',
		name_vn = 'Tuân Du',
		name_en = 'Xun You',
		name_th = 'ต้วนหยู่',
		name_idn = 'Xun You',
		name_cn = '荀攸',
		name_kr = '손유',
		cardMarkID = 771,
		atkType = {8},
		zawakeID = 771,
		zawakeFragID = 50771,
		gender = 0,
		chaRecom = {6, 7, 18, 19},
		nValueRnd = 2,
		specValue = {100, 100, 100, 100, 100, 100, 600},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {606, 607, 608, 602, 603},
		levelExpID = 3,
		unitID = 771,
		innateSkillID = 7713,
		skillList = {7711, 7712, 7713, 7716, 61101, 61102},
		starSkillSeqID = 771,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20771,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 771,
		abilitySeqID = 3,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {7711, 7712, 7713, 7714},
		introduction = 'Tuân Du nổi tiếng với tài mưu lược và hủy diệt thành Hạ Phi để bắt sống Lữ Bố.',
		introduction_vn = 'Tuân Du nổi tiếng với tài mưu lược và hủy diệt thành Hạ Phi để bắt sống Lữ Bố.',
		introduction_en = 'Xun You was famous for his strategy and destroyed Xia Pi city to capture Lü Bu alive.',
		introduction_th = 'ฉวนอวี่มีชื่อเสียงในด้านกลยุทธ์ ทำลายเมืองฮ่าผีเพื่อจับลู่ปู๋',
		introduction_idn = 'Xun You terkenal dengan kecerdasan dan strategi, menghancurkan kota Xiapi untuk menangkap Lü Bu hidup-hidup.',
		introduction_cn = '荀彧以谋略著称，毁掉夏侯惇城池，俘获吕布。',
		introduction_kr = '손유는 뛰어난 책략가로 하비성을 파괴하여 여포를 생포하였다.',
		location = 'Deal AOE status condition',
		location_vn = 'Deal AOE status condition',
		location_en = 'Apply AOE Status Effects',
		location_kr = '광역 상태 이상 부여',
		location_th = 'สร้างสถานะแบบวงกว้าง',
		location_idn = 'Memberikan Kondisi Status AOE',
		location_cn = '造成范围状态异常'
	},
	[781] = {
		id = 781,
		name = 'Chân Cơ',
		name_vn = 'Chân Cơ',
		name_en = 'Zhen Ji',
		name_th = 'เจิ้นจือ',
		name_idn = 'Zhen Ji',
		name_cn = '陈宫',
		name_kr = '진기',
		cardMarkID = 781,
		atkType = {8},
		zawakeID = 781,
		zawakeFragID = 50781,
		chaRecom = {24, 23, 14, 16},
		specValue = {45, 45, 49, 65, 49, 65, 318},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 2,
		unitID = 781,
		skinSkillMap = {[101] = {997811, 997812, 997813, 997816, 61151, 61152}, __size = 1},
		innateSkillID = 7813,
		skillList = {7811, 7812, 7813, 7816, 61151, 61152},
		starSkillSeqID = 781,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20781,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 781,
		abilitySeqID = 20,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {7811, 7812, 7813},
		introduction = 'Chân Cơ là phu nhân của Tào Phi. Tương truyền rằng, trước khi trở thành vợ của Tào Phi, Chân Cơ đã từng yêu Tào Thực. ',
		introduction_vn = 'Chân Cơ là phu nhân của Tào Phi. Tương truyền rằng, trước khi trở thành vợ của Tào Phi, Chân Cơ đã từng yêu Tào Thực. ',
		introduction_en = 'Lady Zhen was the wife of Cao Pi. It is said that before becoming Cao Pi’s wife, she was once in love with Cao Zhi.',
		introduction_th = 'จั้นกี เป็นภรรยาของเต้าฝี่ เล่าลือว่า ก่อนเป็นภรรยาเต้าฝี่ เคยรักเต้สือ',
		introduction_idn = 'Zhen Ji adalah istri Cao Pi. Konon sebelum menjadi istri Cao Pi, Zhen Ji pernah mencintai Cao Zhi.',
		introduction_cn = '陈宫为曹丕夫人。传言其入曹丕前曾爱慕曹植。',
		introduction_kr = '진기(부인)는 조비의 부인으로, 전해지는 이야기에 따르면 조비의 아우 조식과도 사랑에 빠졌다고 한다.',
		location = 'AOE healing',
		location_vn = 'AOE healing',
		location_en = 'AOE Healing',
		location_kr = '광역 치유',
		location_th = 'รักษาแบบวงกว้าง',
		location_idn = 'Penyembuhan AOE',
		location_cn = '范围治疗'
	},
	[791] = {
		id = 791,
		name = 'Thái Sử Từ',
		name_vn = 'Thái Sử Từ',
		name_en = 'Tai Shi Ci',
		name_th = 'ไท่สือจื่อ',
		name_idn = 'Tai Shi Ci',
		name_cn = '太史慈',
		name_kr = '태사자',
		cardMarkID = 791,
		atkType = {8},
		zawakeID = 791,
		zawakeFragID = 50791,
		chaRecom = {6, 7, 18, 19},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {618, 619, 620, 602, 603},
		levelExpID = 2,
		unitID = 791,
		innateSkillID = 7913,
		skillList = {7911, 7912, 7913, 7916, 61101, 61102},
		starSkillSeqID = 791,
		fragID = 20791,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 791,
		abilitySeqID = 16,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {7911, 7912, 7913},
		introduction = 'Thái Sử Từ vốn là thuộc hạ của Lưu Dao sau giúp Tôn Sách càn quét Giang Đông. Giỏi cưỡi ngựa bắn tên.',
		introduction_vn = 'Thái Sử Từ vốn là thuộc hạ của Lưu Dao sau giúp Tôn Sách càn quét Giang Đông. Giỏi cưỡi ngựa bắn tên.',
		introduction_en = 'Tai Shi Ci was originally a subordinate of Liu Yao before helping Sun Ce sweep through Jiangdong. Skilled in horseback archery.',
		introduction_th = 'ไท่สือซื่อเดิมเป็นผู้ใต้บังคับบัญชาของหลิวเต่า ต่อมาช่วยซุ่นซาโจมตีเจียงตง ชำนาญการขี่ม้าและยิงธนู',
		introduction_idn = 'Tai Si Ci awalnya adalah bawahan Liu Yao, kemudian membantu Sun Ce menaklukkan Jiangdong. Mahir menunggang kuda dan memanah.',
		introduction_cn = '太史慈原为刘焉部下，后助孙策征讨江东。擅长骑射。',
		introduction_kr = '태사자 원래 유요의 부하였으나 손책을 도와 강동을 평정했다. 말타기와 활쏘기에 능했다.',
		location = 'Damage based on appearance',
		location_vn = 'Damage based on appearance',
		location_en = 'Damage Based on Appearance',
		location_kr = '외모 기반 피해',
		location_th = 'ความเสียหายขึ้นอยู่กับรูปลักษณ์',
		location_idn = 'Kerusakan Berdasarkan Penampilan',
		location_cn = '基于外观的伤害'
	},
	[801] = {
		id = 801,
		name = 'Từ Thứ',
		name_vn = 'Từ Thứ',
		name_en = 'Ci Xu',
		name_th = 'จื่อซื่อ',
		name_idn = 'Ci Xu',
		name_cn = '司徒',
		name_kr = '자서',
		cardMarkID = 801,
		atkType = {7},
		zawakeID = 801,
		zawakeFragID = 50801,
		chaRecom = {11, 12, 15, 16},
		specValue = {50, 43, 65, 64, 44, 48, 314},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 2,
		unitID = 801,
		innateSkillID = 8013,
		skillList = {8011, 8012, 8013, 8016, 61101, 61102},
		starSkillSeqID = 801,
		fragID = 20801,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 801,
		abilitySeqID = 16,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {8011, 8012, 8013},
		introduction = 'Từ Thứ theo phò tá làm mưu sĩ cho Lưu Bị. Sau này trước khi rời đi đã tiến cử Gia Cát Lượng.',
		introduction_vn = 'Từ Thứ theo phò tá làm mưu sĩ cho Lưu Bị. Sau này trước khi rời đi đã tiến cử Gia Cát Lượng.',
		introduction_en = 'Xu Shu served as a strategist for Liu Bei. Before leaving, he recommended Zhuge Liang.',
		introduction_th = 'ซื่อชู่ติดตามช่วยเป็นที่ปรึกษาหลิวเป่ย ก่อนจากไปแนะนำเจียฉาเหลียง',
		introduction_idn = 'Xu Shu menjadi penasihat Liu Bei. Sebelum pergi, ia merekomendasikan Zhuge Liang.',
		introduction_cn = '徐庶辅佐刘备为谋士，离去前推荐诸葛亮。',
		introduction_kr = '자서는 유비의 책사로 봉사하였다. 떠나기 전 제갈량을 추천하였다.',
		location = 'Savage damage',
		location_vn = 'Savage damage',
		location_en = 'Savage Damage',
		location_kr = '광폭 피해',
		location_th = 'ความเสียหายดุร้าย',
		location_idn = 'Kerusakan Buas',
		location_cn = '狂暴伤害'
	},
	[861] = {
		id = 861,
		name = 'Hoàng Nguyệt Anh',
		name_vn = 'Hoàng Nguyệt Anh',
		name_en = 'Huang Yueying',
		name_th = 'หวงเหว่ยอิง',
		name_idn = 'Huang Yueying',
		name_cn = '黄月英',
		name_kr = '황월영',
		cardMarkID = 861,
		atkType = {8},
		zawakeID = 861,
		zawakeFragID = 50861,
		chaRecom = {24, 23, 14, 16},
		specValue = {35, 20, 20, 65, 40, 65, 245},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {618, 619, 620, 602, 603},
		levelExpID = 3,
		unitID = 861,
		skinSkillMap = {[102] = {8611, 8612, 8613, 8616, 61101, 61102}, __size = 1},
		innateSkillID = 8613,
		skillList = {8621, 8622, 8623, 8626, 61101, 61102},
		starSkillSeqID = 861,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 20861,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 861,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {8611, 8612, 8613, 8614},
		introduction = 'Nguyệt Anh là vợ của Gia Cát Lượng. Tương truyền bà có trí tuệ hơn người, cùng chồng phát minh ra Mộc Ngưu Lưu Mã.',
		introduction_vn = 'Nguyệt Anh là vợ của Gia Cát Lượng. Tương truyền bà có trí tuệ hơn người, cùng chồng phát minh ra Mộc Ngưu Lưu Mã.',
		introduction_en = 'Yueying was the wife of Zhuge Liang. Legend says she was exceptionally intelligent and co-invented the wooden ox and flowing horse with her husband.',
		introduction_th = 'หงวี่ยิ่ง ภรรยาของเจียฉาเหลียง เล่ากันว่ามีปัญญาเหนือคนทั่วไป ร่วมกับสามีคิดค้นม้าลากไม้',
		introduction_idn = 'Yue Ying adalah istri Zhuge Liang. Konon ia sangat cerdas, bersama suaminya menciptakan kereta kayu dan kuda.',
		introduction_cn = '月英，诸葛亮妻子，据传才智过人，与夫共创木牛流马。',
		introduction_kr = '월영은 제갈량의 부인으로, 전해지길 그녀는 비상한 지혜를 지니고 남편과 함께 목우유마를 발명하였다.',
		location = 'Luck Assist',
		location_vn = 'Luck Assist',
		location_en = 'Luck Assist',
		location_kr = '행운 지원',
		location_th = 'ช่วยเหลือโชคดี',
		location_idn = 'Bantuan Keberuntungan',
		location_cn = '幸运辅助'
	},
	[891] = {
		id = 891,
		name = 'Lục Tốn',
		name_vn = 'Lục Tốn',
		name_en = 'Lu Xun',
		name_th = 'หลู่ซุน',
		name_idn = 'Lu Xun',
		name_cn = '陆逊',
		name_kr = '육손',
		cardMarkID = 891,
		atkType = {8},
		zawakeFragID = 50891,
		chaRecom = {24, 23, 14, 16},
		specValue = {50, 20, 20, 40, 20, 40, 190},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {606, 607, 608, 602, 603},
		levelExpID = 2,
		unitID = 891,
		innateSkillID = 8913,
		skillList = {8911, 8912, 8913, 8916, 61151, 61152},
		starSkillSeqID = 891,
		fragID = 20891,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 891,
		abilitySeqID = 11,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {8911, 8912, 8913},
		introduction = 'Lục Tốn từng giữ chức Đại Đô Đốc Đông Ngô. Nổi tiếng sau trận Di Lăng tiêu diệt vạn đại quân Thục của Lưu Bị',
		introduction_vn = 'Lục Tốn từng giữ chức Đại Đô Đốc Đông Ngô. Nổi tiếng sau trận Di Lăng tiêu diệt vạn đại quân Thục của Lưu Bị',
		introduction_en = 'Lu Xun once held the position of Grand Commander of Eastern Wu. Famous for his victory at the Battle of Yiling, where he destroyed Liu Bei’s massive Shu army.',
		introduction_th = 'หลู่ซุ่น เคยดำรงตำแหน่งแม่ทัพใหญ่ตงงู มีชื่อเสียงจากยุทธการดิหลิงทำลายกองทัพหลิวเป่ย',
		introduction_idn = 'Lu Xun pernah menjabat sebagai Laksamana Besar Wu Timur. Terkenal setelah Pertempuran Yiling mengalahkan pasukan besar Shu Liu Bei.',
		introduction_cn = '陆逊，东吴大都督。以夷陵之战大破刘备百万大军闻名。',
		introduction_kr = '육손은 동오의 대도독을 역임하였으며, 이릉 전투에서 유비의 촉한 만대군을 격파한 것으로 유명하다.',
		location = 'Brave Dragon Slayer',
		location_vn = 'Brave Dragon Slayer',
		location_en = 'Brave Dragon Slayer',
		location_kr = '용감한 용 사냥꾼',
		location_th = 'นักล่ามังกรกล้าหาญ',
		location_idn = 'Pembunuh Naga Berani',
		location_cn = '勇敢的屠龙者'
	},
	[1031] = {
		id = 1031,
		name = 'Chúc Dung',
		name_vn = 'Chúc Dung',
		name_en = 'Zhu Rong',
		name_th = 'จูหย่ง',
		name_idn = 'Zhu Rong',
		name_cn = '祝融',
		name_kr = '축융',
		cardMarkID = 1031,
		atkType = {7},
		zawakeID = 1031,
		zawakeFragID = 51031,
		chaRecom = {6, 8, 14, 16},
		specValue = {65, 85, 75, 105, 35, 65, 430},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 3,
		unitID = 1031,
		innateSkillID = 10313,
		skillList = {10311, 10312, 10313, 10316, 61151, 61152},
		starSkillSeqID = 1031,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21031,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1031,
		abilitySeqID = 2,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {10311, 10312, 10313, 10314},
		introduction = 'Chúc Dung, vợ của Mạnh Hoạch. Giỏi dùng phi đao, võ nghệ cao cường, từng cùng chồng dẫn quân chinh chiến. ',
		introduction_vn = 'Chúc Dung, vợ của Mạnh Hoạch. Giỏi dùng phi đao, võ nghệ cao cường, từng cùng chồng dẫn quân chinh chiến. ',
		introduction_en = 'Zhu Rong, wife of Meng Huo. Skilled with throwing knives, highly skilled in martial arts, she fought alongside her husband in battles.',
		introduction_th = 'จู้หย่ง ภรรยาของมั่งเหอ เชี่ยวชาญการใช้ดาบบิน มีฝีมือสูง เคยร่วมรบกับสามี',
		introduction_idn = 'Zhu Rong, istri Meng Huo. Ahli menggunakan pisau terbang, memiliki keterampilan bela diri yang hebat, pernah memimpin pasukan bersama suaminya.',
		introduction_cn = '祝融，孟获妻子，擅长飞刀，武艺高强，曾随夫出征。',
		introduction_kr = '축용은 맹획의 부인으로 비도 사용에 능하며 뛰어난 무술 실력으로 남편과 함께 전장을 누볐다.',
		location = 'Nerf Interfere',
		location_vn = 'Nerf Interfere',
		location_en = 'Nerf Interference',
		location_kr = '너프 방해',
		location_th = 'ลดความรุนแรงการรบกวน',
		location_idn = 'Nerf Gangguan',
		location_cn = '削弱干扰'
	},
	[1071] = {
		id = 1071,
		name = 'Công Tôn Toản',
		name_vn = 'Công Tôn Toản',
		name_en = 'Gongsun Zan',
		name_th = 'กงซุนถ้วน',
		name_idn = 'Gong Sun Quan',
		name_cn = '公孙瓒',
		name_kr = '공손찬',
		cardMarkID = 1071,
		atkType = {7},
		zawakeID = 1071,
		zawakeFragID = 51071,
		chaRecom = {24, 25, 11, 12},
		specValue = {80, 85, 125, 75, 40, 95, 500},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 3,
		unitID = 1071,
		innateSkillID = 10713,
		skillList = {10711, 10712, 10713, 10716, 61101, 61102},
		starSkillSeqID = 1071,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21071,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1071,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {10711, 10712, 10713, 10714},
		introduction = 'Công Tôn Toản là tướng nhà Hán, Thái thú Bắc Bì. Lập nhiều chiến công khi thảo phạt dị tộc phương bắc, được gọi là Bạch Mã Tướng.',
		introduction_vn = 'Công Tôn Toản là tướng nhà Hán, Thái thú Bắc Bì. Lập nhiều chiến công khi thảo phạt dị tộc phương bắc, được gọi là Bạch Mã Tướng.',
		introduction_en = 'Gong Sun Guan was a general of the Han, Administrator of Beipi. Achieved many feats suppressing northern tribes, earning the nickname White Horse General.',
		introduction_th = 'กงซุ่นต้วน ขุนพลราชวงศ์ฮั่น เป็นเจ้าเมืองเป่ยปี สร้างผลงานมากมายในการปราบชนเผ่าทางเหนือ ได้รับสมญาว่าขุนพลม้าขาว',
		introduction_idn = 'Gong Sun Zuan adalah jenderal Han, penguasa utara Bei Bi. Mencapai banyak prestasi saat menyerang suku-suku di utara, dikenal sebagai Jenderal Kuda Putih.',
		introduction_cn = '公孙瓒，汉末将领，北郡太守。多次征讨北方异族，号称白马将军。',
		introduction_kr = '공손탄은 한나라의 장수로 북비 태수였다. 북방 이민족을 토벌하며 많은 전공을 세워 백마장군이라 불렸다.',
		location = 'Damage based on appearance: Multiple attacks',
		location_vn = 'Damage based on appearance: Multiple attacks',
		location_en = 'Appearance-based Damage: Multiple Hits',
		location_kr = '외모 기반 피해: 다중 공격',
		location_th = 'ความเสียหายขึ้นอยู่กับรูปลักษณ์: โจมตีหลายครั้ง',
		location_idn = 'Kerusakan Berdasarkan Penampilan: Serangan Berganda',
		location_cn = '基于外观的伤害：多重攻击'
	},
	[1081] = {
		id = 1081,
		name = 'Vương Dị',
		name_vn = 'Vương Dị',
		name_en = 'Wang Yi',
		name_th = 'หวังอี๋',
		name_idn = 'Wang Yi',
		name_cn = '王异',
		name_kr = '왕의',
		cardMarkID = 1081,
		atkType = {7},
		zawakeID = 1081,
		zawakeFragID = 51081,
		chaRecom = {8, 9, 13, 12},
		specValue = {55, 115, 35, 55, 95, 75, 430},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {606, 607, 608, 602, 603},
		levelExpID = 2,
		unitID = 1081,
		innateSkillID = 10813,
		skillList = {10811, 10812, 10813, 10816, 61101, 61102},
		starSkillSeqID = 1081,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21081,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1081,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {10811, 10812, 10813, 10814},
		introduction = 'Khi Mã Siêu tấn công Dực Thành, Vương Dị đích thân khoác chiến bào giúp chồng thủ thành và ban thưởng cho quân sĩ bằng trang sức của mình.',
		introduction_vn = 'Khi Mã Siêu tấn công Dực Thành, Vương Dị đích thân khoác chiến bào giúp chồng thủ thành và ban thưởng cho quân sĩ bằng trang sức của mình.',
		introduction_en = 'When Ma Chao attacked Yucheng, Wang Yi donned his battle robe to help his husband defend the city and rewarded soldiers with his own jewelry.',
		introduction_th = 'เมื่อตอนม้าไซโจมตีอวี๋เฉิง หวังอี้สวมชุดเกราะรบช่วยสามีป้องกันเมือง และมอบเครื่องประดับให้กับทหาร',
		introduction_idn = 'Saat Ma Chao menyerang Yucheng, Wang Yi secara pribadi mengenakan baju perang membantu suaminya bertahan dan memberi penghargaan pada tentara dengan perhiasannya.',
		introduction_cn = '马超攻奕城时，王异亲自披挂战袍助夫守城，并用饰品奖励士兵。',
		introduction_kr = '마초가 익성을 공격할 때 왕이 직접 갑옷을 입고 남편을 도와 성을 지켰으며, 자신의 장신구로 군사들을 포상하였다.',
		location = 'Savage damage: Increases freezing damage',
		location_vn = 'Savage damage: Increases freezing damage',
		location_en = 'Savage Damage: Increased Freezing Damage',
		location_kr = '광폭 피해: 빙결 피해 증가',
		location_th = 'ความเสียหายดุร้าย: เพิ่มความเสียหายจากการแช่แข็ง',
		location_idn = 'Kerusakan Buas: Meningkatkan Kerusakan Pembekuan',
		location_cn = '狂暴伤害：提升冰冻伤害'
	},
	[1171] = {
		id = 1171,
		name = 'Thủy Kính',
		name_vn = 'Thủy Kính',
		name_en = 'Shui Jing',
		name_th = 'ซุ่ยจิง',
		name_idn = 'Shui Jing',
		name_cn = '水镜',
		name_kr = '수경',
		cardMarkID = 1171,
		atkType = {8},
		zawakeID = 1171,
		zawakeFragID = 51171,
		chaRecom = {6, 7, 18, 19},
		specValue = {45, 65, 60, 30, 80, 50, 330},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {606, 607, 608, 602, 603},
		levelExpID = 3,
		unitID = 1171,
		skinSkillMap = {[107] = {11711, 11712, 11713, 11716, 61151, 61152}, __size = 1},
		innateSkillID = 11713,
		skillList = {11721, 11722, 11723, 11726, 61151, 61152},
		starSkillSeqID = 1171,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21171,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1171,
		abilitySeqID = 4,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {11711, 11712, 11713, 11714},
		introduction = 'Thủy Kính tinh thông đạo học, binh pháp. Học cao hiểu rộng, tài cán hơn người, được người đời xem trọng.',
		introduction_vn = 'Thủy Kính tinh thông đạo học, binh pháp. Học cao hiểu rộng, tài cán hơn người, được người đời xem trọng.',
		introduction_en = 'Shui Jing was proficient in Taoist studies and military strategy. Highly learned and talented, he was respected by his contemporaries.',
		introduction_th = 'สุ่ยจิงชิง เชี่ยวชาญวิชาปรัชญาและยุทธศาสตร์ มีความรู้กว้างไกลและได้รับการเคารพจากคนยุคหลัง',
		introduction_idn = 'Shui Jing menguasai Taoisme dan strategi militer. Berpendidikan tinggi dan berbakat, dihormati oleh orang-orang sezamannya.',
		introduction_cn = '水镜先生，精通道学与兵法，博学多才，备受世人尊重。',
		introduction_kr = '수경은 도학과 병법에 정통하며, 학문이 높고 재능이 뛰어나 세인에게 존경받았다.',
		location = 'Buff support: Increase AOE attack',
		location_vn = 'Buff support: Increase AOE attack',
		location_en = 'Buff Support: Increase AOE Attack',
		location_kr = '버프 지원: 광역 공격 증가',
		location_th = 'สนับสนุนบัฟ: เพิ่มการโจมตีวงกว้าง',
		location_idn = 'Dukungan Buff: Meningkatkan Serangan AOE',
		location_cn = '增益辅助：提升范围攻击'
	},
	[1231] = {
		id = 1231,
		name = 'Tưởng Khâm',
		name_vn = 'Tưởng Khâm',
		name_en = 'Jiang Qin',
		name_th = 'เซียงซิน',
		name_idn = 'Jiang Qin',
		name_cn = '蒋钦',
		name_kr = '장금',
		cardMarkID = 1231,
		atkType = {7},
		zawakeID = 1231,
		zawakeFragID = 51231,
		gender = 0,
		chaRecom = {7, 8, 9, 12},
		nValueRnd = 2,
		specValue = {115, 100, 115, 85, 90, 75, 580},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {618, 619, 620, 602, 603},
		levelExpID = 3,
		unitID = 1231,
		skinSkillMap = {[129] = {12321, 12322, 12323, 12326, 61151, 61152}, __size = 1},
		innateSkillID = 12313,
		skillList = {12311, 12312, 12313, 12316, 61151, 61152},
		starSkillSeqID = 1231,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21231,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1231,
		abilitySeqID = 2,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {12311, 12312, 12313, 12314},
		introduction = 'Tưởng Khâm vốn là tặc phỉ, sau theo hàng Tôn Sách. Từng cùng Chu Thái, Hàn Đương thống lĩnh thủy quân đại phá quân địch ở Tam Giang Khẩu.',
		introduction_vn = 'Tưởng Khâm vốn là tặc phỉ, sau theo hàng Tôn Sách. Từng cùng Chu Thái, Hàn Đương thống lĩnh thủy quân đại phá quân địch ở Tam Giang Khẩu.',
		introduction_en = 'Jiang Qin was originally a bandit, later serving Sun Ce. Along with Chu Tai and Han Dang, he led the navy to decisively defeat enemy forces at Tam Giang Pass.',
		introduction_th = 'เจียงฉิน เดิมเป็นโจร สวามิภักดิ์ซุ่นซา เคยร่วมกับจูไทและหานต่องนำกองทัพเรือทำลายศัตรูที่ตำบลสามแม่น้ำ',
		introduction_idn = 'Jiang Qin awalnya adalah bandit, kemudian bergabung dengan Sun Ce. Pernah memimpin angkatan laut bersama Chu Tai dan Han Dang menghancurkan musuh di Tam Jiang Kou.',
		introduction_cn = '蒋琬原为盗贼，后归顺孙策，与周泰、韩当统领水军大破敌军于三江口。',
		introduction_kr = '장금은 도적 출신으로 손책을 따라 복무하였다. 주태, 한당과 함께 수군을 이끌어 삼강구 전투에서 적군을 대파하였다.',
		location = 'The Fire Messenger & The Fire-type in the Front Line',
		location_vn = 'The Fire Messenger & The Fire-type in the Front Line',
		location_en = 'The Fire Messenger & Fire Type Frontline',
		location_kr = '불 속성 전령 & 전열 불 속성',
		location_th = 'ผู้ส่งสารไฟ & ประเภทไฟในแนวหน้า',
		location_idn = 'Utusan Api & Tipe Api di Barisan Depan',
		location_cn = '火之使者&前线火属性'
	},
	[1241] = {
		id = 1241,
		name = 'Đại Kiều',
		name_vn = 'Đại Kiều',
		name_en = 'Da Qiao',
		name_th = 'ต้าเฉียว',
		name_idn = 'Da Qiao',
		name_cn = '大乔',
		name_kr = '대교',
		cardMarkID = 1241,
		atkType = {8},
		zawakeID = 1241,
		zawakeFragID = 51241,
		gender = 0,
		chaRecom = {24, 23, 14, 16},
		nValueRnd = 2,
		specValue = {100, 85, 75, 115, 90, 115, 580},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 3,
		unitID = 1241,
		skinSkillMap = {[106] = {12421, 12422, 12423, 12426, 61151, 61152}, __size = 1},
		innateSkillID = 12413,
		skillList = {12411, 12412, 12413, 12416, 61151, 61152},
		starSkillSeqID = 1241,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21241,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1241,
		abilitySeqID = 4,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {12411, 12412, 12413, 12414},
		introduction = 'Đại Kiều là vợ của Tôn Sách, một trong những mỹ nhân thời kỳ Tam Quốc. Cùng em gái Tiểu Kiều được thiên hạ ca tụng là Giang Đông Nhị Kiều.',
		introduction_vn = 'Đại Kiều là vợ của Tôn Sách, một trong những mỹ nhân thời kỳ Tam Quốc. Cùng em gái Tiểu Kiều được thiên hạ ca tụng là Giang Đông Nhị Kiều.',
		introduction_en = 'Da Qiao was the wife of Sun Ce, one of the beauties of the Three Kingdoms. Along with her sister Xiao Qiao, they were praised as the Two Qiaos of Jiangdong.',
		introduction_th = 'ต้าเฉียว ภรรยาของซุ่นซา หนึ่งในสาวงามยุคสามก๊ก กับน้องสาวเสี่ยวเฉียวได้รับการยกย่องเป็นสองสาวงามแห่งเจียงตง',
		introduction_idn = 'Da Qiao adalah istri Sun Ce, salah satu wanita cantik pada zaman Tiga Kerajaan. Bersama adiknya Xiao Qiao, mereka dikenal sebagai Dua Qiao Jiangdong.',
		introduction_cn = '大乔，孙策妻，三国时期美女，与妹妹小乔合称江东二乔。',
		introduction_kr = '대교는 손책의 부인이자 삼국시대의 미인 중 하나이다. 여동생 소교와 함께 강동의 이교로 칭송받는다.',
		location = 'The Water Messenger & The Core of the Water-type',
		location_vn = 'The Water Messenger & The Core of the Water-type',
		location_en = 'The Water Messenger & Core of the Water Type',
		location_kr = '물 속성 전령 & 물 속성 핵심',
		location_th = 'ผู้ส่งสารน้ำ & แกนหลักของประเภทน้ำ',
		location_idn = 'Utusan Air & Inti Tipe Air',
		location_cn = '水之使者&水属性核心'
	},
	[1251] = {
		id = 1251,
		name = 'Vu Cấm',
		name_vn = 'Vu Cấm',
		name_en = 'Wu Jin',
		name_th = 'หวูจิ๋น',
		name_idn = 'Wu Jin',
		name_cn = '俞禁',
		name_kr = '오금',
		cardMarkID = 1251,
		atkType = {7},
		zawakeID = 1251,
		zawakeFragID = 51251,
		chaRecom = {11, 12, 15, 16},
		specValue = {50, 41, 64, 50, 45, 50, 300},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {615, 616, 617, 602, 603},
		levelExpID = 3,
		unitID = 1251,
		skinSkillMap = {[104] = {12511, 12512, 12513, 12516, 61151, 61152}, __size = 1},
		innateSkillID = 12513,
		skillList = {12521, 12522, 12523, 12526, 61151, 61152},
		starSkillSeqID = 1251,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21251,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1251,
		abilitySeqID = 2,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {12511, 12512, 12513, 12514},
		introduction = 'Vu Cấm nổi tiếng với lòng trung thành và dũng cảm trong chiến đấu, là một trong những tướng quân quan trọng dưới trướng của Tào Tháo.',
		introduction_vn = 'Vu Cấm nổi tiếng với lòng trung thành và dũng cảm trong chiến đấu, là một trong những tướng quân quan trọng dưới trướng của Tào Tháo.',
		introduction_en = 'Yu Jin was known for his loyalty and bravery in battle, a key general under Cao Cao.',
		introduction_th = 'อู๋จิ๋ม มีชื่อเสียงในความซื่อสัตย์และความกล้าหาญในการรบ เป็นหนึ่งในแม่ทัพสำคัญของเต้าตง',
		introduction_idn = 'Yu Jin terkenal karena kesetiaan dan keberanian dalam bertempur, adalah salah satu jenderal penting di bawah komando Cao Cao.',
		introduction_cn = '俞谦以忠诚与勇敢著称，为曹操重要将领之一。',
		introduction_kr = '오금은 조조 휘하에서 충성과 용맹으로 유명한 중대한 장수였다.',
		location = 'Burst damage: Desperate Counterattack',
		location_vn = 'Burst damage: Desperate Counterattack',
		location_en = 'Burst Damage: Desperate Counterattack',
		location_kr = '폭발 피해: 절박한 반격',
		location_th = 'ความเสียหายระเบิด: การตอบโต้ในสถานการณ์ยากลำบาก',
		location_idn = 'Kerusakan Ledakan: Serangan Balasan Putus Asa',
		location_cn = '爆发伤害：绝地反击'
	},
	[1271] = {
		id = 1271,
		name = 'Xích Diễm Chu Du',
		name_vn = 'Xích Diễm Chu Du',
		name_en = 'Red Flame Zhou Yu',
		name_th = 'ซีเยิ่นจูตู้',
		name_idn = 'Zhuge Chu Diao',
		name_cn = '赤焰周瑜',
		name_kr = '적염 주유',
		cardMarkID = 1271,
		atkType = {7},
		zawakeID = 1271,
		zawakeFragID = 51271,
		gender = 0,
		chaRecom = {24, 25, 11, 12},
		nValueRnd = 3,
		specValue = {106, 90, 110, 90, 130, 154, 680},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {618, 619, 620, 604, 605},
		levelExpID = 4,
		unitID = 1271,
		skinSkillMap = {[122] = {912711, 912712, 912713, 912716, 61151, 61152}, __size = 1},
		innateSkillID = 12713,
		skillList = {12711, 12712, 12713, 12716, 61151, 61152},
		starSkillSeqID = 1271,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 21271,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1271,
		abilitySeqID = 9,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {12711, 12712, 12713, 12714},
		introduction = 'Chu Du văn võ song toàn, có tài thao lược, tinh thông âm luật, khôi ngô tuấn tú, nên được người đời gọi là Chu Lang.',
		introduction_vn = 'Chu Du văn võ song toàn, có tài thao lược, tinh thông âm luật, khôi ngô tuấn tú, nên được người đời gọi là Chu Lang.',
		introduction_en = 'Zhou Yu was skilled in both civil and military arts, talented in strategy, well-versed in music, handsome and charismatic, earning the nickname Zhou Lang.',
		introduction_th = 'จูตู้มีทั้งวรยุทธและวรศาสตร์ มีความสามารถทางยุทธศาสตร์ เชี่ยวชาญดนตรี และมีหน้าตาทรงเสน่ห์ จึงได้รับการขนานนามว่าจูหลาง',
		introduction_idn = 'Zhou Yu cakap dalam seni bela diri dan strategi, ahli musik, tampan, sehingga dikenal sebagai Zhou Lang.',
		introduction_cn = '周瑜文武双全，才略过人，精通音律，英俊潇洒，人称周郎。',
		introduction_kr = '주유는 문무겸전하며 책략과 음률에 능하고, 수려한 용모로 세인에게 주랑이라 불렸다.',
		location = 'Guardian of the Sky & Rising from the Ashes',
		location_vn = 'Guardian of the Sky & Rising from the Ashes',
		location_en = 'Guardian of the Sky & Rising from Ashes',
		location_kr = '하늘의 수호자 & 재에서 부활',
		location_th = 'ผู้พิทักษ์แห่งท้องฟ้า & ฟื้นขึ้นจากเถ้าถ่าน',
		location_idn = 'Penjaga Langit & Bangkit dari Abu',
		location_cn = '天空守护者&浴火重生'
	},
	[1281] = {
		id = 1281,
		name = 'Dịch Thần Gia Cát Lượng',
		name_vn = 'Dịch Thần Gia Cát Lượng',
		name_en = 'Dịch Thần Gia Cát Lượng',
		name_th = 'Dịch Thần Gia Cát Lượng',
		name_idn = 'Dịch Thần Gia Cát Lượng',
		name_cn = 'Dịch Thần Gia Cát Lượng',
		name_kr = 'Dịch Thần Gia Cát Lượng',
		cardMarkID = 1281,
		atkType = {8},
		zawakeID = 1281,
		zawakeFragID = 51281,
		gender = 0,
		chaRecom = {24, 23, 14, 16},
		nValueRnd = 3,
		specValue = {100, 100, 100, 100, 100, 100, 600},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {606, 607, 608, 604, 605},
		levelExpID = 4,
		unitID = 1281,
		innateSkillID = 12813,
		skillList = {12811, 12812, 12813, 12816, 61151, 61152},
		starSkillSeqID = 1281,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 21281,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1281,
		abilitySeqID = 7,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {12811, 12812, 12813, 12814},
		townSkill = 1563,
		energy = 20,
		introduction = 'This Hero wanders across time. Grass and trees flourish in the forests in which it has appeared.',
		introduction_vn = 'This Hero wanders across time. Grass and trees flourish in the forests in which it has appeared.',
		location = 'Time and Space Crossing, Backtracking Guard',
		location_vn = 'Time and Space Crossing, Backtracking Guard',
		contractPlan = 530
	},
	[1291] = {
		id = 1291,
		name = 'Viên Thiệu',
		name_vn = 'Viên Thiệu',
		name_en = 'Yuan Shao',
		name_th = 'หยวนเสี่ยว',
		name_idn = 'Yuan Shao',
		name_cn = '袁绍',
		name_kr = '원소',
		cardMarkID = 1291,
		atkType = {8},
		zawakeID = 1291,
		zawakeFragID = 51291,
		chaRecom = {6, 7, 18, 19},
		specValue = {40, 70, 45, 35, 65, 55, 310},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 3,
		unitID = 1291,
		innateSkillID = 12913,
		skillList = {12911, 12912, 12913, 12916, 61101, 61102},
		starSkillSeqID = 1291,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21291,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1291,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {12911, 12912, 12913, 12914},
		introduction = 'Viên Thiệu là bạn học thời nhỏ của Tào Tháo. Sau trở thành minh chủ liên minh chống Đổng Trác. Ở trận Quan Độ đại bại dưới tay Tào Tháo rồi sinh bệnh mà mất.',
		introduction_vn = 'Viên Thiệu là bạn học thời nhỏ của Tào Tháo. Sau trở thành minh chủ liên minh chống Đổng Trác. Ở trận Quan Độ đại bại dưới tay Tào Tháo rồi sinh bệnh mà mất.',
		introduction_en = 'Yuan Shao was a childhood friend of Cao Cao. Later became the leader of the coalition against Dong Zhuo. Suffered a major defeat at Guandu by Cao Cao and died of illness thereafter.',
		introduction_th = 'หยวนเสี่ยวเป็นเพื่อนสมัยเด็กของเต้าตง ต่อมาเป็นผู้นำพันธมิตรร่วมต่อต้านตงจั๋ว ในยุทธการกวนตู้พ่ายแพ้ต่อเต้าตงจนป่วยและเสียชีวิต',
		introduction_idn = 'Yuan Shao adalah teman masa kecil Cao Cao. Kemudian menjadi pemimpin aliansi melawan Dong Zhuo. Kalah telak di Pertempuran Guandu oleh Cao Cao dan meninggal karena sakit.',
		introduction_cn = '袁绍是曹操少年时同窗，后为反董卓联盟首领。官渡之战大败于曹操，忧愤而亡。',
		introduction_kr = '원소는 조조의 어린 시절 친구로, 동탁에 맞선 연합군의 지도자였다. 관도 전투에서 조조에게 대패 후 병으로 사망하였다.',
		location = 'Stealth Assassin: Assassinate the Back Line',
		location_vn = 'Stealth Assassin: Assassinate the Back Line',
		location_en = 'Stealth Assassin: Assassinate Back Line',
		location_kr = '은신 암살자: 후열 암살',
		location_th = 'นักฆ่าล่องหน: ลอบสังหารแนวหลัง',
		location_idn = 'Pembunuh Siluman: Membunuh Barisan Belakang',
		location_cn = '潜行刺客：刺杀后排'
	},
	[1301] = {
		id = 1301,
		name = 'Hoàng Cái',
		name_vn = 'Hoàng Cái',
		name_en = 'Huang Gai',
		name_th = 'หวงไค',
		name_idn = 'Huang Gai',
		name_cn = '黄盖',
		name_kr = '황개',
		cardMarkID = 1301,
		atkType = {7},
		zawakeID = 1301,
		zawakeFragID = 51301,
		chaRecom = {10, 11, 13, 9},
		specValue = {45, 45, 60, 40, 70, 50, 310},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {618, 619, 620, 602, 603},
		levelExpID = 3,
		unitID = 1301,
		innateSkillID = 13013,
		skillList = {13011, 13012, 13013, 13016, 61101, 61102},
		starSkillSeqID = 1301,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21301,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1301,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {13011, 13012, 13013, 13014},
		introduction = 'Hoàng Cái là công thần khai quốc của Đông Ngô. Tại Trận Xích Bích, dùng khổ nhục kế, thừa dịp phóng hỏa đại phá quân Tào',
		introduction_vn = 'Hoàng Cái là công thần khai quốc của Đông Ngô. Tại Trận Xích Bích, dùng khổ nhục kế, thừa dịp phóng hỏa đại phá quân Tào',
		introduction_en = 'Huang Gai was a founding hero of Eastern Wu. At the Battle of Red Cliffs, he used the scheme of self-sacrifice and fire to greatly destroy Cao Cao’s forces.',
		introduction_th = 'หวงไค ขุนพลผู้ร่วมก่อตั้งตงงู ในยุทธการฉีผีใช้แผนทรมานใจ โอกาสจุดไฟเผาทำลายกองทัพเต้าตง',
		introduction_idn = 'Huang Gai adalah tokoh pendiri Wu Timur. Dalam Pertempuran Chibi, menggunakan taktik sengsara, memanfaatkan kesempatan membakar dan menghancurkan pasukan Cao.',
		introduction_cn = '黄盖，东吴开国功臣。赤壁之战中施苦肉计，火攻大破曹军。',
		introduction_kr = '황개는 동오 개국 공신으로, 적벽대전에서 고난의 계책을 써서 불을 지피고 조조군을 크게 무찔렀다.',
		location = 'Savage damage: Strong single target attack',
		location_vn = 'Savage damage: Strong single target attack',
		location_en = 'Savage Damage: Powerful Single-target Attack',
		location_kr = '광폭 피해: 강력한 단일 대상 공격',
		location_th = 'ความเสียหายดุร้าย: โจมตีเป้าหมายเดียวอย่างรุนแรง',
		location_idn = 'Kerusakan Buas: Serangan Sasaran Tunggal Kuat',
		location_cn = '狂暴伤害：强力单体攻击'
	},
	[1311] = {
		id = 1311,
		name = 'Tiểu Kiều',
		name_vn = 'Tiểu Kiều',
		name_en = 'Xiao Qiao',
		name_th = 'เสี่ยวเฉียว',
		name_idn = 'Xiao Qiao',
		name_cn = '小乔',
		name_kr = '소교',
		cardMarkID = 1311,
		atkType = {7},
		zawakeID = 1311,
		zawakeFragID = 51311,
		chaRecom = {11, 12, 15, 16},
		specValue = {50, 40, 50, 50, 70, 50, 310},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 3,
		unitID = 1311,
		innateSkillID = 13113,
		skillList = {13111, 13112, 13113, 13116, 61151, 61152},
		starSkillSeqID = 1311,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21311,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1311,
		abilitySeqID = 2,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {13111, 13112, 13113, 13114},
		introduction = 'Tiểu Kiều, vợ Chu Du, mỹ nhân nổi tiếng Tam Quốc, cùng chị gái Đại Kiều được thiên hạ ca tụng là Giang Đông Nhị Kiều.',
		introduction_vn = 'Tiểu Kiều, vợ Chu Du, mỹ nhân nổi tiếng Tam Quốc, cùng chị gái Đại Kiều được thiên hạ ca tụng là Giang Đông Nhị Kiều.',
		introduction_en = 'Xiao Qiao, wife of Zhou Yu and a famous beauty of the Three Kingdoms, along with her elder sister Da Qiao, celebrated as the Two Qiaos of Jiangdong.',
		introduction_th = 'เสี่ยวเฉียว ภรรยาของจูตู้ สาวงามชื่อดังยุคสามก๊ก กับพี่สาวต้าจะได้รับการยกย่องเป็นสองสาวงามแห่งเจียงตง',
		introduction_idn = 'Xiao Qiao, istri Zhou Yu, wanita cantik terkenal Tiga Kerajaan, bersama kakaknya Da Qiao dikenal sebagai Dua Qiao Jiangdong.',
		introduction_cn = '小乔，周瑜妻，三国著名美女，和姐姐大乔合称江东二乔。',
		introduction_kr = '소교는 주유의 부인이자 삼국시대의 유명 미인으로, 누이 대교와 함께 강동의 이교로 불린다.',
		location = 'Water-type front line tank',
		location_vn = 'Water-type front line tank',
		location_en = 'Water-type Frontline Tank',
		location_kr = '물 속성 전열 탱크',
		location_th = 'แท็งค์ประเภทน้ำแนวหน้า',
		location_idn = 'Tank Barisan Depan Tipe Air',
		location_cn = '水属性前排坦克'
	},
	[1351] = {
		id = 1351,
		name = 'Trương Ninh',
		name_vn = 'Trương Ninh',
		name_en = 'Zhang Ning',
		name_th = 'จางหนิง',
		name_idn = 'Zhang Ning',
		name_cn = '张宁',
		name_kr = '장녕',
		cardMarkID = 1351,
		atkType = {8},
		zawakeFragID = 51351,
		chaRecom = {22, 24, 18, 20},
		specValue = {40, 30, 30, 30, 40, 50, 220},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 2,
		unitID = 1351,
		innateSkillID = 13513,
		skillList = {13511, 13512, 13513, 13516, 61151, 61152},
		starSkillSeqID = 1351,
		fragID = 21351,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 1351,
		abilitySeqID = 15,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {13511, 13512, 13513},
		introduction = 'Trương Ninh là con gái của Trương Giác, người đứng đầu của Đạo quân Hoàng Cân  trong thời kỳ cuối triều đại Đông Hán.',
		introduction_vn = 'Trương Ninh là con gái của Trương Giác, người đứng đầu của Đạo quân Hoàng Cân  trong thời kỳ cuối triều đại Đông Hán.',
		introduction_en = 'Zhang Ning was the daughter of Zhang Jiao, leader of the Yellow Turban Army at the end of the Eastern Han dynasty.',
		introduction_th = 'จางหนิง ลูกสาวของจางเจี๋ย หัวหน้ากองทัพผ้าผืนเหลืองในช่วงปลายยุคตงฮั่น',
		introduction_idn = 'Zhang Ning adalah putri Zhang Jiao, pemimpin pasukan Taoist kuning pada akhir Dinasti Han Timur.',
		introduction_cn = '张宁，张角女儿，东汉末黄巾军首领。',
		introduction_kr = '장녕은 장각의 딸로, 동한 말기 황건적 군대의 수장이다.',
		location = 'AOE healing',
		location_vn = 'AOE healing',
		location_en = 'AOE Healing',
		location_kr = '광역 치유',
		location_th = 'รักษาแบบวงกว้าง',
		location_idn = 'Penyembuhan AOE',
		location_cn = '范围治疗'
	},
	[1391] = {
		id = 1391,
		name = 'Thái Văn Cơ',
		name_vn = 'Thái Văn Cơ',
		name_en = 'Tai Wanji',
		name_th = 'ไท่ว่านขี',
		name_idn = 'Tai Wenji',
		name_cn = '太文姬',
		name_kr = '태문기',
		cardMarkID = 1391,
		atkType = {8},
		zawakeID = 1391,
		zawakeFragID = 51391,
		chaRecom = {6, 7, 18, 19},
		specValue = {28, 40, 25, 25, 45, 35, 198},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {606, 607, 608, 602, 603},
		levelExpID = 3,
		unitID = 1391,
		innateSkillID = 13913,
		skillList = {13911, 13912, 13913, 13916, 61101, 61102},
		starSkillSeqID = 1391,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21391,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1391,
		abilitySeqID = 3,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {13911, 13912, 13913, 13914},
		introduction = 'Thái Diễm tự Văn Cơ, là người đa tài, tinh thông âm luật. Được Tào Tháo quý mến tài năng nên đã bỏ một số tiền lớn chuộc về.',
		introduction_vn = 'Thái Diễm tự Văn Cơ, là người đa tài, tinh thông âm luật. Được Tào Tháo quý mến tài năng nên đã bỏ một số tiền lớn chuộc về.',
		introduction_en = 'Tai Yan, courtesy name Wenji, was a versatile person skilled in music. Cao Cao admired his talents and spent a large sum to secure his service.',
		introduction_th = 'ไท่เยี่ยน หรือเวิ่นกี เป็นผู้มีความสามารถหลายด้าน เชี่ยวชาญดนตรี ได้รับความชื่นชมจากเต้าตงจนถูกไถ่ตัวด้วยเงินจำนวนมาก',
		introduction_idn = 'Tai Yan dengan nama kecil Wen Qi, adalah orang multitalenta dan ahli musik. Cao Cao sangat menghargai bakatnya sehingga membayar tebusan besar untuk membawanya pulang.',
		introduction_cn = '太史慈，字文琪，多才多艺，精通音律。受曹操器重，斥巨资赎回。',
		introduction_kr = '태염 자문기라 불리며 다재다능하고 음률에 능했다. 조조가 그의 재능을 아껴 거액을 주고 데려왔다.',
		location = 'Back line control: Disarming power',
		location_vn = 'Back line control: Disarming power',
		location_en = 'Back Line Control: Disarming Power',
		location_kr = '후열 제어: 무장 해제 능력',
		location_th = 'ควบคุมแนวหลัง: พลังถอดอาวุธ',
		location_idn = 'Kontrol Barisan Belakang: Kekuatan Melucuti Senjata',
		location_cn = '后排控制：缴械能力'
	},
	[1421] = {
		id = 1421,
		name = 'Từ Thịnh',
		name_vn = 'Từ Thịnh',
		name_en = 'Ci Sheng',
		name_th = 'จื่อชิ่ง',
		name_idn = 'Ci Sheng',
		name_cn = '司政',
		name_kr = '자성',
		cardMarkID = 1421,
		atkType = {7},
		zawakeID = 1421,
		zawakeFragID = 51421,
		chaRecom = {7, 8, 9, 12},
		specValue = {60, 30, 60, 60, 35, 35, 280},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {606, 607, 608, 602, 603},
		levelExpID = 3,
		unitID = 1421,
		innateSkillID = 14213,
		skillList = {14211, 14212, 14213, 14216, 61101, 61102},
		starSkillSeqID = 1421,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21421,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1421,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {14211, 14212, 14213, 14214},
		introduction = 'Từ Thịnh là một tướng lĩnh Đông Ngô trong thời Tam Quốc. Ông tham gia nhiều trận đánh, phục vụ cho nhiều đời vua của Đông Ngô',
		introduction_vn = 'Từ Thịnh là một tướng lĩnh Đông Ngô trong thời Tam Quốc. Ông tham gia nhiều trận đánh, phục vụ cho nhiều đời vua của Đông Ngô',
		introduction_en = 'Xu Sheng was a general of Eastern Wu during the Three Kingdoms. He participated in many battles, serving under several Wu rulers.',
		introduction_th = 'ซื่อชิ่ง เป็นแม่ทัพตงงูในยุคสามก๊ก เข้าร่วมรบหลายครั้ง รับใช้กษัตริย์หลายยุคของตงงู',
		introduction_idn = 'Xu Sheng adalah jenderal Wu Timur pada masa Tiga Kerajaan. Ia ikut dalam banyak pertempuran dan melayani berbagai raja Wu Timur.',
		introduction_cn = '徐盛，东吴将领，参与多次战役，效力多位东吴君主。',
		introduction_kr = '자성은 삼국시대 동오의 장수로 여러 전투에 참여해 여러 왕을 섬겼다.',
		location = 'Savage damage: One punch man',
		location_vn = 'Savage damage: One punch man',
		location_en = 'Savage Damage: One Punch Man',
		location_kr = '광폭 피해: 원펀맨',
		location_th = 'ความเสียหายดุร้าย: หนึ่งหมัดจบ',
		location_idn = 'Kerusakan Buas: Manusia Pukulan Satu',
		location_cn = '狂暴伤害：一拳超人'
	},
	[1501] = {
		id = 1501,
		name = 'Đổng Trác',
		name_vn = 'Đổng Trác',
		name_en = 'Dong Zhuo',
		name_th = 'ตงจ๋า',
		name_idn = 'Dong Zhuo',
		name_cn = '董卓',
		name_kr = '동탁',
		cardMarkID = 1501,
		atkType = {7},
		zawakeID = 1501,
		zawakeFragID = 51501,
		chaRecom = {15, 16, 11, 12},
		specValue = {50, 30, 70, 100, 40, 40, 330},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {615, 616, 617, 602, 603},
		levelExpID = 3,
		unitID = 1501,
		innateSkillID = 15013,
		skillList = {15011, 15012, 15013, 15016, 61151, 61152},
		starSkillSeqID = 1501,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21501,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1501,
		abilitySeqID = 2,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {15011, 15012, 15013, 15014},
		introduction = 'Đổng Trác là quyền thần nhà Đông Hán. Sau này trúng mỹ nhân kế của Vương Doãn và bị Lữ Bố trảm sát.',
		introduction_vn = 'Đổng Trác là quyền thần nhà Đông Hán. Sau này trúng mỹ nhân kế của Vương Doãn và bị Lữ Bố trảm sát.',
		introduction_en = 'Dong Zhuo was the powerful warlord of the Eastern Han court. Later fell victim to the beauty trap set by Wang Yun and was assassinated by Lü Bu.',
		introduction_th = 'ตงจั๋ว เป็นขุนนางทรงอำนาจของราชวงศ์ตงฮั่น ต่อมาแพ้แผนสาวงามของหวังหยวนและถูกลู่ปู๋สังหาร',
		introduction_idn = 'Dong Zhuo adalah penguasa de facto Dinasti Han Timur. Akhirnya tertipu oleh kecantikan Wang Yun dan dibunuh oleh Lü Bu.',
		introduction_cn = '董卓，东汉权臣。后被王允美人计所害，遭吕布所杀。',
		introduction_kr = '동탁은 동한의 권신으로, 후에 왕윤의 미인계에 걸려 여포에게 참살당했다.',
		location = 'Damage taken by front line: Metal shield',
		location_vn = 'Damage taken by front line: Metal shield',
		location_en = 'Front Line Damage Taken: Metal Shield',
		location_kr = '전열 피해 흡수: 금속 방패',
		location_th = 'ความเสียหายที่ได้รับโดยแนวหน้า: โล่โลหะ',
		location_idn = 'Kerusakan yang Diterima Barisan Depan: Perisai Logam',
		location_cn = '前排所受伤害：金属护盾'
	},
	[1611] = {
		id = 1611,
		name = 'Quách Gia',
		name_vn = 'Quách Gia',
		name_en = 'Guo Jia',
		name_th = 'กัวเจีย',
		name_idn = 'Guo Jia',
		name_cn = '郭嘉',
		name_kr = '곽가',
		cardMarkID = 1611,
		atkType = {8},
		zawakeID = 1611,
		zawakeFragID = 51611,
		chaRecom = {18, 19, 20, 21},
		specValue = {60, 35, 60, 40, 65, 45, 305},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {618, 619, 620, 602, 603},
		levelExpID = 2,
		unitID = 1611,
		innateSkillID = 16113,
		skillList = {16111, 16112, 16113, 16116, 61151, 61152},
		starSkillSeqID = 1611,
		fragID = 21611,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 1611,
		abilitySeqID = 16,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {16111, 16112, 16113},
		introduction = 'Quách Gia là mưu sĩ tinh thông chiến lược của Tào Tháo',
		introduction_vn = 'Quách Gia là mưu sĩ tinh thông chiến lược của Tào Tháo',
		introduction_en = 'Guo Jia was a strategist skilled in military strategy serving Cao Cao.',
		introduction_th = 'กัวเจีย ที่ปรึกษาผู้เชี่ยวชาญกลยุทธ์ของเต้าตง',
		introduction_idn = 'Guo Jia adalah penasihat strategi ulung di bawah Cao Cao.',
		introduction_cn = '郭嘉，曹操谋士，精通战略。',
		introduction_kr = '곽가는 조조의 전략에 능숙한 책사이다.',
		location = 'Charge damage: Fire-type cannon',
		location_vn = 'Charge damage: Fire-type cannon',
		location_en = 'Charge Damage: Fire-type Cannon',
		location_kr = '충전 피해: 불 속성 대포',
		location_th = 'ความเสียหายชาร์จ: ปืนใหญ่ประเภทไฟ',
		location_idn = 'Kerusakan Pengisian: Meriam Tipe Api',
		location_cn = '蓄力伤害：火属性加农炮'
	},
	[1771] = {
		id = 1771,
		name = 'Tân Hiến Anh',
		name_vn = 'Tân Hiến Anh',
		name_en = 'Xin Xianying',
		name_th = 'ซินเซียนอิ่ง',
		name_idn = 'Xin Xianying',
		name_cn = '辛宪英',
		name_kr = '신헌영',
		cardMarkID = 1771,
		atkType = {8},
		chaRecom = {6, 8, 22, 25},
		specValue = {20, 80, 15, 20, 10, 55, 200},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 3,
		unitID = 1771,
		innateSkillID = 17713,
		skillList = {17711, 17712, 17713, 17716, 61151, 61152},
		starSkillSeqID = 1771,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21771,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1771,
		abilitySeqID = 2,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {17711, 17712, 17713, 17714},
		introduction = 'Tân Hiến Anh thông minh, lanh lợi, một tài nữ nổi tiếng thời Ngụy Tấn',
		introduction_vn = 'Tân Hiến Anh thông minh, lanh lợi, một tài nữ nổi tiếng thời Ngụy Tấn',
		introduction_en = 'Xin Xianying was intelligent and quick-witted, a famous talented woman during the Wei-Jin period.',
		introduction_th = 'ซินเซียนอิง ฉลาดเฉลียว เป็นหญิงผู้มีชื่อเสียงในยุคเว่ยจิ้น',
		introduction_idn = 'Xin Xianying cerdas dan lincah, seorang wanita berbakat terkenal pada zaman Wei Jin.',
		introduction_cn = '辛宪英，魏晋时期著名才女，聪明机敏。',
		introduction_kr = '신헌영은 지혜롭고 영리한 위진시대의 유명한 여재이다.',
		location = 'Also expecting to be loved',
		location_vn = 'Also expecting to be loved',
		location_en = 'Also Yearning for Love',
		location_kr = '사랑받길 기대하기도 함',
		location_th = 'ก็หวังจะได้รับความรักเช่นกัน',
		location_idn = 'Juga Mengharapkan Dicintai',
		location_cn = '也渴望被爱'
	},
	[1801] = {
		id = 1801,
		name = 'Âm Hậu',
		name_vn = 'Âm Hậu',
		name_en = 'Yin Hou',
		name_th = 'อวี๋มู่',
		name_idn = 'Yin Hou',
		name_cn = '音侯',
		name_kr = '음후',
		cardMarkID = 1801,
		atkType = {7},
		zawakeID = 1801,
		zawakeFragID = 51801,
		chaRecom = {10, 11, 13, 9},
		specValue = {44, 45, 75, 35, 63, 33, 295},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 2,
		unitID = 1801,
		innateSkillID = 18013,
		skillList = {18011, 18012, 18013, 18016, 61101, 61102},
		starSkillSeqID = 1801,
		fragID = 21801,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 1801,
		abilitySeqID = 18,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {18011, 18012, 18013},
		introduction = 'Âm Hậu là vợ của Hán Hiến Đế Lưu Hiệp, hoàng đế cuối cùng của triều đại Đông Hán. Bà xuất thân từ gia tộc Âm ở Nam Dương.',
		introduction_vn = 'Âm Hậu là vợ của Hán Hiến Đế Lưu Hiệp, hoàng đế cuối cùng của triều đại Đông Hán. Bà xuất thân từ gia tộc Âm ở Nam Dương.',
		introduction_en = 'Empress Yin was the wife of Emperor Xian of Han, Liu Xie, the last emperor of the Eastern Han dynasty. She came from the Yin clan of Nanyang.',
		introduction_th = 'อวมห่าว ภรรยาของฮั่นเซียนตี้ หลิวเสี้ยะ จักรพรรดิองค์สุดท้ายของราชวงศ์ตงฮั่น เธอมาจากตระกูลอวมห่าวแห่งหนานหยาง',
		introduction_idn = 'Empress Yin, istri Kaisar Han Xian Di Liu Xie, kaisar terakhir Dinasti Han Timur. Ia berasal dari keluarga Yin di Nanyang.',
		introduction_cn = '媪后，汉献帝刘协妻子，出身南阳媪氏家族。',
		introduction_kr = '음후는 한 헌제 유협의 부인으로, 동한 말기 마지막 황후이다. 남양 음씨 가문 출신이다.',
		location = 'Cursed Body & Vengeance Control',
		location_vn = 'Cursed Body & Vengeance Control',
		location_en = 'Cursed Body & Vengeance Control',
		location_kr = '저주받은 몸 & 복수 제어',
		location_th = 'ร่างต้องคำสาป & การควบคุมการล้างแค้น',
		location_idn = 'Tubuh Terluka & Kontrol Balas Dendam',
		location_cn = '诅咒之躯&复仇控制'
	},
	[1841] = {
		id = 1841,
		name = 'Cam Ninh',
		name_vn = 'Cam Ninh',
		name_en = 'Gan Ning',
		name_th = 'กานหนิง',
		name_idn = 'Gan Ning',
		name_cn = '甘宁',
		name_kr = '감녕',
		cardMarkID = 1841,
		atkType = {7},
		zawakeFragID = 51841,
		chaRecom = {10, 11, 13, 9},
		specValue = {65, 75, 130, 60, 75, 60, 465},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {606, 607, 608, 602, 603},
		levelExpID = 3,
		unitID = 1841,
		skinSkillMap = {[105] = {18421, 18422, 18423, 18426, 61151, 61152}, __size = 1},
		innateSkillID = 18413,
		skillList = {18411, 18412, 18413, 18416, 61151, 61152},
		starSkillSeqID = 1841,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21841,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1841,
		abilitySeqID = 4,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {18411, 18412, 18413, 18414},
		introduction = 'Cam Ninh là mãnh tướng Giang Đông, theo Chu Du lập công lớn trong trận Xích Bích, đánh bại quân của Tào Tháo, hạ được thành Di Lăng. ',
		introduction_vn = 'Cam Ninh là mãnh tướng Giang Đông, theo Chu Du lập công lớn trong trận Xích Bích, đánh bại quân của Tào Tháo, hạ được thành Di Lăng. ',
		introduction_en = 'Gan Ning was a fierce general of Jiangdong, who, under Zhou Yu, played a major role at the Battle of Red Cliffs, defeating Cao Cao’s forces and capturing Yiling city.',
		introduction_th = 'กานหนิง ขุนพลเจียงตง ร่วมกับจูตู้สร้างผลงานในยุทธการฉีผี ทำลายกองทัพเต้าตงและยึดเมืองดิหลิง',
		introduction_idn = 'Gan Ning adalah jenderal tangguh Jiangdong, ikut serta bersama Zhou Yu meraih kemenangan besar di Pertempuran Chibi, mengalahkan pasukan Cao dan merebut benteng Yiling.',
		introduction_cn = '甘宁，江东猛将，助周瑜赤壁大破曹操，攻下夷陵。',
		introduction_kr = '감녕은 강동의 맹장으로, 주유와 함께 적벽대전에서 조조군을 격파하고 이릉성을 함락시켰다.',
		location = 'Catastrophe arrives & damage reduction',
		location_vn = 'Catastrophe arrives & damage reduction',
		location_en = 'Catastrophe Arrives & Damage Reduction',
		location_kr = '재앙 도래 & 피해 감소',
		location_th = 'ภัยพิบัติมาถึง & ลดความเสียหาย',
		location_idn = 'Bencana Datang & Pengurangan Kerusakan',
		location_cn = '灾难降临&伤害减免'
	},
	[1851] = {
		id = 1851,
		name = 'Tào Thực',
		name_vn = 'Tào Thực',
		name_en = 'Cao Shuang',
		name_th = 'เตียวสือ',
		name_idn = 'Cao Shuo',
		name_cn = '曹植',
		name_kr = '조식',
		cardMarkID = 1851,
		atkType = {8},
		zawakeID = 1851,
		zawakeFragID = 51851,
		chaRecom = {6, 7, 18, 19},
		specValue = {50, 50, 50, 50, 50, 50, 300},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {615, 616, 617, 602, 603},
		levelExpID = 2,
		unitID = 1851,
		innateSkillID = 18513,
		skillList = {18511, 18512, 18513, 18516, 61151, 61152},
		starSkillSeqID = 1851,
		fragID = 21851,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 1851,
		abilitySeqID = 17,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {18511, 18512, 18513},
		introduction = 'Tào Thực là người tài hoa xuất chúng giỏi văn thơ. Cùng với Tào Phi và Tào Tháo, Tào Thực được xưng tụng là Tam Tào, góp công lớn cho sự phát triển của văn học thời kỳ này.',
		introduction_vn = 'Tào Thực là người tài hoa xuất chúng giỏi văn thơ. Cùng với Tào Phi và Tào Tháo, Tào Thực được xưng tụng là Tam Tào, góp công lớn cho sự phát triển của văn học thời kỳ này.',
		introduction_en = 'Cao Zhi was a remarkably talented poet and writer. Along with Cao Pi and Cao Cao, they were known as the Three Caos, greatly contributing to the literature of the era.',
		introduction_th = 'เต้าสือ ผู้มีความสามารถโดดเด่นทางวรรณกรรม ร่วมกับเต้าฝี่และเต้าตงได้รับการยกย่องเป็นสามเต้าช่วยพัฒนาวรรณกรรมยุคนี้',
		introduction_idn = 'Cao Zhi adalah orang berbakat, ahli sastra. Bersama Cao Pi dan Cao Cao, mereka disebut Tiga Cao, memberikan kontribusi besar bagi perkembangan sastra zaman itu.',
		introduction_cn = '曹植，文学天才，擅长诗文。与曹丕、曹操合称“三曹”，对当时文学发展贡献卓著。',
		introduction_kr = '조식은 뛰어난 문학가로, 조비, 조조와 함께 삼조라 불리며 당대 문학 발전에 크게 기여했다.',
		location = 'Wavering Mind & Possess Attacking and Defensive Abilities',
		location_vn = 'Wavering Mind & Possess Attacking and Defensive Abilities',
		location_en = 'Wavering Mind & Possess Offensive and Defensive Abilities',
		location_kr = '흔들리는 마음 & 공격 및 방어 능력 보유',
		location_th = 'จิตใจลังเล & ครอบครองพลังโจมตีและป้องกัน',
		location_idn = 'Pikiran Goyah & Menguasai Kemampuan Serang dan Bertahan',
		location_cn = '动摇心志&兼具攻防能力'
	},
	[1861] = {
		id = 1861,
		name = 'Điển Vi',
		name_vn = 'Điển Vi',
		name_en = 'Dian Wei',
		name_th = 'เตี่ยนอวี๋',
		name_idn = 'Dian Wei',
		name_cn = '典韦',
		name_kr = '전위',
		cardMarkID = 1861,
		atkType = {8},
		zawakeFragID = 51861,
		chaRecom = {18, 20, 15, 14},
		specValue = {70, 25, 55, 50, 40, 50, 290},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {615, 616, 617, 602, 603},
		levelExpID = 2,
		unitID = 1861,
		innateSkillID = 18613,
		skillList = {18611, 18612, 18613, 18616, 61151, 61152},
		starSkillSeqID = 1861,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21861,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 1861,
		abilitySeqID = 17,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {18611, 18612, 18613},
		introduction = 'Điển Vi là võ tướng nổi tiếng với sức mạnh vô địch và sở trường sử dụng song kích. Khi Trương Tủ làm loạn, Điển Vi đã hy sinh thân mình để cứu Tào Tháo.',
		introduction_vn = 'Điển Vi là võ tướng nổi tiếng với sức mạnh vô địch và sở trường sử dụng song kích. Khi Trương Tủ làm loạn, Điển Vi đã hy sinh thân mình để cứu Tào Tháo.',
		introduction_en = 'Dian Wei was a famous warrior known for his unmatched strength and mastery of twin halberds. He sacrificed himself to save Cao Cao during Zhang Miao’s rebellion.',
		introduction_th = 'เตี้ยนหวี่ ขุนพลผู้มีชื่อเสียงในความแข็งแกร่งและชำนาญใช้ดาบคู่ เมื่อจางตู้ก่อกบฏได้เสียสละชีวิตช่วยเต้าตง',
		introduction_idn = 'Dian Wei adalah jenderal terkenal dengan kekuatan luar biasa dan ahli menggunakan dua senjata. Saat Zhang Miao memberontak, Dian Wei berkorban nyawa untuk menyelamatkan Cao Cao.',
		introduction_cn = '典韦，勇猛无敌的武将，擅长双刀。张绣叛乱时为救曹操牺牲。',
		introduction_kr = '전위는 무력과 쌍검 사용에 뛰어난 무장으로, 장탁의 난 때 자신의 목숨을 희생하여 조조를 구했다.',
		location = 'Damage reflection support',
		location_vn = 'Damage reflection support',
		location_en = 'Damage Reflection Support',
		location_kr = '피해 반사 지원',
		location_th = 'สนับสนุนการสะท้อนความเสียหาย',
		location_idn = 'Dukungan Pemantulan Kerusakan',
		location_cn = '伤害反弹支援'
	},
	[1891] = {
		id = 1891,
		name = 'Hình Đạo Vinh',
		name_vn = 'Hình Đạo Vinh',
		name_en = 'Xing Dao Ying',
		name_th = 'หยิ่งเต้าหยิง',
		name_idn = 'Xing Dao Ying',
		name_cn = '形道荣',
		name_kr = '형도영',
		cardMarkID = 1891,
		atkType = {7},
		zawakeID = 1891,
		zawakeFragID = 51891,
		chaRecom = {11, 12, 15, 16},
		specValue = {45, 50, 75, 60, 40, 30, 300},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {618, 619, 620, 602, 603},
		levelExpID = 3,
		unitID = 1891,
		innateSkillID = 18913,
		skillList = {18911, 18912, 18913, 18916, 61101, 61102},
		starSkillSeqID = 1891,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21891,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1891,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {18911, 18912, 18913, 18914},
		introduction = 'Hình Đạo Vinh, đại tướng của Lưu Độ, một viên tướng được Lưu Hiền giới thiệu là sức địch muôn người, sử dụng búa khai sơn. ',
		introduction_vn = 'Hình Đạo Vinh, đại tướng của Lưu Độ, một viên tướng được Lưu Hiền giới thiệu là sức địch muôn người, sử dụng búa khai sơn. ',
		introduction_en = 'Xing Dao Ying, a general under Liu Du, was introduced by Liu Xian as a formidable warrior wielding a mountain-splitting hammer.',
		introduction_th = 'หยิ่งเต๋าวิง นายทหารของหลิวตู้ เป็นขุนพลที่หลิวเสียนแนะนำว่าแข็งแกร่งเทียบเท่าหลายคน ใช้ค้อนทำลายภูเขา',
		introduction_idn = 'Xing Daorong, jenderal Liu Du, diperkenalkan oleh Liu Xian sebagai lawan yang tangguh, menggunakan palu untuk membuka benteng.',
		introduction_cn = '形道荣，刘度大将，刘贤引荐为千军敌手，擅长使用大锤开山。',
		introduction_kr = '형도영은 유도의 대장으로, 유현이 추천한 만인 적수의 장수이며, 개산용 망치를 사용한다.',
		location = 'Savage damage: Crushing true damage',
		location_vn = 'Savage damage: Crushing true damage',
		location_en = 'Savage Damage: Crushing True Damage',
		location_kr = '광폭 피해: 진짜 피해 분쇄',
		location_th = 'ความเสียหายดุร้าย: บดขยี้ความเสียหายแท้จริง',
		location_idn = 'Kerusakan Buas: Menghancurkan Kerusakan Sebenarnya',
		location_cn = '狂暴伤害：碾压真实伤害'
	},
	[1901] = {
		id = 1901,
		name = 'Quan Vũ OLD',
		name_vn = 'Quan Vũ OLD',
		name_en = 'Guan Yu OLD',
		name_th = 'กวนอู OLD',
		name_idn = 'Guan Yu OLD',
		name_cn = '关羽 OLD',
		name_kr = '관우 OLD',
		cardMarkID = 1901,
		atkType = {7},
		zawakeID = 1901,
		zawakeFragID = 51901,
		gender = 0,
		chaRecom = {24, 23, 14, 16},
		specValue = {40, 30, 55, 80, 35, 60, 300},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {615, 616, 617, 602, 603},
		levelExpID = 3,
		unitID = 1901,
		innateSkillID = 19013,
		skillList = {19011, 19012, 19013, 19016, 61151, 61152},
		starSkillSeqID = 1901,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21901,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1901,
		abilitySeqID = 2,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {19011, 19012, 19013, 19014},
		introduction = 'Quan Vũ đứng đầu trong Ngũ Hổ Thượng Tướng. Nổi tiếng trung nghĩa, anh dũng nên sau này được thiên hạ tôn làm Võ Thánh.',
		introduction_vn = 'Quan Vũ đứng đầu trong Ngũ Hổ Thượng Tướng. Nổi tiếng trung nghĩa, anh dũng nên sau này được thiên hạ tôn làm Võ Thánh.',
		introduction_en = 'Guan Yu was the leader of the Five Tiger Generals. Famous for loyalty and bravery, he was later revered as the Martial Saint.',
		introduction_th = 'กวนอูผู้นำห้าขุนพลผู้กล้าหาญ มีชื่อเสียงในความซื่อสัตย์และกล้าหาญ จึงได้รับการยกย่องเป็นเทพเจ้าแห่งสงคราม',
		introduction_idn = 'Guan Yu memimpin lima jenderal harimau. Dikenal karena kesetiaan dan keberanian, kemudian dihormati sebagai Dewa Perang.',
		introduction_cn = '关羽，五虎上将之首。以忠义英勇著称，后被尊为武圣。',
		introduction_kr = '관우는 오호상장 중 으뜸으로 충성과 용맹이 뛰어나 후에 무성으로 추존되었다.',
		location = 'Steel-type fort: Taunts the back line',
		location_vn = 'Steel-type fort: Taunts the back line',
		location_en = 'Steel-type Fort: Taunts Back Line',
		location_kr = '강철 속성 요새: 후열 도발',
		location_th = 'ป้อมประเภทเหล็ก: ล่อเป้าแนวหลัง',
		location_idn = 'Benteng Tipe Baja: Memprovokasi Barisan Belakang',
		location_cn = '钢属性堡垒：嘲讽后排'
	},
	[1911] = {
		id = 1911,
		name = 'Lữ Bố',
		name_vn = 'Lữ Bố',
		name_en = 'Lu Bu',
		name_th = 'หลู่ปู้',
		name_idn = 'Lu Bu',
		name_cn = '吕布',
		name_kr = '여포',
		cardMarkID = 1911,
		atkType = {7},
		zawakeID = 1911,
		zawakeFragID = 51911,
		gender = 0,
		chaRecom = {15, 16, 11, 12},
		nValueRnd = 2,
		specValue = {80, 50, 100, 200, 50, 100, 580},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {615, 616, 617, 602, 603},
		levelExpID = 3,
		unitID = 1911,
		skinSkillMap = {[117] = {919111, 919112, 919113, 919116, 61101, 61102}, __size = 1},
		innateSkillID = 19113,
		skillList = {19111, 19112, 19113, 19116, 61101, 61102},
		starSkillSeqID = 1911,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 21911,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1911,
		abilitySeqID = 2,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {19111, 19112, 19113, 19114},
		introduction = 'Lữ Bố tự là Phụng Tiên, mãnh tướng số một tam quốc, cưỡi Xích Thổ, tay cầm Phương Thiên Kích, được ca tụng là chiến thần!',
		introduction_vn = 'Lữ Bố tự là Phụng Tiên, mãnh tướng số một tam quốc, cưỡi Xích Thổ, tay cầm Phương Thiên Kích, được ca tụng là chiến thần!',
		introduction_en = 'Lü Bu, courtesy name Fengxian, was the most formidable general of the Three Kingdoms, riding the Red Hare horse and wielding the Sky Piercer spear, celebrated as a war god!',
		introduction_th = 'ลู่ปู๋ หรือที่รู้จักในนามฟงเซียน เป็นขุนพลผู้เก่งสุดแห่งยุคสามก๊ก ขี่ม้าซือโถว ถือหอกฟางเทียนจวี่ ได้รับการยกย่องเป็นเทพนักรบ',
		introduction_idn = 'Lü Bu, bernama kehormatan Fengxian, jenderal terkuat zaman Tiga Kerajaan, menunggangi kuda Chitu, memegang tombak Fangtian Ji, dijuluki Dewa Perang!',
		introduction_cn = '吕布，字奉先，三国第一猛将，骑赤兔马，执方天画戟，被称战神！',
		introduction_kr = '여포는 자봉선으로, 삼국 최고의 맹장으로 적토마를 타고 방천극을 휘두르며 전신이라 불렸다!',
		location = 'Rock Blast & Formation Guard',
		location_vn = 'Rock Blast & Formation Guard',
		location_en = 'Rock Blast & Formation Guard',
		location_kr = '암석 폭발 & 진형 수호',
		location_th = 'ระเบิดหิน & การป้องกันกองทัพ',
		location_idn = 'Letusan Batu & Penjaga Formasi',
		location_cn = '岩石冲击&阵型守护'
	},
	[1961] = {
		id = 1961,
		name = 'Trạch Vân Hoa Đà',
		name_vn = 'Trạch Vân Hoa Đà',
		name_en = 'Trạch Vân Hoa Đà',
		name_th = 'Trạch Vân Hoa Đà',
		name_idn = 'Trạch Vân Hoa Đà',
		name_cn = 'Trạch Vân Hoa Đà',
		name_kr = 'Trạch Vân Hoa Đà',
		cardMarkID = 1961,
		atkType = {8},
		zawakeID = 1961,
		zawakeFragID = 51961,
		gender = 0,
		chaRecom = {22, 24, 18, 20},
		nValueRnd = 3,
		specValue = {100, 90, 100, 90, 150, 140, 670},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {621, 622, 623, 604, 605},
		levelExpID = 4,
		unitID = 1961,
		innateSkillID = 19613,
		skillList = {19611, 19612, 19613, 19616, 61151, 61152},
		starSkillSeqID = 1961,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 21961,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1961,
		abilitySeqID = 9,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {19611, 19612, 19613, 19614},
		introduction = 'Kyogre has appeared in mythology as the creator of the sea. After long years of feuding with Groudon, it took to sleep at the bottom of the sea.',
		introduction_vn = 'Kyogre has appeared in mythology as the creator of the sea. After long years of feuding with Groudon, it took to sleep at the bottom of the sea.',
		introduction_en = 'Kyogre has appeared in mythology as the creator of the sea. After long years of feuding with Groudon, it took to sleep at the bottom of the sea.',
		introduction_th = 'Kyogre has appeared in mythology as the creator of the sea. After long years of feuding with Groudon, it took to sleep at the bottom of the sea.',
		introduction_idn = 'Kyogre has appeared in mythology as the creator of the sea. After long years of feuding with Groudon, it took to sleep at the bottom of the sea.',
		introduction_cn = 'Kyogre has appeared in mythology as the creator of the sea. After long years of feuding with Groudon, it took to sleep at the bottom of the sea.',
		introduction_kr = 'Kyogre has appeared in mythology as the creator of the sea. After long years of feuding with Groudon, it took to sleep at the bottom of the sea.',
		location = 'Master of the Ocean&Strong dispel&Crit Resistance',
		location_vn = 'Master of the Ocean&Strong dispel&Crit Resistance',
		location_en = 'Master of the Ocean&Strong dispel&Crit Resistance',
		location_kr = 'Master of the Ocean&Strong dispel&Crit Resistance',
		location_th = 'Master of the Ocean&Strong dispel&Crit Resistance',
		location_idn = 'Master of the Ocean&Strong dispel&Crit Resistance',
		location_cn = 'Master of the Ocean&Strong dispel&Crit Resistance'
	},
	[1971] = {
		id = 1971,
		name = 'Trọng Minh Trương Phi',
		name_vn = 'Trọng Minh Trương Phi',
		name_en = 'Zhongming Zhang Fei',
		name_th = 'จงหมิงจางฟี',
		name_idn = 'Zhongming Zhang Fei',
		name_cn = '重明张飞',
		name_kr = '중명 장비',
		cardMarkID = 1971,
		atkType = {7},
		zawakeID = 1971,
		zawakeFragID = 51971,
		gender = 0,
		chaRecom = {11, 12, 15, 16},
		nValueRnd = 3,
		specValue = {100, 90, 150, 140, 100, 90, 670},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {609, 610, 611, 604, 605},
		levelExpID = 4,
		unitID = 1971,
		innateSkillID = 19713,
		skillList = {19711, 19712, 19713, 19716, 61151, 61152},
		starSkillSeqID = 1971,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 21971,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1971,
		abilitySeqID = 7,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {19711, 19712, 19713, 19714},
		introduction = 'Trương Phi, tự Dục Đức, danh tướng Thục Hán thời Tam Quốc. Kết nghĩa vườn đào với Lưu Bị và Quan Vũ, từng đại chiến Lữ Bố ở Hổ Lao Quan, thét lui đại quân Tào Tháo ở Trường Bản Pha. Về sau được phong làm Tây Hương Hầu.',
		introduction_vn = 'Trương Phi, tự Dục Đức, danh tướng Thục Hán thời Tam Quốc. Kết nghĩa vườn đào với Lưu Bị và Quan Vũ, từng đại chiến Lữ Bố ở Hổ Lao Quan, thét lui đại quân Tào Tháo ở Trường Bản Pha. Về sau được phong làm Tây Hương Hầu.',
		introduction_en = 'Zhang Fei, courtesy name Yude, was a famous general of Shu Han. Swore brotherhood with Liu Bei and Guan Yu in the Peach Garden. Fought fiercely against Lü Bu at Hulao Pass and shouted back Cao Cao’s massive army at Changban. Later titled Marquis of Xixiang.',
		introduction_th = 'จางเฟย ชื่อเล่นหยู่ตู้ ขุนพลรัฐฉู่ฮั่นในยุคสามก๊ก สาบานเป็นพี่น้องกับหลิวเป่ยและกวนอู เคยรบกับลู่ปู๋ที่หูล่าวกวน และกรีดร้องขับไล่กองทัพเต้าตงที่ฉางปั้นผา ได้รับตำแหน่งไท่เซียงโฮ่ว',
		introduction_idn = 'Zhang Fei, bernama kehormatan Yude, jenderal Shu Han pada masa Tiga Kerajaan. Bersaudara sumpah persahabatan dengan Liu Bei dan Guan Yu di Kebun Persik, pernah bertempur melawan Lü Bu di Hulao Pass, meneriakkan mundur pasukan Cao Cao di Changban. Kemudian diberi gelar Marquess of West Xiang.',
		introduction_cn = '张飞，字益德，蜀汉名将。与刘备、关羽桃园结义，曾于虎牢关大战吕布，长坂坡怒吼击退曹军。后封为西乡侯。',
		introduction_kr = '장비는 자욕덕으로, 촉한의 명장이다. 유비, 관우와 도원결의를 맺고 호로관에서 여포와 대전하며 장판파에서 조조군을 격퇴했다. 후에 서향후에 봉해졌다.',
		location = 'Land King&Earthquake Attack',
		location_vn = 'Land King&Earthquake Attack',
		location_en = 'Land King & Earthquake Attack',
		location_kr = '땅의 왕 & 지진 공격',
		location_th = 'จ้าวแห่งแผ่นดิน & การโจมตีแผ่นดินไหว',
		location_idn = 'Raja Tanah & Serangan Gempa',
		location_cn = '大地之王&地震攻击'
	},
	[1981] = {
		id = 1981,
		name = 'Mộng Huyễn Tả Từ',
		name_vn = 'Mộng Huyễn Tả Từ',
		name_en = 'Dream of Illusions: Zuo Ci',
		name_th = 'มộngฮวียนซ้ายซี',
		name_idn = 'Dream of Illusions: Zuo Ci',
		name_cn = '梦幻左慈',
		name_kr = '몽현 좌사',
		cardMarkID = 1981,
		zawakeID = 1981,
		zawakeFragID = 51981,
		gender = 0,
		chaRecom = {10, 11, 13, 9},
		nValueRnd = 3,
		specValue = {105, 95, 150, 90, 150, 90, 680},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {618, 619, 620, 604, 605},
		levelExpID = 4,
		unitID = 1981,
		skinSkillMap = {[124] = {919811, 919812, 919813, 919816, 61101, 61102}, __size = 1},
		innateSkillID = 19813,
		skillList = {19811, 19812, 19813, 19816, 61101, 61102},
		starSkillSeqID = 1981,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 21981,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1981,
		abilitySeqID = 6,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {19811, 19812, 19813, 19814},
		townSkill = 1113,
		energy = 20,
		energyCorrection = 1.05,
		introduction = 'Tả Từ ngụ tại Thiên Trụ Sơn, nghiên cứu tiên thuật. Nghe đồn ông thông hiểu ngũ kinh lục giáp, đã đạt đến mức có thể cưỡi kiếm bay cao, sai khiến quỷ thần.',
		introduction_vn = 'Tả Từ ngụ tại Thiên Trụ Sơn, nghiên cứu tiên thuật. Nghe đồn ông thông hiểu ngũ kinh lục giáp, đã đạt đến mức có thể cưỡi kiếm bay cao, sai khiến quỷ thần.',
		introduction_en = 'Zuo Ci lived on Mount Tianzhu, studying immortal arts. Rumored to have mastered the Five Classics and Six Jias, able to fly on a sword and command spirits.',
		introduction_th = 'ซ้ายซื่ออาศัยอยู่ที่ภูเขาเทียนจู ศึกษาวิชาจำเริญ ว่ากันว่าเขาเข้าใจตำรับห้าหลักและหกเกราะจนสามารถขี่ดาบบินสูง บังคับผีปีศาจได้',
		introduction_idn = 'Zuo Ci tinggal di Gunung Tianzhu, mempelajari ilmu Tao. Konon ia menguasai lima kitab dan enam alkimia, mampu terbang dengan menunggangi pedang dan mengendalikan roh serta dewa.',
		introduction_cn = '左慈隐居天柱山，研习仙术。传闻通晓五经六甲，能骑剑飞天，驱使鬼神。',
		introduction_kr = '좌사는 천축산에 거주하며 선술을 연구했다. 오경육갑에 통달해 검을 타고 날아다니며 귀신을 부린다고 전해진다.',
		location = 'Master of the Sky & Expert in P. and SP.',
		location_vn = 'Master of the Sky & Expert in P. and SP.',
		location_en = 'Master of the Sky & Expert in P. and SP.',
		location_kr = '하늘의 장인 & P.와 SP. 전문가',
		location_th = 'เจ้าแห่งท้องฟ้า & ผู้เชี่ยวชาญด้านพลังจิตและพลังวิญญาณ',
		location_idn = 'Penguasa Langit & Ahli P. dan SP.',
		location_cn = '天空大师&物理与法术专家',
		contractPlan = 510
	},
	[1991] = {
		id = 1991,
		name = 'Lưu Hồng Quách Gia',
		name_vn = 'Lưu Hồng Quách Gia',
		name_en = 'Liu Hong Guo Jia',
		name_th = 'หลิวหงกัวเจีย',
		name_idn = 'Liu Hong Guo Jia',
		name_cn = '刘弘郭嘉',
		name_kr = '유홍 곽가',
		cardMarkID = 1991,
		zawakeFragID = 51991,
		gender = 0,
		chaRecom = {24, 23, 14, 16},
		nValueRnd = 3,
		specValue = {100, 100, 100, 100, 100, 100, 600},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {615, 616, 617, 604, 605},
		levelExpID = 4,
		unitID = 1991,
		skinSkillMap = {[126] = {19921, 19922, 19923, 19926, 61151, 61152}, __size = 1},
		innateSkillID = 19913,
		skillList = {19911, 19912, 19913, 19916, 61151, 61152},
		starSkillSeqID = 1991,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 21991,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 1991,
		abilitySeqID = 9,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {19911, 19912, 19913, 19914},
		introduction = 'Quách Gia có khả năng nhìn xa trông rộng, nhận biết sự thay đổi dù rất nhỏ. Ông là một nhân tài chiến lược và người kỳ lạ, đóng góp không nhỏ vào thành công của Tào Tháo.',
		introduction_vn = 'Quách Gia có khả năng nhìn xa trông rộng, nhận biết sự thay đổi dù rất nhỏ. Ông là một nhân tài chiến lược và người kỳ lạ, đóng góp không nhỏ vào thành công của Tào Tháo.',
		introduction_en = 'Guo Jia had farsighted insight, noticing even the smallest changes. A strategic genius and an unusual figure, he contributed greatly to Cao Cao’s success.',
		introduction_th = 'กัวเจีย มีความสามารถมองการณ์ไกล รับรู้การเปลี่ยนแปลงแม้เล็กน้อย เป็นนักกลยุทธ์และบุคคลลึกลับ มีส่วนสำคัญต่อความสำเร็จของเต้าตง',
		introduction_idn = 'Guo Jia memiliki penglihatan jauh dan mampu mengenali perubahan kecil sekalipun. Ia adalah seorang jenius strategi dan sosok aneh, berkontribusi besar pada kesuksesan Cao Cao.',
		introduction_cn = '郭嘉，洞察远识，能察细微变化。为曹操成功立下汗马功劳，既是战略奇才亦怪异人物。',
		introduction_kr = '곽가는 먼 앞날까지 내다보며 작은 변화도 알아차리는 능력을 지녔다. 그는 조조의 성공에 크게 기여한 전략적 인재이자 독특한 인물이다.',
		location = 'Wishing Star of Seven Nights',
		location_vn = 'Wishing Star of Seven Nights',
		location_en = 'Wishing Star of Seven Nights',
		location_kr = '일곱 밤의 소원별',
		location_th = 'ดวงดาวแห่งเจ็ดคืน',
		location_idn = 'Bintang Harapan Tujuh Malam',
		location_cn = '七星许愿星'
	},
	[2021] = {
		id = 2021,
		name = 'Liêu Nguyên Hoả',
		name_vn = 'Liêu Nguyên Hoả',
		name_en = 'Liao Yuan Huo',
		name_th = 'เหลียวหยวนหั่ว',
		name_idn = 'Liao Yuan Huo',
		name_cn = '辽元火',
		name_kr = '요원 화',
		cardMarkID = 2021,
		chaRecom = {10, 11, 13, 9},
		specValue = {44, 61, 58, 44, 58, 44, 309},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {618, 619, 620, 602, 603},
		levelExpID = 3,
		unitID = 2021,
		innateSkillID = 20213,
		skillList = {20211, 20212, 20213, 20216, 61101, 61102},
		starSkillSeqID = 2021,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 22021,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 2021,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {20211, 20212, 20213, 20214},
		introduction = 'Liêu Nguyên Hỏa, sát thủ của tổ chức Tàn Binh, giỏi dùng thương, kiếm, ám khí, từng dùng tên giả Triệu Hỏa.',
		introduction_vn = 'Liêu Nguyên Hỏa, sát thủ của tổ chức Tàn Binh, giỏi dùng thương, kiếm, ám khí, từng dùng tên giả Triệu Hỏa.',
		introduction_en = 'Liao Yuanhuo, an assassin of the Scavenger organization, skilled with spear, sword, and hidden weapons; once used the alias Zhao Huo.',
		introduction_th = 'เหลียวหยวนหัว นักฆ่าขององค์กรทานบิง เชี่ยวชาญใช้หอก ดาบ และอาวุธลับ เคยใช้ชื่อปลอมเจียวหัว',
		introduction_idn = 'Liao Yuanhuo, pembunuh organisasi Sisa Prajurit, ahli menggunakan tombak, pedang, dan senjata rahasia, pernah menggunakan nama samaran Zhao Huo.',
		introduction_cn = '廖元火，残兵组织刺客，擅长长枪、剑及暗器，曾用假名赵火。',
		introduction_kr = '요원화는 잔병 조직의 암살자로 창, 검, 암기를 능숙하게 다루며, 한때 조호라는 가명을 사용했다.',
		location = 'Violent output: Burning Soul Awakens',
		location_vn = 'Violent output: Burning Soul Awakens',
		location_en = 'Violent Output: Burning Soul Awakens',
		location_kr = '폭력적 출력: 타오르는 영혼 각성',
		location_th = 'การโจมตีรุนแรง: จิตวิญญาณลุกโชนตื่นขึ้น',
		location_idn = 'Output Kekerasan: Kebangkitan Jiwa yang Membara',
		location_cn = '暴力输出：燃魂觉醒'
	},
	[2071] = {
		id = 2071,
		name = 'Hạ Hầu Uyên',
		name_vn = 'Hạ Hầu Uyên',
		name_en = 'Xiahou Yuan',
		name_th = 'ฮ่าฝูหยวน',
		name_idn = 'Xiahou Yuan',
		name_cn = '夏侯渊',
		name_kr = '하후연',
		cardMarkID = 2071,
		atkType = {7},
		zawakeID = 2071,
		zawakeFragID = 52071,
		chaRecom = {7, 8, 9, 12},
		specValue = {45, 45, 65, 34, 40, 34, 263},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {612, 613, 614, 602, 603},
		levelExpID = 2,
		unitID = 2071,
		innateSkillID = 20713,
		skillList = {20711, 20712, 20713, 20716, 61101, 61102},
		starSkillSeqID = 2071,
		fragID = 22071,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 2071,
		abilitySeqID = 16,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {20711, 20712, 20713},
		introduction = 'Hạ Hầu Uyên, một trong tám Hổ Kỵ, là chiến tướng phe Tào Ngụy. Khi trấn thủ Hán Trung, ông bị Hoàng Trung tập kích và tử trận.',
		introduction_vn = 'Hạ Hầu Uyên, một trong tám Hổ Kỵ, là chiến tướng phe Tào Ngụy. Khi trấn thủ Hán Trung, ông bị Hoàng Trung tập kích và tử trận.',
		introduction_en = 'Xiahou Yuan, one of the Eight Tiger Cavalry, was a general of Cao Wei. While defending Hanzhong, he was ambushed and killed by Huang Zhong.',
		introduction_th = 'ฮ่าฮ่าวหยวน หนึ่งในแปดเสือม้า เป็นแม่ทัพฝ่ายเต้าหวี่ ขณะรักษาการฮั่นจง ถูกหวงจงโจมตีและเสียชีวิต',
		introduction_idn = 'Xiahou Yuan, salah satu dari Delapan Harimau Berkuda, adalah jenderal Cao Wei. Saat menahan Han Zhong, ia disergap dan gugur oleh Huang Zhong.',
		introduction_cn = '夏侯渊，八虎骑之一，曹魏名将。镇守汉中时被黄忠突袭阵亡。',
		introduction_kr = '하후연은 팔호기 중 한 명으로 조위의 장수이다. 한중을 수비하다 황중의 기습을 받아 전사하였다.',
		location = 'Perseverance Damage',
		location_vn = 'Perseverance Damage',
		location_en = 'Perseverance Damage',
		location_kr = '인내 피해',
		location_th = 'ความเสียหายความอดทน',
		location_idn = 'Kerusakan Ketekunan',
		location_cn = '毅力伤害'
	},
	[2241] = {
		id = 2241,
		name = 'Lữ Linh Khởi',
		name_vn = 'Lữ Linh Khởi',
		name_en = 'Lu Lingqi',
		name_th = 'หลู่หลิงฉี',
		name_idn = 'Lu Lingqi',
		name_cn = '吕灵起',
		name_kr = '여령기',
		cardMarkID = 2241,
		zawakeFragID = 52241,
		chaRecom = {7, 8, 9, 12},
		specValue = {40, 60, 70, 40, 35, 40, 285},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {612, 613, 614, 602, 603},
		levelExpID = 3,
		unitID = 2241,
		innateSkillID = 22413,
		skillList = {22411, 22412, 22413, 22416, 61101, 61102},
		starSkillSeqID = 2241,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 22241,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 2241,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {22411, 22412, 22413, 22414},
		introduction = 'Con gái của Lữ Bố và Điêu Thuyền. Kế thừa vẻ đẹp của mẹ và võ nghệ cao cường của cha, vẻ ngoài sáng sủa, tính cách lạnh lùng, sát phạt quyết đoán.',
		introduction_vn = 'Con gái của Lữ Bố và Điêu Thuyền. Kế thừa vẻ đẹp của mẹ và võ nghệ cao cường của cha, vẻ ngoài sáng sủa, tính cách lạnh lùng, sát phạt quyết đoán.',
		introduction_en = 'Daughter of Lü Bu and Diao Chan. Inherited her mother’s beauty and father’s martial prowess; bright appearance, cold personality, decisive and ruthless in battle.',
		introduction_th = 'ลูกสาวของลู่ปู๋และเตียวเทียน สืบทอดความงามจากแม่และความสามารถทางยุทธศิลป์จากพ่อ รูปร่างสง่างาม บุคลิกเยือกเย็นและเด็ดขาด',
		introduction_idn = 'Putri Lü Bu dan Diao Chan. Mewarisi kecantikan sang ibu dan keterampilan bela diri ayahnya, penampilan cerah dan kepribadian dingin, tegas dalam pertempuran.',
		introduction_cn = '吕布与貂蝉之女，继承母亲美貌与父亲高强武艺，外貌清秀，性格冷酷，果断杀伐。',
		introduction_kr = '여포와 조비의 딸로, 어머니의 미모와 아버지의 뛰어난 무예를 이어받았으며, 밝은 외모에 냉철하고 단호한 성격을 지녔다.',
		location = 'Savage damage: Deepening status condition',
		location_vn = 'Savage damage: Deepening status condition',
		location_en = 'Savage Damage: Deepening Status Effects',
		location_kr = '광폭 피해: 상태 이상 심화',
		location_th = 'ความเสียหายดุร้าย: ทำให้สถานะลึกซึ้งยิ่งขึ้น',
		location_idn = 'Kerusakan Buas: Memperdalam Kondisi Status',
		location_cn = '狂暴伤害：加深状态异常'
	},
	[2351] = {
		id = 2351,
		name = 'Lẫm Dục Lưu Bị',
		name_vn = 'Lẫm Dục Lưu Bị',
		name_en = 'Lian Yu Liu Bei',
		name_th = 'หลิ๋มหยู่หลิวเป่ย',
		name_idn = 'Lin Yu Liu Bei',
		name_cn = '林育刘备',
		name_kr = '림욕 유비',
		cardMarkID = 2351,
		atkType = {8},
		zawakeID = 2351,
		zawakeFragID = 52351,
		gender = 0,
		chaRecom = {18, 19, 20, 21},
		nValueRnd = 3,
		specValue = {100, 90, 120, 120, 150, 100, 680},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {615, 616, 617, 604, 605},
		levelExpID = 4,
		unitID = 2351,
		innateSkillID = 23513,
		skillList = {23511, 23512, 23513, 23516, 61101, 61102},
		starSkillSeqID = 2351,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 22351,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 2351,
		abilitySeqID = 6,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {23511, 23512, 23513, 23514},
		introduction = 'Hán Chiêu Liệt Đế Lưu Bị, hoàng đế khai quốc nước Thục. Là người có đạo đức cao thượng, được lòng dân. Về sau ủy thác cho Gia Cát Lượng phò chính.',
		introduction_vn = 'Hán Chiêu Liệt Đế Lưu Bị, hoàng đế khai quốc nước Thục. Là người có đạo đức cao thượng, được lòng dân. Về sau ủy thác cho Gia Cát Lượng phò chính.',
		introduction_en = 'Emperor Shao of Han, Liu Bei, founding emperor of Shu Han. A man of high virtue, beloved by the people. Later entrusted Zhuge Liang as regent.',
		introduction_th = 'ฮั่นเฉียวเหลียดตี้ หลิวเป่ย จักรพรรดิผู้ก่อตั้งรัฐฉู่ เป็นคนมีคุณธรรมสูง ชาวบ้านรักใคร่ ต่อมาฝากให้เจียฉาเหลียงช่วยดูแลราชการ',
		introduction_idn = 'Kaisar Han Zhao Lie Di Liu Bei, pendiri kerajaan Shu. Orang yang bermoral tinggi dan dicintai rakyat. Kemudian mempercayakan pemerintahan kepada Zhuge Liang.',
		introduction_cn = '汉昭烈帝刘备，蜀汉开国皇帝。品德高尚，得民心。后托孤诸葛亮辅政。',
		introduction_kr = '한조 열제 유비는 촉한의 개국 황제로 고결한 덕망으로 백성의 신임을 받았다. 후에 제갈량에게 정사를 맡겼다.',
		location = 'God of Time',
		location_vn = 'God of Time',
		location_en = 'God of Time',
		location_kr = '시간의 신',
		location_th = 'เทพแห่งกาลเวลา',
		location_idn = 'Dewa Waktu',
		location_cn = '时间之神'
	},
	[2361] = {
		id = 2361,
		name = 'Hào Nhận Tào Tháo',
		name_vn = 'Hào Nhận Tào Tháo',
		name_en = 'Hao Ren Cao Cao',
		name_th = 'ห่าวเหรินเตียวโต๊ะ',
		name_idn = 'Hao Ren Cao Cao',
		name_cn = '豪仁曹操',
		name_kr = '호인 조조',
		cardMarkID = 2361,
		atkType = {8},
		zawakeID = 2361,
		zawakeFragID = 52361,
		gender = 0,
		chaRecom = {18, 19, 20, 21},
		nValueRnd = 3,
		specValue = {90, 100, 120, 100, 150, 120, 680},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {621, 622, 623, 604, 605},
		levelExpID = 4,
		unitID = 2361,
		innateSkillID = 23613,
		skillList = {23611, 23612, 23613, 23616, 61101, 61102},
		starSkillSeqID = 2361,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 22361,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 2361,
		abilitySeqID = 6,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {23611, 23612, 23613, 23614},
		introduction = 'Tào Tháo xuất thân từ gia đình quan thần, hậu duệ Hán thất. Sau này, Tào Tháo đến kinh thành Lạc Dương làm quan và trở thành nhà chính trị và quân sự kiệt xuất cuối thời Đông Hán.',
		introduction_vn = 'Tào Tháo xuất thân từ gia đình quan thần, hậu duệ Hán thất. Sau này, Tào Tháo đến kinh thành Lạc Dương làm quan và trở thành nhà chính trị và quân sự kiệt xuất cuối thời Đông Hán.',
		introduction_en = 'Cao Cao came from a family of officials, descendants of the Han dynasty. Later he came to the capital Luoyang to serve and became a distinguished politician and military leader at the end of the Eastern Han.',
		introduction_th = 'เต้าตงเกิดจากครอบครัวขุนนาง เป็นทายาทราชวงศ์ฮั่น ต่อมาเต้าตงไปทำงานในเมืองหลวงลั่วหยางและกลายเป็นนักการเมืองและแม่ทัพผู้ยิ่งใหญ่ในยุคปลายตงฮั่น',
		introduction_idn = 'Cao Cao berasal dari keluarga pejabat, keturunan Han. Kemudian pergi ke ibu kota Luoyang menjadi pejabat dan menjadi politisi serta jenderal ulung akhir Dinasti Han Timur.',
		introduction_cn = '曹操出身官宦世家，汉室后裔。后入洛阳为官，成为东汉末杰出政治军事家。',
		introduction_kr = '조조는 관료 가문 출신으로 한실의 후예이다. 이후 낙양에 출사해 동한 말기의 뛰어난 정치가이자 군사 지도자가 되었다.',
		location = 'God of Space',
		location_vn = 'God of Space',
		location_en = 'God of Space',
		location_kr = '공간의 신',
		location_th = 'เทพแห่งอวกาศ',
		location_idn = 'Dewa Ruang',
		location_cn = '空间之神'
	},
	[2391] = {
		id = 2391,
		name = 'Huyền Uyên Lữ Bố',
		name_vn = 'Huyền Uyên Lữ Bố',
		name_en = 'Xuan Yuan Lu Bu',
		name_th = 'เหวินหยวนหลู่ปู้',
		name_idn = 'Xuan Yuan Lu Bu',
		name_cn = '玄渊吕布',
		name_kr = '현원 여포',
		cardMarkID = 2391,
		atkType = {7},
		zawakeID = 2391,
		zawakeFragID = 52391,
		gender = 0,
		chaRecom = {24, 23, 14, 16},
		nValueRnd = 3,
		specValue = {150, 90, 100, 120, 100, 120, 680},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {621, 622, 623, 604, 605},
		levelExpID = 4,
		unitID = 2391,
		skinSkillMap = {[125] = {223911, 223912, 223913, 223916, 61151, 61152}, __size = 1},
		innateSkillID = 23913,
		skillList = {23911, 23912, 23913, 23916, 61151, 61152},
		starSkillSeqID = 2391,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 22391,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 2391,
		abilitySeqID = 7,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {23911, 23912, 23913, 23914},
		introduction = 'Lữ Bố, đệ nhất mãnh tướng thời Tam Quốc. Nổi danh dũng mãnh vô song, được xưng tụng là Phi Tướng, có câu: Ngựa có Xích Thố, người có Lữ Bố.',
		introduction_vn = 'Lữ Bố, đệ nhất mãnh tướng thời Tam Quốc. Nổi danh dũng mãnh vô song, được xưng tụng là Phi Tướng, có câu: Ngựa có Xích Thố, người có Lữ Bố.',
		introduction_en = 'Lü Bu, the number one fierce general of the Three Kingdoms. Famous for unmatched bravery, known as the Flying General, with the saying: Horses have Red Hare, people have Lü Bu.',
		introduction_th = 'ลู่ปู๋ ขุนพลผู้เก่งที่สุดในยุคสามก๊ก มีชื่อเสียงในความกล้าหาญไม่มีใครเทียบได้ ได้รับการขนานนามว่านายพลบิน มีคำกล่าวว่า “ม้ามีซือโถว คนมีลู่ปู๋”',
		introduction_idn = 'Lü Bu, jenderal terkuat zaman Tiga Kerajaan. Terkenal dengan keberanian tiada tara, dijuluki Jenderal Terbang, dengan pepatah: Kuda punya Chitu, manusia punya Lü Bu.',
		introduction_cn = '吕布，三国第一猛将，勇猛无双，被称飞将，有“赤兔马，吕布人”之说。',
		introduction_kr = '여포는 삼국시대 일등 맹장으로 무쌍한 용맹으로 비장이라 불리며, 말은 적토마, 사람은 여포라 했다.',
		location = 'Primal Awaken & Haunt',
		location_vn = 'Primal Awaken & Haunt',
		location_en = 'Primal Awakening & Haunt',
		location_kr = '원초적 각성 & 유령',
		location_th = 'การตื่นรู้ดั้งเดิม & การหลอกหลอน',
		location_idn = 'Kebangkitan Primal & Menghantui',
		location_cn = '原始觉醒&缠绕'
	},
	[2401] = {
		id = 2401,
		name = 'Hoa Đà',
		name_vn = 'Hoa Đà',
		name_en = 'Hua Tuo',
		name_th = 'ฮว๋าดา',
		name_idn = 'Hua Tuo',
		name_cn = '华佗',
		name_kr = '화타',
		cardMarkID = 2401,
		atkType = {8},
		zawakeID = 2401,
		zawakeFragID = 52401,
		gender = 2,
		chaRecom = {24, 23, 14, 16},
		nValueRnd = 2,
		specValue = {120, 85, 70, 120, 75, 130, 600},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {606, 607, 608, 602, 603},
		levelExpID = 3,
		unitID = 2401,
		innateSkillID = 24013,
		skillList = {24011, 24012, 24013, 24016, 61151, 61152},
		starSkillSeqID = 2401,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 22401,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 2401,
		abilitySeqID = 5,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {24011, 24012, 24013, 24014},
		introduction = 'Hoa Đà tinh thông y thuật, là một thầy thuốc nổi tiếng. Ông được xưng tụng như một Thần y nổi tiếng.',
		introduction_vn = 'Hoa Đà tinh thông y thuật, là một thầy thuốc nổi tiếng. Ông được xưng tụng như một Thần y nổi tiếng.',
		introduction_en = 'Hua Tuo was a renowned physician skilled in medicine, celebrated as a divine healer.',
		introduction_th = 'หัวต้า เชี่ยวชาญแพทย์ศาสตร์ เป็นหมอผู้มีชื่อเสียง ได้รับการยกย่องว่าเป็นเทพหมอ',
		introduction_idn = 'Hua Tuo menguasai ilmu kedokteran, seorang tabib terkenal. Ia dijuluki Dewa Pengobatan.',
		introduction_cn = '华佗，精通医术，著名医生。被尊为神医。',
		introduction_kr = '화타는 의학에 정통한 명의로 신의라 불렸다.',
		location = 'Joyful Dreams & Crescent Guard',
		location_vn = 'Joyful Dreams & Crescent Guard',
		location_en = 'Joyful Dreams & Crescent Guard',
		location_kr = '즐거운 꿈 & 초승달 수호',
		location_th = 'ความฝันรื่นรมย์ & การป้องกันพระจันทร์เสี้ยว',
		location_idn = 'Mimpi Bahagia & Penjaga Bulan Sabit',
		location_cn = '快乐梦境&新月守护'
	},
	[2431] = {
		id = 2431,
		name = 'Văn Đế Tào Phi',
		name_vn = 'Văn Đế Tào Phi',
		name_en = 'Wen Di Cao Pi',
		name_th = 'เหวินตี้เตียวผี',
		name_idn = 'Wen Di Cao Pi',
		name_cn = '文帝曹丕',
		name_kr = '문제 조비',
		cardMarkID = 2431,
		atkType = {8},
		zawakeID = 2431,
		zawakeFragID = 52431,
		gender = 0,
		chaRecom = {6, 7, 18, 19},
		nValueRnd = 2,
		specValue = {70, 125, 90, 90, 135, 90, 600},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {606, 607, 608, 604, 605},
		levelExpID = 4,
		unitID = 2431,
		skinSkillMap = {[121] = {924311, 924312, 924313, 924316, 61101, 61102}, __size = 1},
		innateSkillID = 24313,
		skillList = {24311, 24312, 24313, 24316, 61101, 61102},
		starSkillSeqID = 2431,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 22431,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 2431,
		abilitySeqID = 8,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {24311, 24312, 24313, 24314},
		introduction = 'Tào Phi, con thứ của Tào Tháo, là Hoàng đế đầu tiên của Tào Ngụy, văn võ song toàn và có nhiều đóng góp trong việc củng cố và phát triển đất nước. ',
		introduction_vn = 'Tào Phi, con thứ của Tào Tháo, là Hoàng đế đầu tiên của Tào Ngụy, văn võ song toàn và có nhiều đóng góp trong việc củng cố và phát triển đất nước. ',
		introduction_en = 'Cao Pi, the second son of Cao Cao, was the first emperor of Cao Wei, accomplished in both civil and military affairs, and contributed greatly to strengthening and developing the state.',
		introduction_th = 'เต้าฝี่ บุตรชายคนที่สองของเต้าตง เป็นจักรพรรดิองค์แรกของเต้าหวี่ มีความรู้ทั้งด้านวรรณกรรมและยุทธศาสตร์ มีบทบาทสำคัญในการเสริมสร้างและพัฒนาประเทศ',
		introduction_idn = 'Cao Pi, putra kedua Cao Cao, adalah kaisar pertama Wei, mahir dalam ilmu dan seni bela diri, memberikan banyak kontribusi dalam memperkuat dan mengembangkan negara.',
		introduction_cn = '曹丕，曹操次子，曹魏第一任皇帝，文武双全，为国家巩固发展贡献良多。',
		introduction_kr = '조비는 조조의 막내 아들로 조위의 초대 황제이며, 문무겸전하고 국가 발전에 크게 기여했다.',
		location = 'Nightmare Control & Nightmare Drain',
		location_vn = 'Nightmare Control & Nightmare Drain',
		location_en = 'Nightmare Control & Nightmare Drain',
		location_kr = '악몽 제어 & 악몽 흡수',
		location_th = 'การควบคุมฝันร้าย & ดูดฝันร้าย',
		location_idn = 'Kontrol Mimpi Buruk & Penguras Mimpi Buruk',
		location_cn = '噩梦控制&噩梦吸取'
	},
	[2461] = {
		id = 2461,
		name = 'Chiêu Hành Lục Tốn',
		name_vn = 'Chiêu Hành Lục Tốn',
		name_en = 'Chiêu Hành Lục Tốn',
		name_th = 'Chiêu Hành Lục Tốn',
		name_idn = 'Chiêu Hành Lục Tốn',
		name_cn = 'Chiêu Hành Lục Tốn',
		name_kr = 'Chiêu Hành Lục Tốn',
		cardMarkID = 2461,
		atkType = {8},
		zawakeID = 2461,
		zawakeFragID = 52461,
		gender = 0,
		chaRecom = {6, 7, 18, 19},
		nValueRnd = 3,
		specValue = {100, 100, 100, 100, 100, 100, 600},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {606, 607, 608, 604, 605},
		levelExpID = 4,
		unitID = 2461,
		innateSkillID = 24613,
		skillList = {24611, 24612, 24613, 24616, 61101, 61102},
		starSkillSeqID = 2461,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 22461,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 2461,
		abilitySeqID = 7,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {24611, 24612, 24613, 24614},
		townSkill = 1103,
		energy = 20,
		introduction = 'Legend has it that a trainer who conquers Victini will never fail. It can generate endless energy in its body, and can pass this energy to other Pokémon through touch.',
		introduction_vn = 'Legend has it that a trainer who conquers Victini will never fail. It can generate endless energy in its body, and can pass this energy to other Pokémon through touch.',
		introduction_en = 'Legend has it that a trainer who conquers Victini will never fail. It can generate endless energy in its body, and can pass this energy to other Pokémon through touch.',
		introduction_th = 'Legend has it that a trainer who conquers Victini will never fail. It can generate endless energy in its body, and can pass this energy to other Pokémon through touch.',
		introduction_idn = 'Legend has it that a trainer who conquers Victini will never fail. It can generate endless energy in its body, and can pass this energy to other Pokémon through touch.',
		introduction_cn = 'Legend has it that a trainer who conquers Victini will never fail. It can generate endless energy in its body, and can pass this energy to other Pokémon through touch.',
		introduction_kr = 'Legend has it that a trainer who conquers Victini will never fail. It can generate endless energy in its body, and can pass this energy to other Pokémon through touch.'
	},
	[2481] = {
		id = 2481,
		name = 'Trương Phi',
		name_vn = 'Trương Phi',
		name_en = 'Zhang Fei',
		name_th = 'จางฟี',
		name_idn = 'Zhang Fei',
		name_cn = '张飞',
		name_kr = '장비',
		cardMarkID = 2481,
		atkType = {7},
		zawakeID = 2481,
		zawakeFragID = 52481,
		chaRecom = {11, 12, 15, 16},
		specValue = {65, 45, 63, 45, 45, 45, 308},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {618, 619, 620, 602, 603},
		levelExpID = 2,
		unitID = 2481,
		innateSkillID = 24813,
		skillList = {24811, 24812, 24813, 24816, 61101, 61102},
		starSkillSeqID = 2481,
		fragID = 22481,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 2481,
		abilitySeqID = 16,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {24811, 24812, 24813},
		introduction = 'Trương Phi là danh tướng nhà Thục Hán, cùng Lưu Bị và Quan Vũ kết bái huynh đệ ở vườn đào, là người dũng mãnh phi phàm, tính nóng như lửa.',
		introduction_vn = 'Trương Phi là danh tướng nhà Thục Hán, cùng Lưu Bị và Quan Vũ kết bái huynh đệ ở vườn đào, là người dũng mãnh phi phàm, tính nóng như lửa.',
		introduction_en = 'Zhang Fei was a famous general of Shu Han, swore brotherhood with Liu Bei and Guan Yu in the Peach Garden, known for extraordinary bravery and fiery temper.',
		introduction_th = 'จางเฟย ขุนพลรัฐฉู่ฮั่น ร่วมสาบานเป็นพี่น้องกับหลิวเป่ยและกวนอู มีความกล้าหาญและอารมณ์ร้อนแรง',
		introduction_idn = 'Zhang Fei adalah jenderal Shu Han, bersaudara dengan Liu Bei dan Guan Yu di Kebun Persik, dikenal pemberani luar biasa dan temperamen panas seperti api.',
		introduction_cn = '张飞，蜀汉名将，与刘备、关羽桃园结义，勇猛异常，性格暴躁。',
		introduction_kr = '장비는 촉한의 명장으로 유비, 관우와 도원결의를 맺었으며, 용맹하고 성격이 급했다.',
		location = 'Flame Shield Break',
		location_vn = 'Flame Shield Break',
		location_en = 'Flame Shield Break',
		location_kr = '화염 방패 파괴',
		location_th = 'ทำลายโล่เปลวไฟ',
		location_idn = 'Pecah Perisai Api',
		location_cn = '火焰盾破'
	},
	[2591] = {
		id = 2591,
		name = 'Hoàng Trung',
		name_vn = 'Hoàng Trung',
		name_en = 'Huang Zhong',
		name_th = 'หวงจง',
		name_idn = 'Huang Zhong',
		name_cn = '黄忠',
		name_kr = '황중',
		cardMarkID = 2591,
		atkType = {7},
		zawakeID = 2591,
		zawakeFragID = 52591,
		chaRecom = {15, 16, 11, 12},
		specValue = {55, 15, 75, 85, 25, 25, 280},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {615, 616, 617, 602, 603},
		levelExpID = 2,
		unitID = 2591,
		innateSkillID = 25913,
		skillList = {25911, 25912, 25913, 25916, 61151, 61152},
		starSkillSeqID = 2591,
		fragID = 22591,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 2591,
		abilitySeqID = 17,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {25911, 25912, 25913},
		introduction = 'Hoàng Trung, một trong Ngũ Hổ Thượng Tướng, tuy đã già nhưng sức khỏe và tài năng chẳng thua kém ai. Trước khi đầu quân cho nhà Thục của Lưu Bị, ông theo Lưu Biểu',
		introduction_vn = 'Hoàng Trung, một trong Ngũ Hổ Thượng Tướng, tuy đã già nhưng sức khỏe và tài năng chẳng thua kém ai. Trước khi đầu quân cho nhà Thục của Lưu Bị, ông theo Lưu Biểu',
		introduction_en = 'Huang Zhong, one of the Five Tiger Generals, though old, was vigorous and talented. Before joining Liu Bei’s Shu, he served Liu Biao.',
		introduction_th = 'หวงจง หนึ่งในห้าขุนพลผู้กล้าหาญ แม้แก่แล้วแต่ยังแข็งแรงและมีความสามารถสูง ก่อนเข้ารับใช้รัฐฉู่ภายใต้หลิวเปียวยังเคยรับใช้หลิวเปียว',
		introduction_idn = 'Huang Zhong, salah satu dari Lima Jenderal Harimau, meskipun sudah tua, kesehatan dan bakatnya tidak kalah dengan siapa pun. Sebelum bergabung dengan Shu Liu Bei, ia mengikuti Liu Biao.',
		introduction_cn = '黄忠，五虎上将之一，虽年长但健康与才能不减当年。投靠刘备前曾随刘表。',
		introduction_kr = '황중은 오호상장 중 하나로 나이가 많지만 건강과 재능이 뛰어났다. 촉한 유비에 귀순하기 전에는 유표를 섬겼다.',
		location = 'Damage taken by front line: Stone hardened',
		location_vn = 'Damage taken by front line: Stone hardened',
		location_en = 'Front Line Damage Taken: Stone Hardened',
		location_kr = '전열 피해 흡수: 돌처럼 단단해짐',
		location_th = 'ความเสียหายที่ได้รับโดยแนวหน้า: หินแข็งแกร่ง',
		location_idn = 'Kerusakan yang Diterima Barisan Depan: Mengeras seperti Batu',
		location_cn = '前排所受伤害：石化坚硬'
	},
	[2611] = {
		id = 2611,
		name = 'Trương Liêu',
		name_vn = 'Trương Liêu',
		name_en = 'Zhang Liao',
		name_th = 'จางเหลียว',
		name_idn = 'Zhang Liao',
		name_cn = '张辽',
		name_kr = '장료',
		cardMarkID = 2611,
		atkType = {7},
		zawakeID = 2611,
		zawakeFragID = 52611,
		chaRecom = {7, 8, 9, 12},
		specValue = {60, 68, 85, 40, 30, 45, 328},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 2,
		unitID = 2611,
		skinSkillMap = {[119] = {26121, 26122, 26123, 26126, 61151, 61152}, __size = 1},
		innateSkillID = 26113,
		skillList = {26111, 26112, 26113, 26116, 61151, 61152},
		starSkillSeqID = 2611,
		fragID = 22611,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 2611,
		abilitySeqID = 16,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {26111, 26112, 26113},
		introduction = 'Trương Liêu, một trong Ngũ Hổ Tướng của Tào Ngụy, nổi tiếng với trận Hợp Phì. ',
		introduction_vn = 'Trương Liêu, một trong Ngũ Hổ Tướng của Tào Ngụy, nổi tiếng với trận Hợp Phì. ',
		introduction_en = 'Zhang Liao, one of the Five Tiger Generals of Cao Wei, famous for the Battle of Hefei.',
		introduction_th = 'จางเหลียว หนึ่งในห้าขุนพลผู้กล้าหาญของเต้าหวี่ มีชื่อเสียงจากยุทธการเหอผี',
		introduction_idn = 'Zhang Liao, salah satu dari Lima Jenderal Harimau Cao Wei, terkenal dalam Pertempuran Hefei.',
		introduction_cn = '张辽，曹魏五虎将之一，以合肥之战著称。',
		introduction_kr = '장료는 조위의 오호장군 중 하나로 합비 전투로 유명하다.',
		location = 'P.DEF Tank Killer',
		location_vn = 'P.DEF Tank Killer',
		location_en = 'P.DEF Tank Killer',
		location_kr = '물리 방어 탱크 킬러',
		location_th = 'ฆ่าแท็งค์ป้องกันกายภาพ',
		location_idn = 'Pembunuh Tank P.DEF',
		location_cn = '物防坦克克星'
	},
	[2631] = {
		id = 2631,
		name = 'Ngụy Diên',
		name_vn = 'Ngụy Diên',
		name_en = 'Wei Yan',
		name_th = 'งุยเยียน',
		name_idn = 'Wei Yan',
		name_cn = '魏延',
		name_kr = '위연',
		cardMarkID = 2631,
		atkType = {7},
		zawakeID = 2631,
		zawakeFragID = 52631,
		chaRecom = {11, 12, 15, 16},
		specValue = {75, 35, 80, 55, 25, 35, 305},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {612, 613, 614, 602, 603},
		levelExpID = 2,
		unitID = 2631,
		innateSkillID = 26313,
		skillList = {26311, 26312, 26313, 26316, 61151, 61152},
		starSkillSeqID = 2631,
		fragID = 22631,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 2631,
		abilitySeqID = 18,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {26311, 26312, 26313},
		introduction = 'Ngụy Diên là đại tướng nhà Thục Hán. Sau khi Gia Cát Lượng qua đời vì tranh đoạt binh quyền với Dương Nghi nên bị Mã Đại xử trảm.',
		introduction_vn = 'Ngụy Diên là đại tướng nhà Thục Hán. Sau khi Gia Cát Lượng qua đời vì tranh đoạt binh quyền với Dương Nghi nên bị Mã Đại xử trảm.',
		introduction_en = 'Wei Yan was a general of Shu Han. After Zhuge Liang’s death, he clashed over military command with Yang Yi and was executed by Ma Dai.',
		introduction_th = 'เหวินเยียน ขุนพลรัฐฉู่ฮั่น หลังเจียฉาเหลียงเสียชีวิตและแย่งชิงอำนาจกับหยางอี้ ถูกม้าไต้ประหาร',
		introduction_idn = 'Wei Yan adalah jenderal Shu Han. Setelah kematian Zhuge Liang, karena persaingan kekuasaan dengan Yang Yi, ia dieksekusi oleh Ma Dai.',
		introduction_cn = '魏延，蜀汉大将。诸葛亮去世后因与杨仪争权被马岱处死。',
		introduction_kr = '위연은 촉한의 대장으로, 제갈량 사후 양의와 군권 다툼 끝에 마대에게 처형당했다.',
		location = 'Stun damage increase',
		location_vn = 'Stun damage increase',
		location_en = 'Stun Damage Increase',
		location_kr = '기절 피해 증가',
		location_th = 'เพิ่มความเสียหายจากการทำให้ช็อก',
		location_idn = 'Peningkatan Kerusakan Stun',
		location_cn = '眩晕伤害提升'
	},
	[2721] = {
		id = 2721,
		name = 'Từ Hoảng',
		name_vn = 'Từ Hoảng',
		name_en = 'Tu Huang',
		name_th = 'จื่อหวง',
		name_idn = 'Ci Huang',
		name_cn = '庞恇',
		name_kr = '자황',
		cardMarkID = 2721,
		atkType = {7},
		zawakeID = 2721,
		zawakeFragID = 52721,
		chaRecom = {7, 8, 9, 12},
		specValue = {50, 65, 72, 35, 35, 35, 292},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 3,
		unitID = 2721,
		innateSkillID = 27213,
		skillList = {27211, 27212, 27213, 27216, 61101, 61102},
		starSkillSeqID = 2721,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 22721,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 2721,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {27211, 27212, 27213, 27214},
		introduction = 'Từ Hoảng là vị tướng xuất sắc nhất của nhà Tào Ngụy. Lập được vô số chiến công trong sự nghiệp của mình, nổi bật nhất là chiến thắng Quan Vũ trong trận chiến Phàn Thành. ',
		introduction_vn = 'Từ Hoảng là vị tướng xuất sắc nhất của nhà Tào Ngụy. Lập được vô số chiến công trong sự nghiệp của mình, nổi bật nhất là chiến thắng Quan Vũ trong trận chiến Phàn Thành. ',
		introduction_en = 'Xu Huang was the most outstanding general of Cao Wei. He achieved countless victories, notably defeating Guan Yu at the Battle of Fan Castle.',
		introduction_th = 'ซื่อหวง เป็นแม่ทัพที่เก่งที่สุดของเต้าหวี่ สร้างผลงานมากมาย มีชัยชนะที่โดดเด่นคือการปราบกวนอูในยุทธการฟานเฉิง',
		introduction_idn = 'Xu Huang adalah jenderal terbaik Cao Wei. Berhasil meraih banyak kemenangan, yang paling menonjol adalah mengalahkan Guan Yu dalam Pertempuran Fan Cheng.',
		introduction_cn = '徐晃，曹魏最杰出将领。战绩斐然，尤以樊城战役中战胜关羽著称。',
		introduction_kr = '자황은 조위 최고의 장수로 무수한 전공을 세웠으며, 특히 판성 전투에서 관우를 격파했다.',
		location = 'True damage burst & Single-target blind',
		location_vn = 'True damage burst & Single-target blind',
		location_en = 'True Damage Burst & Single-target Blind',
		location_kr = '진짜 피해 폭발 & 단일 대상 실명',
		location_th = 'ความเสียหายแท้จริงระเบิด & ทำให้เป้าหมายเดียวตาบอด',
		location_idn = 'Ledakan Kerusakan Sebenarnya & Membutakan Sasaran Tunggal',
		location_cn = '真实伤害爆发&单体致盲'
	},
	[2961] = {
		id = 2961,
		name = 'Trương Kỳ Anh',
		name_vn = 'Trương Kỳ Anh',
		name_en = 'Zhang Qi\'ang',
		name_th = 'จางขี่อัน',
		name_idn = 'Zhang Qiying',
		name_cn = '张期英',
		name_kr = '장기영',
		cardMarkID = 2961,
		atkType = {7},
		zawakeID = 2961,
		zawakeFragID = 52961,
		gender = 0,
		chaRecom = {24, 23, 14, 16},
		specValue = {40, 30, 55, 70, 45, 60, 300},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {615, 616, 617, 602, 603},
		levelExpID = 2,
		unitID = 2961,
		innateSkillID = 29613,
		skillList = {29611, 29612, 29613, 29616, 61151, 61152},
		starSkillSeqID = 2961,
		fragID = 22961,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 2961,
		abilitySeqID = 17,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {29611, 29612, 29613},
		introduction = 'Trương Kỳ Anh là con gái củaTrương Lỗ, một quân phiệt cát cứ ở Hán Trung vào cuối thời Đông Hán. ',
		introduction_vn = 'Trương Kỳ Anh là con gái củaTrương Lỗ, một quân phiệt cát cứ ở Hán Trung vào cuối thời Đông Hán. ',
		introduction_en = 'Zhang Qiying was the daughter of Zhang Lu, a warlord ruling Hanzhong at the end of the Eastern Han.',
		introduction_th = 'จางฉีอิง ลูกสาวของจางลู่ ผู้ปกครองแคว้นฮั่นจงในปลายยุคตงฮั่น',
		introduction_idn = 'Zhang Qiying adalah putri Zhang Lu, seorang penguasa militer di Han Zhong pada akhir Dinasti Han Timur.',
		introduction_cn = '张星彩，张鲁之女，东汉末汉中割据军阀。',
		introduction_kr = '장기영은 동한 말기 한중의 군벌 장로의 딸이다.',
		location = 'Buff Support: Rage recovery for back line',
		location_vn = 'Buff Support: Rage recovery for back line',
		location_en = 'Buff Support: Rage Recovery for Back Line',
		location_kr = '버프 지원: 후열 분노 회복',
		location_th = 'สนับสนุนบัฟ: ฟื้นฟูความโกรธสำหรับแนวหลัง',
		location_idn = 'Dukungan Buff: Pemulihan Amarah untuk Barisan Belakang',
		location_cn = '增益辅助：后排怒气回复'
	},
	[2991] = {
		id = 2991,
		name = 'Tôn Thượng Hương',
		name_vn = 'Tôn Thượng Hương',
		name_en = 'Sun Shangxiang',
		name_th = 'ซุนเฉิงเสียง',
		name_idn = 'Sun Shangxiang',
		name_cn = '孙尚香',
		name_kr = '손상향',
		cardMarkID = 2991,
		atkType = {8},
		zawakeFragID = 52991,
		chaRecom = {18, 20, 15, 14},
		specValue = {50, 20, 30, 55, 65, 55, 275},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 3,
		unitID = 2991,
		skinSkillMap = {[115] = {29921, 29922, 29923, 29926, 61101, 61102}, __size = 1},
		innateSkillID = 29913,
		skillList = {29911, 29912, 29913, 29916, 61101, 61102},
		starSkillSeqID = 2991,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 22991,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 2991,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {29911, 29912, 29913, 29914},
		introduction = 'Con gái Tôn Kiên và là em gái của Tôn Sách và Tôn Quyền, sau lấy Lưu Bị làm phu quân',
		introduction_vn = 'Con gái Tôn Kiên và là em gái của Tôn Sách và Tôn Quyền, sau lấy Lưu Bị làm phu quân',
		introduction_en = 'Daughter of Sun Jian and younger sister of Sun Ce and Sun Quan, later married Liu Bei.',
		introduction_th = 'ลูกสาวซุ่นเกียน และน้องสาวซุ่นซาและซุ่นชวน ต่อมาขึ้นเป็นภรรยาของหลิวเป่ย',
		introduction_idn = 'Putri Sun Jian dan adik Sun Ce serta Sun Quan, kemudian menikah dengan Liu Bei.',
		introduction_cn = '孙坚女儿，孙策、孙权妹妹，后嫁刘备。',
		introduction_kr = '손견의 딸이자 손책, 손권의 여동생으로, 후에 유비와 결혼하였다.',
		location = 'Will-O-Wisp Control & Reignite Vengeance',
		location_vn = 'Will-O-Wisp Control & Reignite Vengeance',
		location_en = 'Will-O-Wisp Control & Reignite Vengeance',
		location_kr = '위습 제어 & 복수 재점화',
		location_th = 'ควบคุมวิญญาณเพลิง & จุดไฟแค้นใหม่',
		location_idn = 'Kontrol Will-O-Wisp & Menyalakan Balas Dendam Kembali',
		location_cn = '魂火控制&重燃复仇'
	},
	[3001] = {
		id = 3001,
		name = 'Nhạc Tiến',
		name_vn = 'Nhạc Tiến',
		name_en = 'Yue Jin',
		name_th = 'เหยี่ยนเจี้ยน',
		name_idn = 'Yue Jin',
		name_cn = '乐进',
		name_kr = '악진',
		cardMarkID = 3001,
		atkType = {7},
		zawakeID = 3001,
		zawakeFragID = 53001,
		chaRecom = {11, 12, 15, 16},
		specValue = {46, 57, 87, 60, 30, 40, 320},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {618, 619, 620, 602, 603},
		levelExpID = 3,
		unitID = 3001,
		skinSkillMap = {[103] = {30011, 30012, 30013, 30016, 61101, 61102}, __size = 1},
		innateSkillID = 30013,
		skillList = {30021, 30022, 30023, 30026, 61101, 61102},
		starSkillSeqID = 3001,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 23001,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 3001,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {30011, 30012, 30013, 30014},
		introduction = 'Nhạc Tiến là võ tướng dưới quyền Tào Tháo cuối thời Đông Hán, tham gia chinh chiến nhiều trận đánh quan trọng của quân Tào ',
		introduction_vn = 'Nhạc Tiến là võ tướng dưới quyền Tào Tháo cuối thời Đông Hán, tham gia chinh chiến nhiều trận đánh quan trọng của quân Tào ',
		introduction_en = 'Yue Jin was a martial general under Cao Cao at the end of the Eastern Han, participating in many important battles.',
		introduction_th = 'เหนี่ยวจิ้น ขุนพลใต้บังคับบัญชาเต้าตงในปลายยุคตงฮั่น เข้าร่วมรบในหลายสมรภูมิสำคัญของกองทัพเต้าตง',
		introduction_idn = 'Yue Jin adalah jenderal di bawah Cao Cao pada akhir Dinasti Han Timur, mengikuti banyak pertempuran penting tentara Cao.',
		introduction_cn = '乐进，曹操麾下武将，东汉末多次参与重要战役。',
		introduction_kr = '악진은 동한 말기 조조 휘하의 무장으로 여러 중요한 전투에 참가하였다.',
		location = 'Multiple Pursuits & Increased Damage',
		location_vn = 'Multiple Pursuits & Increased Damage',
		location_en = 'Multiple Pursuits & Increased Damage',
		location_kr = '다중 추격 & 피해 증가',
		location_th = 'การไล่ล่าหลายครั้ง & เพิ่มความเสียหาย',
		location_idn = 'Pengejaran Berganda & Peningkatan Kerusakan',
		location_cn = '多重追击&提升伤害'
	},
	[3011] = {
		id = 3011,
		name = 'Chu Thái',
		name_vn = 'Chu Thái',
		name_en = 'Zhu Tai',
		name_th = 'จูไท่',
		name_idn = 'Zhu Tai',
		name_cn = '朱泰',
		name_kr = '주태',
		cardMarkID = 3011,
		atkType = {7},
		zawakeID = 3011,
		zawakeFragID = 53011,
		chaRecom = {10, 11, 13, 9},
		specValue = {55, 40, 70, 40, 60, 40, 305},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {615, 616, 617, 602, 603},
		levelExpID = 2,
		unitID = 3011,
		innateSkillID = 30113,
		skillList = {30111, 30112, 30113, 30116, 61101, 61102},
		starSkillSeqID = 3011,
		fragID = 23011,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 3011,
		abilitySeqID = 16,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {30111, 30112, 30113},
		introduction = 'Chu Thái là công thần khai quốc Đông Ngô, nhiều lần lấy thân bảo vệ Tôn Quyền.',
		introduction_vn = 'Chu Thái là công thần khai quốc Đông Ngô, nhiều lần lấy thân bảo vệ Tôn Quyền.',
		introduction_en = 'Chu Tai was a founding hero of Eastern Wu, repeatedly risking himself to protect Sun Quan.',
		introduction_th = 'จูไท ขุนพลผู้ร่วมก่อตั้งตงงู หลายครั้งเสียสละชีวิตปกป้องซุ่นชวน',
		introduction_idn = 'Zhu Tai adalah tokoh pendiri Wu Timur, berkali-kali mengorbankan diri untuk melindungi Sun Quan.',
		introduction_cn = '周泰，东吴开国功臣，多次以身护孙权。',
		introduction_kr = '주태는 동오 개국 공신으로 여러 차례 목숨을 걸고 손권을 보호하였다.',
		location = 'Ice-type damage: increase damage quickly',
		location_vn = 'Ice-type damage: increase damage quickly',
		location_en = 'Ice-type Damage: Rapid Damage Increase',
		location_kr = '얼음 속성 피해: 빠르게 피해 증가',
		location_th = 'ความเสียหายประเภทน้ำแข็ง: เพิ่มความเสียหายอย่างรวดเร็ว',
		location_idn = 'Kerusakan Tipe Es: Meningkatkan Kerusakan dengan Cepat',
		location_cn = '冰属性伤害：快速提升伤害'
	},
	[3151] = {
		id = 3151,
		name = 'Bàng Thống',
		name_vn = 'Bàng Thống',
		name_en = 'Pang Tong',
		name_th = 'ปั้งทง',
		name_idn = 'Pang Tong',
		name_cn = '庞统',
		name_kr = '방통',
		cardMarkID = 3151,
		atkType = {8},
		zawakeID = 3151,
		zawakeFragID = 53151,
		chaRecom = {6, 18, 19, 21},
		specValue = {55, 60, 85, 55, 50, 55, 360},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 3,
		unitID = 3151,
		innateSkillID = 31513,
		skillList = {31511, 31512, 31513, 31516, 61101, 61102},
		starSkillSeqID = 3151,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 23151,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 3151,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {31511, 31512, 31513, 31514},
		introduction = 'Bàng Thống có tài năng sánh ngang với Ngọa Long Khổng Minh, góp phần quan trọng trong chiến thắng Xích Bích của liên minh Tôn Lưu trước đại quân Tào Ngụy',
		introduction_vn = 'Bàng Thống có tài năng sánh ngang với Ngọa Long Khổng Minh, góp phần quan trọng trong chiến thắng Xích Bích của liên minh Tôn Lưu trước đại quân Tào Ngụy',
		introduction_en = 'Pang Tong was a strategist whose talent rivaled Zhuge Liang’s, playing a key role in the allied victory at Red Cliffs against Cao Wei.',
		introduction_th = 'ปั้งถง มีความสามารถเทียบเท่าขงเบ้ง มีบทบาทสำคัญในชัยชนะยุทธการฉีผีของพันธมิตรซุ่น-หลิวต่อกองทัพเต้าหวี่',
		introduction_idn = 'Pang Tong adalah penasihat yang bakatnya setara dengan Zhuge Liang, berperan penting dalam kemenangan aliansi Sun-Liu di Pertempuran Chibi melawan tentara Cao Wei.',
		introduction_cn = '庞统，才华堪比卧龙诸葛亮，助孙刘联盟赤壁大胜曹军功臣。',
		introduction_kr = '방통은 제갈량과 어깨를 나란히 하는 재능을 지녔으며, 손유와 유비 연합의 적벽 대승에 중요한 역할을 했다.',
		location = 'Insect Invite & Rear-line Strike',
		location_vn = 'Insect Invite & Rear-line Strike',
		location_en = 'Insect Summon & Rear-line Strike',
		location_kr = '곤충 소환 & 후열 타격',
		location_th = 'เชิญแมลง & โจมตีแนวหลัง',
		location_idn = 'Undangan Serangga & Serangan Barisan Belakang',
		location_cn = '虫群诱导&后排打击'
	},
	[3161] = {
		id = 3161,
		name = 'Lăng Thống',
		name_vn = 'Lăng Thống',
		name_en = 'Ling Tong',
		name_th = 'หลิ่งทง',
		name_idn = 'Ling Tong',
		name_cn = '凌统',
		name_kr = '능통',
		cardMarkID = 3161,
		atkType = {7},
		zawakeFragID = 53161,
		gender = 0,
		chaRecom = {6, 8, 14, 16},
		nValueRnd = 2,
		specValue = {91, 108, 90, 129, 90, 72, 580},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {615, 616, 617, 602, 603},
		levelExpID = 3,
		unitID = 3161,
		innateSkillID = 31613,
		skillList = {31611, 31612, 31613, 31616, 61101, 61102},
		starSkillSeqID = 3161,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 23161,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 3161,
		abilitySeqID = 2,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {31611, 31612, 31613, 31614},
		introduction = 'Lăng Thống nổi danh từ nhỏ. Từng theo Tôn Quyền chinh chiến, lập nhiều chiến công. Tuy có thù với Cam Ninh, về sau đã giảng hòa.',
		introduction_vn = 'Lăng Thống nổi danh từ nhỏ. Từng theo Tôn Quyền chinh chiến, lập nhiều chiến công. Tuy có thù với Cam Ninh, về sau đã giảng hòa.',
		introduction_en = 'Ling Tong was famous from a young age. He fought alongside Sun Quan, achieving many victories. Though he had a feud with Gan Ning, they later reconciled.',
		introduction_th = 'หลั่งถงมีชื่อเสียงตั้งแต่เด็ก เคยร่วมรบกับซุ่นชวน สร้างผลงานมากมาย แม้มีศัตรูกับกานหนิงแต่ท้ายที่สุดก็คืนดีกัน',
		introduction_idn = 'Ling Tong terkenal sejak kecil. Pernah ikut berperang bersama Sun Quan, meraih banyak prestasi. Meskipun berseteru dengan Gan Ning, akhirnya berdamai.',
		introduction_cn = '凌统，自幼名声显赫。随孙权征战立功，曾与甘宁有仇，后释怀和解。',
		introduction_kr = '능통은 어릴 적부터 명성을 떨쳤으며, 손권을 따라 여러 전투에 참가해 공을 세웠다. 감녕과 원한이 있었으나 후에 화해하였다.'
	},
	[3181] = {
		id = 3181,
		name = 'Mã Lương',
		name_vn = 'Mã Lương',
		name_en = 'Ma Liang',
		name_th = 'ม้าเลียง',
		name_idn = 'Ma Liang',
		name_cn = '马良',
		name_kr = '마량',
		cardMarkID = 3181,
		atkType = {8},
		zawakeFragID = 53181,
		gender = 0,
		chaRecom = {6, 8, 22, 25},
		nValueRnd = 2,
		specValue = {91, 108, 90, 72, 90, 129, 580},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 3,
		unitID = 3181,
		innateSkillID = 31813,
		skillList = {31811, 31812, 31813, 31816, 61151, 61152},
		starSkillSeqID = 3181,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 23181,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 3181,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {31811, 31812, 31813, 31814},
		introduction = 'Mã Lương là quan văn nhà Thục Hán. Sau trận đại bại ở Hào Đình, Mã Lương tử trận.',
		introduction_vn = 'Mã Lương là quan văn nhà Thục Hán. Sau trận đại bại ở Hào Đình, Mã Lương tử trận.',
		introduction_en = 'Ma Liang was a civil official of Shu Han. He died in battle after the great defeat at Haoting.',
		introduction_th = 'ม้าเหลียง ขุนนางวรรณาการรัฐฉู่ฮั่น หลังพ่ายแพ้ย่อยยับที่ห่าวติง ม้าเหลียงเสียชีวิตในสนามรบ',
		introduction_idn = 'Ma Liang adalah pejabat sipil Shu Han. Setelah kekalahan besar di Haoting, ia gugur.',
		introduction_cn = '马良，蜀汉文官。遭遇皋亭之战大败，战死沙场。',
		introduction_kr = '마량은 촉한의 문관으로 호정 전투에서 전사하였다.'
	},
	[3211] = {
		id = 3211,
		name = 'Huyền Minh Tôn Sách',
		name_vn = 'Huyền Minh Tôn Sách',
		name_en = 'Mysterious Sun Ce',
		name_th = 'เหวินหมิงซุนซือ',
		name_idn = 'Xuan Ming Sun Ce',
		name_cn = '玄明孙策',
		name_kr = '현명 손책',
		cardMarkID = 3211,
		atkType = {8},
		zawakeID = 3211,
		zawakeFragID = 53211,
		gender = 0,
		chaRecom = {18, 19, 20, 21},
		nValueRnd = 3,
		specValue = {100, 90, 150, 100, 120, 120, 680},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {618, 619, 620, 604, 605},
		levelExpID = 4,
		unitID = 3211,
		innateSkillID = 32113,
		skillList = {32111, 32112, 32113, 32116, 61151, 61152},
		starSkillSeqID = 3211,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 23211,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 3211,
		abilitySeqID = 7,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {32111, 32112, 32113, 32114},
		introduction = 'Tôn Sách tính tình phóng đãng không câu nệ tiểu tiết, có kiến thức về bày binh bố trận, thống lĩnh quân Giang Đông bách chiến bách thắng, còn được gọi là Tiểu Bá Vương.',
		introduction_vn = 'Tôn Sách tính tình phóng đãng không câu nệ tiểu tiết, có kiến thức về bày binh bố trận, thống lĩnh quân Giang Đông bách chiến bách thắng, còn được gọi là Tiểu Bá Vương.',
		introduction_en = 'Sun Ce was reckless and carefree, knowledgeable in military formations, commanding Jiangdong’s forces to numerous victories, earning the title Little Overlord.',
		introduction_th = 'ซุ่นซา มีนิสัยเสเพล ไม่จู้จี้จุกจิก มีความรู้ด้านการจัดวางกองทัพ นำกองทัพเจียงตงรบชนะหลายครั้ง ได้รับสมญาว่าเสี่ยวป๋าวหวัง',
		introduction_idn = 'Sun Ce berkarakter santai dan tidak memusingkan hal kecil, memiliki pengetahuan tentang penyusunan pasukan, memimpin tentara Jiangdong menang dalam banyak pertempuran, juga dikenal sebagai Raja Muda.',
		introduction_cn = '孙策性格豪放，不拘小节，精通布阵，统领江东军屡战屡胜，被称小霸王。',
		introduction_kr = '손책은 성격이 방탕하고 사소한 일에 구애받지 않으며 병법에 해박하여 강동 군대를 이끌어 많은 승리를 거두어 소백왕이라 불렸다.',
		location = 'Purifying Fire & Blue Flare Dragon',
		location_vn = 'Purifying Fire & Blue Flare Dragon',
		location_en = 'Purifying Fire & Blue Flare Dragon',
		location_kr = '정화의 불꽃 & 청염룡',
		location_th = 'ไฟชำระล้าง & มังกรเปลวไฟน้ำเงิน',
		location_idn = 'Api Pemurnian & Naga Nyala Biru',
		location_cn = '净化之火&蓝焰龙'
	},
	[3221] = {
		id = 3221,
		name = 'Lôi Đình Triệu Vân',
		name_vn = 'Lôi Đình Triệu Vân',
		name_en = 'Thunder Roar Zhao Yun',
		name_th = 'เล่ยติงเจ้าเหวิน',
		name_idn = 'Lei Ding Zhao Yun',
		name_cn = '雷霆赵云',
		name_kr = '뇌정 조운',
		cardMarkID = 3221,
		atkType = {7},
		zawakeID = 3221,
		zawakeFragID = 53221,
		gender = 0,
		chaRecom = {11, 12, 15, 16},
		nValueRnd = 3,
		specValue = {100, 90, 150, 120, 120, 100, 680},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {618, 619, 620, 604, 605},
		levelExpID = 4,
		unitID = 3221,
		innateSkillID = 32213,
		skillList = {32211, 32212, 32213, 32216, 61101, 61102},
		starSkillSeqID = 3221,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 23221,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 3221,
		abilitySeqID = 6,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {32211, 32212, 32213, 32214},
		introduction = 'Nổi tiếng với võ nghệ cao cường. Triệu Vân bắt đầu sự nghiệp quân sự dưới trướng của Công Tôn Toản trước khi gia nhập lực lượng của Lưu Bị.',
		introduction_vn = 'Nổi tiếng với võ nghệ cao cường. Triệu Vân bắt đầu sự nghiệp quân sự dưới trướng của Công Tôn Toản trước khi gia nhập lực lượng của Lưu Bị.',
		introduction_en = 'Famous for his martial skills. Zhao Yun began his military career under Gong Sun Guan before joining Liu Bei’s forces.',
		introduction_th = 'มีชื่อเสียงในยุทธศาสตร์เลิศล้ำ เจ้าเวินเริ่มต้นเส้นทางทหารใต้บังคับบัญชากงซุ่นต้วน ก่อนเข้าร่วมกองทัพหลิวเป่ย',
		introduction_idn = 'Terkenal dengan keahlian bertarung yang hebat. Zhao Yun memulai karier militernya di bawah Gong Sun Zuan sebelum bergabung dengan Liu Bei.',
		introduction_cn = '张辽以武艺高强著称。赵云早年从公孙瓒起兵，后归顺刘备。',
		introduction_kr = '뛰어난 무술로 유명한 조운은 공손탄 휘하에서 군사 경력을 시작한 뒤 유비 군에 합류하였다.',
		location = 'Formless Thunder & Lightning Black Dragon',
		location_vn = 'Formless Thunder & Lightning Black Dragon',
		location_en = 'Formless Thunder & Lightning Black Dragon',
		location_kr = '무형의 천둥 & 번개 흑룡',
		location_th = 'สายฟ้าไร้รูปแบบ & มังกรดำสายฟ้า',
		location_idn = 'Petir Tanpa Bentuk & Naga Hitam Petir',
		location_cn = '无形雷电&黑雷龙'
	},
	[3241] = {
		id = 3241,
		name = 'Hãn Tuyết Trương Liêu',
		name_vn = 'Hãn Tuyết Trương Liêu',
		name_en = 'Snowstorm Zhang Liao',
		name_th = 'ห่านเสวี่ยจางเหลียว',
		name_idn = 'Han Xue Zhang Liao',
		name_cn = '旱雪张辽',
		name_kr = '한설 장료',
		cardMarkID = 3241,
		atkType = {7},
		zawakeID = 3241,
		zawakeFragID = 53241,
		gender = 0,
		chaRecom = {10, 11, 13, 9},
		nValueRnd = 3,
		specValue = {125, 95, 130, 90, 130, 90, 660},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {618, 619, 620, 604, 605},
		levelExpID = 4,
		unitID = 3241,
		innateSkillID = 32413,
		skillList = {32411, 32412, 32413, 32416, 61151, 61152},
		starSkillSeqID = 3241,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 23241,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 3241,
		abilitySeqID = 9,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {32411, 32412, 32413, 32414},
		introduction = 'Trương Liêu nổi tiếng với sức mạnh hơn người. Tính cách ngay thẳng, không thích nói vòng vo và luôn một lòng vì nước, rất được kính phục.',
		introduction_vn = 'Trương Liêu nổi tiếng với sức mạnh hơn người. Tính cách ngay thẳng, không thích nói vòng vo và luôn một lòng vì nước, rất được kính phục.',
		introduction_en = 'Zhang Liao was famous for his extraordinary strength. Straightforward in character, he disliked beating around the bush and was wholeheartedly loyal to the state, earning great respect.',
		introduction_th = 'จางเหลียว มีชื่อเสียงในความแข็งแกร่งและซื่อตรง ไม่ชอบพูดอ้อมค้อม ทุ่มเทเพื่อแผ่นดิน ได้รับความเคารพอย่างสูง',
		introduction_idn = 'Zhang Liao terkenal karena kekuatan luar biasa. Berwatak jujur, tidak suka bertele-tele, dan selalu setia kepada negara, sangat dihormati.',
		introduction_cn = '张辽以超群力量著称。性格刚直，不喜绕弯，总为国尽忠，深受敬重。',
		introduction_kr = '장료는 강한 힘과 직설적인 성격으로 나라를 위해 헌신하여 존경받았다.'
	},
	[3281] = {
		id = 3281,
		name = 'Tào Nhân',
		name_vn = 'Tào Nhân',
		name_en = 'Cao Ren',
		name_th = 'เตียวเหริน',
		name_idn = 'Cao Ren',
		name_cn = '曹仁',
		name_kr = '조인',
		cardMarkID = 3281,
		atkType = {7},
		zawakeID = 3281,
		zawakeFragID = 53281,
		chaRecom = {15, 16, 11, 12},
		specValue = {56, 38, 61, 65, 48, 45, 313},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 2,
		unitID = 3281,
		innateSkillID = 32813,
		skillList = {32811, 32812, 32813, 32816, 61151, 61152},
		starSkillSeqID = 3281,
		fragID = 23281,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 3281,
		abilitySeqID = 17,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {32811, 32812, 32813},
		introduction = 'Tào Nhân, một trong tám Hổ Kỵ, là danh tướng của Tào Ngụy với sở trường phòng thủ. Chinh chiến cùng Tào Tháo nhiều năm và từng giữ chức Đại Tư Mã.',
		introduction_vn = 'Tào Nhân, một trong tám Hổ Kỵ, là danh tướng của Tào Ngụy với sở trường phòng thủ. Chinh chiến cùng Tào Tháo nhiều năm và từng giữ chức Đại Tư Mã.',
		introduction_en = 'Cao Ren, one of the Eight Tiger Cavalry, was a renowned general of Cao Wei specializing in defense. He fought alongside Cao Cao for many years and served as Grand Marshal.',
		introduction_th = 'เต้าหญิน หนึ่งในแปดเสือม้า เป็นขุนพลผู้มีชื่อเสียงของเต้าหวี่ มีความชำนาญด้านการป้องกัน รบเคียงข้างเต้าตงหลายปี เคยดำรงตำแหน่งแม่ทัพใหญ่ฝ่ายทหาร',
		introduction_idn = 'Cao Ren, salah satu dari Delapan Harimau Berkuda, adalah jenderal Cao Wei dengan keahlian pertahanan. Berperang bersama Cao Cao selama bertahun-tahun dan pernah menjabat sebagai Grand Marshal.',
		introduction_cn = '曹仁，八虎骑之一，曹魏名将，擅长防守。随曹操多年征战，曾任大司马。',
		introduction_kr = '조인은 팔호기 중 한 명으로 조위의 명장으로 방어에 특화되었으며, 조조와 다년간 전투를 치렀고 대사마를 역임했다.',
		location = 'Damage Immunity Defense',
		location_vn = 'Damage Immunity Defense',
		location_en = 'Damage Immunity Defense',
		location_kr = '피해 면역 방어',
		location_th = 'การป้องกันภูมิคุ้มกันความเสียหาย',
		location_idn = 'Pertahanan Kebal Kerusakan',
		location_cn = '伤害免疫防御'
	},
	[3301] = {
		id = 3301,
		name = 'Triệu Vân',
		name_vn = 'Triệu Vân',
		name_en = 'Zhao Yun',
		name_th = 'เจ้าเหวิน',
		name_idn = 'Zhao Yun',
		name_cn = '赵云',
		name_kr = '조운',
		cardMarkID = 3301,
		atkType = {8},
		zawakeFragID = 53301,
		chaRecom = {6, 7, 18, 19},
		specValue = {41, 71, 62, 40, 56, 44, 314},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 3,
		unitID = 3301,
		skinSkillMap = {[113] = {33021, 33022, 33023, 33026, 61101, 61102}, __size = 1},
		innateSkillID = 33013,
		skillList = {33011, 33012, 33013, 33016, 61101, 61102},
		starSkillSeqID = 3301,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 23301,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 3301,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {33011, 33012, 33013, 33014},
		introduction = 'Triệu Vân, người vùng Thường Sơn, một trong Ngũ Hổ Thượng Tướng của Thục Hán. Nổi tiếng với điển tích Triệu Vân cứu ấu chúa.',
		introduction_vn = 'Triệu Vân, người vùng Thường Sơn, một trong Ngũ Hổ Thượng Tướng của Thục Hán. Nổi tiếng với điển tích Triệu Vân cứu ấu chúa.',
		introduction_en = 'Zhao Yun, from Changshan, was one of the Five Tiger Generals of Shu Han. Famous for the legend of rescuing the young lord.',
		introduction_th = 'เจ้าอวี้ จากเมืองฉางซาน หนึ่งในห้าขุนพลผู้กล้าหาญของรัฐฉู่ฮั่น มีชื่อเสียงจากเรื่องราวการช่วยเจ้าหลวง',
		introduction_idn = 'Zhao Yun, orang dari Changshan, salah satu dari Lima Jenderal Harimau Shu Han. Terkenal dengan kisah menyelamatkan bayi penguasa.',
		introduction_cn = '赵云，常山人，蜀汉五虎上将之一。以救幼主典故闻名。',
		introduction_kr = '조운은 상산 출신으로 촉한의 오호상장 중 하나이며, 조운이 유비를 구한 고사로 유명하다.',
		location = 'Savage damage: Increase damage quickly',
		location_vn = 'Savage damage: Increase damage quickly',
		location_en = 'Savage Damage: Rapid Damage Increase',
		location_kr = '광폭 피해: 빠르게 피해 증가',
		location_th = 'ความเสียหายดุร้าย: เพิ่มความเสียหายอย่างรวดเร็ว',
		location_idn = 'Kerusakan Buas: Meningkatkan Kerusakan dengan Cepat',
		location_cn = '狂暴伤害：快速提升伤害'
	},
	[3471] = {
		id = 3471,
		name = 'Tôn Kiên',
		name_vn = 'Tôn Kiên',
		name_en = 'Sun Jian',
		name_th = 'ซุนเกียน',
		name_idn = 'Sun Jian',
		name_cn = '孙坚',
		name_kr = '손견',
		cardMarkID = 3471,
		atkType = {8},
		zawakeID = 3471,
		zawakeFragID = 53471,
		chaRecom = {24, 23, 14, 16},
		specValue = {50, 44, 53, 62, 58, 63, 330},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {621, 622, 623, 602, 603},
		levelExpID = 2,
		unitID = 3471,
		innateSkillID = 34713,
		skillList = {34711, 34712, 34713, 34716, 61101, 61102},
		starSkillSeqID = 3471,
		fragID = 23471,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 3471,
		abilitySeqID = 16,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {34711, 34712, 34713},
		introduction = 'Tôn Kiên là người đặt nền móng xây dựng nước Đông Ngô. Từng tham gia chiến dịch thảo phạt Hoàng Cân và Đổng Trác.',
		introduction_vn = 'Tôn Kiên là người đặt nền móng xây dựng nước Đông Ngô. Từng tham gia chiến dịch thảo phạt Hoàng Cân và Đổng Trác.',
		introduction_en = 'Sun Jian laid the foundation for building Eastern Wu. He participated in campaigns against Huang Can and Dong Zhuo.',
		introduction_th = 'ซุ่นเกียน เป็นผู้วางรากฐานสร้างรัฐตงงู เคยเข้าร่วมรบปราบกองทัพหวงจินและตงจั๋ว',
		introduction_idn = 'Sun Jian adalah pendiri negara Wu Timur. Pernah ikut kampanye melawan Huang Gan dan Dong Zhuo.',
		introduction_cn = '孙坚，东吴奠基人，参与讨伐黄巾军及董卓军。',
		introduction_kr = '손견은 동오 건국의 기초를 다진 인물로, 황건적과 동탁 토벌에 참여했다.',
		location = 'Final Speed Heavy Gunner',
		location_vn = 'Final Speed Heavy Gunner',
		location_en = 'Final Speed Heavy Gunner',
		location_kr = '최종 속도 중화기병',
		location_th = 'พลซุ่มยิงความเร็วสูงสุด',
		location_idn = 'Penembak Berat Kecepatan Akhir',
		location_cn = '终极速射枪手'
	},
	[3511] = {
		id = 3511,
		name = 'Tào Chương',
		name_vn = 'Tào Chương',
		name_en = 'Cao Zhang',
		name_th = 'เตียวจวง',
		name_idn = 'Cao Zhang',
		name_cn = '曹彰',
		name_kr = '조창',
		cardMarkID = 3511,
		atkType = {7},
		zawakeID = 3511,
		zawakeFragID = 53511,
		chaRecom = {8, 9, 13, 12},
		specValue = {78, 118, 92, 75, 74, 63, 500},
		starTypeID = 2,
		advanceTypeID = 11,
		feelType = 2,
		feelItems = {612, 613, 614, 602, 603},
		levelExpID = 2,
		unitID = 3511,
		innateSkillID = 35113,
		skillList = {35111, 35112, 35113, 35116, 61101, 61102},
		starSkillSeqID = 3511,
		fragID = 23511,
		fragNumType = 2,
		star = 2,
		starEffectIndex = 3511,
		abilitySeqID = 16,
		effortSeqID = 2,
		gemPosSeqID = 2,
		gemQualitySeqID = 2,
		fetterList = {35111, 35112, 35113},
		introduction = 'Tào Chương là con trai của Tào Tháo và là anh trai của Tào Phi. Có tài năng quân sự và rất trung thành.',
		introduction_vn = 'Tào Chương là con trai của Tào Tháo và là anh trai của Tào Phi. Có tài năng quân sự và rất trung thành.',
		introduction_en = 'Cao Zhang was the son of Cao Cao and elder brother of Cao Pi. He was militarily talented and very loyal.',
		introduction_th = 'เต้าจวง บุตรชายของเต้าตงและพี่ชายของเต้าฝี่ มีความสามารถทางยุทธศาสตร์และซื่อสัตย์อย่างยิ่ง',
		introduction_idn = 'Cao Zhang adalah putra Cao Cao dan kakak Cao Pi. Memiliki bakat militer dan sangat setia.',
		introduction_cn = '曹彰，曹操长子，曹丕兄，军事才能出众，忠心耿耿。',
		introduction_kr = '조장은 조조의 아들이자 조비의 형으로 군사 재능과 충성이 뛰어났다.',
		location = 'Charging damage: Tap and kill all enemies',
		location_vn = 'Charging damage: Tap and kill all enemies',
		location_en = 'Charging Damage: Tap to Kill All Enemies',
		location_kr = '충전 피해: 탭하여 모든 적 처치',
		location_th = 'ความเสียหายชาร์จ: แตะและฆ่าศัตรูทั้งหมด',
		location_idn = 'Kerusakan Pengisian: Ketuk dan Bunuh Semua Musuh',
		location_cn = '蓄力伤害：点击秒杀全敌'
	},
	[3561] = {
		id = 3561,
		name = 'Mưu Sĩ',
		name_vn = 'Mưu Sĩ',
		name_en = 'Strategist',
		name_th = 'ที่ปรึกษา',
		name_idn = 'Penasihat',
		name_cn = '谋士',
		name_kr = '모사',
		cardMarkID = 3561,
		atkType = {7},
		zawakeFragID = 53561,
		chaRecom = {24, 25, 11, 12},
		specValue = {43, 38, 70, 48, 50, 60, 309},
		feelItems = {621, 622, 623, 602, 603},
		unitID = 3561,
		innateSkillID = 35613,
		skillList = {35611, 35612, 35613, 35616, 61101, 61102},
		starSkillSeqID = 3561,
		equipsList = {11001, 11101, 11201, 11301},
		fragID = 23561,
		star = 2,
		starEffectIndex = 3561,
		abilitySeqID = 13,
		fetterList = {35611, 35612, 35613},
		introduction = 'These Heroes are created when spirits possess rotten tree stumps. They prefer to live in abandoned forests.',
		introduction_vn = 'These Heroes are created when spirits possess rotten tree stumps. They prefer to live in abandoned forests.',
		introduction_en = 'These Heroes are created when spirits possess rotten tree stumps. They prefer to live in abandoned forests.',
		introduction_th = 'ฮีโร่เหล่านี้ถูกสร้างขึ้นเมื่อจิตวิญญาณเข้าสิงท่อนซุงผุพัง มักอาศัยอยู่ในป่าร้าง',
		introduction_idn = 'Para Pahlawan ini tercipta ketika roh merasuki tunggul pohon yang membusuk. Mereka lebih suka tinggal di hutan yang ditinggalkan.',
		introduction_cn = '这些英雄由灵魂附身于腐朽树桩而成。喜居废弃森林。',
		introduction_kr = '이 영웅들은 영혼이 썩은 나무 그루터기에 깃들 때 창조된다. 버려진 숲에서 사는 것을 좋아한다.',
		location = 'Silence the back line',
		location_vn = 'Silence the back line',
		location_en = 'Silence the Back Line',
		location_kr = '후열 침묵',
		location_th = 'ปิดปากแนวหลัง',
		location_idn = 'Membungkam Barisan Belakang',
		location_cn = '沉默后排'
	},
	[3562] = {
		id = 3562,
		name = 'Pháp Sư Khăn Vàng',
		name_vn = 'Pháp Sư Khăn Vàng',
		name_en = 'Yellow Turban Mage',
		name_th = 'จอมเวทผ้าขาวเหลือง',
		name_idn = 'Ahli Sihir Kain Kuning',
		name_cn = '黄巾法师',
		name_kr = '황건법사',
		cardMarkID = 3562,
		atkType = {7},
		zawakeFragID = 53561,
		chaRecom = {24, 25, 11, 12},
		specValue = {85, 56, 110, 76, 65, 82, 474},
		feelItems = {621, 622, 623, 602, 603},
		unitID = 3562,
		innateSkillID = 35623,
		skillList = {35621, 35622, 35623, 35626, 61101, 61102},
		starSkillSeqID = 3562,
		equipsList = {11001, 11101, 11201, 11301},
		fragID = 23561,
		star = 2,
		starEffectIndex = 3562,
		abilitySeqID = 13,
		fetterList = {35611, 35612, 35613},
		introduction = 'It can control trees at will. It will trap people who harm the forest, so they can never leave.',
		introduction_vn = 'It can control trees at will. It will trap people who harm the forest, so they can never leave.',
		introduction_en = 'It can control trees at will. It will trap people who harm the forest, so they can never leave.',
		introduction_th = 'มันสามารถควบคุมต้นไม้ได้ตามใจ จะดักจับคนที่ทำร้ายป่าไม่ให้หนีไปได้',
		introduction_idn = 'Ia dapat mengendalikan pohon sesuka hati. Ia akan menjebak orang yang merusak hutan agar tidak bisa keluar.',
		introduction_cn = '能随意操控树木。会困住伤害森林的人，使其永远无法离开。',
		introduction_kr = '나무를 자유자재로 조종하며, 숲을 해치는 사람들을 가두어 절대 나가지 못하게 한다.',
		location = 'Silence the back line',
		location_vn = 'Silence the back line',
		location_en = 'Silence the Back Line',
		location_kr = '후열 침묵',
		location_th = 'ปิดปากแนวหลัง',
		location_idn = 'Membungkam Barisan Belakang',
		location_cn = '沉默后排'
	},
	[3601] = {
		id = 3601,
		name = 'Minh U Điêu Thuyền',
		name_vn = 'Minh U Điêu Thuyền',
		name_en = 'Gentle Diao Chan',
		name_th = 'หมิงอู่เตียวเทียน',
		name_idn = 'Terang Diao Chan',
		name_cn = '明羽貂蝉',
		name_kr = '명우 초선',
		cardMarkID = 3601,
		atkType = {8},
		gender = 0,
		chaRecom = {24, 23, 14, 16},
		nValueRnd = 3,
		specValue = {126, 99, 131, 95, 131, 98, 680},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {618, 619, 620, 604, 605},
		levelExpID = 4,
		unitID = 3601,
		innateSkillID = 36013,
		skillList = {36011, 36012, 36013, 36016, 61151, 61152},
		starSkillSeqID = 3601,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 23601,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 3601,
		abilitySeqID = 9,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {36011, 36012, 36013, 36014},
		introduction = 'Điêu Thuyền, một trong tứ đại mỹ nhân Trung Quốc. Vì cứu Hán triều đã dùng mỹ nhân kế ly gián Đổng Trác và Lữ Bố, tiêu diệt được ác tặc Đổng Trác.',
		introduction_vn = 'Điêu Thuyền, một trong tứ đại mỹ nhân Trung Quốc. Vì cứu Hán triều đã dùng mỹ nhân kế ly gián Đổng Trác và Lữ Bố, tiêu diệt được ác tặc Đổng Trác.',
		introduction_en = 'Diao Chan, one of China’s Four Beauties. To save the Han dynasty, she used a beauty trap to drive a wedge between Dong Zhuo and Lü Bu, eliminating the tyrant Dong Zhuo.',
		introduction_th = 'เตียวเทียน หนึ่งในสี่สาวงามจีน เพื่อช่วยราชวงศ์ฮั่นใช้แผนสาวงามทำให้ตงจั๋วและลู่ปู๋แตกแยก จนสามารถทำลายตงจั๋วผู้ชั่วร้าย',
		introduction_idn = 'Diao Chan, salah satu dari Empat Wanita Cantik Cina. Demi menyelamatkan Dinasti Han, ia menggunakan siasat kecantikan untuk memecah belah Dong Zhuo dan Lü Bu, membunuh tiran Dong Zhuo.',
		introduction_cn = '貂蝉，中华四大美女之一。为救汉室用美人计离间董卓与吕布，灭恶贼董卓。',
		introduction_kr = '조비는 중국 4대 미녀 중 하나로, 한나라를 구하기 위해 미인계를 써 동탁과 여포를 이간하여 악탁 동탁을 제거했다.'
	},
	[3631] = {
		id = 3631,
		name = 'Trương Tinh Thái',
		name_vn = 'Trương Tinh Thái',
		name_en = 'Zhang Qing Tai',
		name_th = 'จางติงไท่',
		name_idn = 'Zhang Ting Tai',
		name_cn = '张星彩',
		name_kr = '장정태',
		cardMarkID = 3631,
		atkType = {8},
		zawakeID = 3631,
		zawakeFragID = 53631,
		gender = 0,
		chaRecom = {24, 23, 14, 16},
		nValueRnd = 2,
		specValue = {50, 50, 100, 150, 100, 150, 600},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {615, 616, 617, 604, 605},
		levelExpID = 4,
		unitID = 3631,
		skinSkillMap = {[114] = {36321, 36322, 36323, 36326, 61151, 61152}, __size = 1},
		innateSkillID = 36313,
		skillList = {36311, 36312, 36313, 36316, 61151, 61152},
		starSkillSeqID = 3631,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 23631,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 3631,
		abilitySeqID = 9,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {36311, 36312, 36313, 36314},
		introduction = 'Trương Tĩnh Thái, con gái của Trương Phi, có vẻ đẹp hoạt bát nhí nhảnh. Lớn lên gả cho Lưu Thiện, được phong làm hoàng hậu.',
		introduction_vn = 'Trương Tĩnh Thái, con gái của Trương Phi, có vẻ đẹp hoạt bát nhí nhảnh. Lớn lên gả cho Lưu Thiện, được phong làm hoàng hậu.',
		introduction_en = 'Zhang Jingtai, daughter of Zhang Fei, known for her lively and playful beauty. She married Liu Shan and was made empress.',
		introduction_th = 'จางจิ่งไท่ ลูกสาวจางเฟย มีความงามสดใส ร่าเริง โตขึ้นแต่งงานกับหลิวเซียน ได้รับตำแหน่งราชินี',
		introduction_idn = 'Zhang Jingtai, putri Zhang Fei, memiliki kecantikan ceria dan lincah. Dewasa menikah dengan Liu Shan, diangkat menjadi permaisuri.',
		introduction_cn = '张靖泰，张飞女儿，活泼可爱。长大后嫁刘禅，封为皇后。',
		introduction_kr = '장정태는 장비의 딸로 활발하고 발랄한 미모를 지녔다. 성장하여 유선과 결혼해 황후가 되었다.',
		location = 'Diamond Princess & Perfect Support',
		location_vn = 'Diamond Princess & Perfect Support',
		location_en = 'Diamond Princess & Perfect Support',
		location_kr = '다이아몬드 공주 & 완벽한 지원',
		location_th = 'เจ้าหญิงเพชร & สนับสนุนสมบูรณ์แบบ',
		location_idn = 'Putri Berlian & Dukungan Sempurna',
		location_cn = '钻石公主&完美支援'
	},
	[3661] = {
		id = 3661,
		name = 'Quan Hưng',
		name_vn = 'Quan Hưng',
		name_en = 'Guan Xing',
		name_th = 'กวนหง',
		name_idn = 'Guan Xing',
		name_cn = '关兴',
		name_kr = '관흥',
		cardMarkID = 3661,
		atkType = {7},
		zawakeID = 3661,
		zawakeFragID = 53661,
		chaRecom = {7, 8, 9, 12},
		specValue = {68, 42, 55, 55, 50, 50, 320},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 3,
		unitID = 3661,
		skinSkillMap = {[111] = {36621, 36622, 36623, 36626, 61101, 61102}, __size = 1},
		innateSkillID = 36613,
		skillList = {36611, 36612, 36613, 36616, 61101, 61102},
		starSkillSeqID = 3661,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 23661,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 3661,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {36611, 36612, 36613, 36614},
		introduction = 'Quan Hưng là con thứ của Quan Vũ, từng theo Lưu Bị chinh phạt Đông Ngô, diệt được kẻ thù Phan Chương, đoạt lại Thanh Long Đao.',
		introduction_vn = 'Quan Hưng là con thứ của Quan Vũ, từng theo Lưu Bị chinh phạt Đông Ngô, diệt được kẻ thù Phan Chương, đoạt lại Thanh Long Đao.',
		introduction_en = 'Quan Xing, the younger son of Guan Yu, followed Liu Bei to campaign against Eastern Wu, defeated the enemy Fan Zhang, and recovered the Green Dragon Crescent Blade.',
		introduction_th = 'กวนหึง ลูกชายคนรองของกวนอู เคยติดตามหลิวเป่ยไปปราบตงงู สังหารศัตรูฟานจวงและยึดดาบเขียวมังกรคืน',
		introduction_idn = 'Guan Xing adalah putra kedua Guan Yu, mengikuti Liu Bei menyerang Wu Timur, mengalahkan musuh Pan Zhang, merebut kembali Pedang Naga Hijau.',
		introduction_cn = '关兴，关羽次子，随刘备征讨东吴，击败敌人樊长，夺回青龙偃月刀。',
		introduction_kr = '관흥은 관우의 차남으로 유비를 따라 동오를 정벌해 적 반장을 죽이고 청룡도를 탈환했다.',
		location = 'Charging damage: Tap and kill all enemies',
		location_vn = 'Charging damage: Tap and kill all enemies',
		location_en = 'Charging Damage: Tap to Kill All Enemies',
		location_kr = '충전 피해: 탭하여 모든 적 처치',
		location_th = 'ความเสียหายชาร์จ: แตะและฆ่าศัตรูทั้งหมด',
		location_idn = 'Kerusakan Pengisian: Ketuk dan Bunuh Semua Musuh',
		location_cn = '蓄力伤害：点击秒杀全敌'
	},
	[3841] = {
		id = 3841,
		name = 'Lỗ Túc',
		name_vn = 'Lỗ Túc',
		name_en = 'Lu Su',
		name_th = 'หลู่จู้',
		name_idn = 'Lu Su',
		name_cn = '卢植',
		name_kr = '노숙',
		cardMarkID = 3841,
		atkType = {7},
		zawakeID = 3841,
		zawakeFragID = 53841,
		gender = 2,
		chaRecom = {24, 23, 14, 16},
		specValue = {42, 32, 30, 38, 30, 38, 210},
		starTypeID = 3,
		advanceTypeID = 11,
		feelType = 3,
		feelItems = {609, 610, 611, 602, 603},
		levelExpID = 3,
		unitID = 3841,
		innateSkillID = 38413,
		skillList = {38411, 38412, 38413, 38416, 61101, 61102},
		starSkillSeqID = 3841,
		equipsList = {11003, 11103, 11203, 11303},
		fragID = 23841,
		fragNumType = 3,
		star = 3,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 3841,
		effortSeqID = 3,
		gemPosSeqID = 3,
		gemQualitySeqID = 3,
		fetterList = {38411, 38412, 38413, 38414},
		introduction = 'Lỗ Túc là tướng lĩnh quân sự và nhà ngoại giao phục vụ dưới trướng Tôn Quyền vào cuối thời Đông Hán.',
		introduction_vn = 'Lỗ Túc là tướng lĩnh quân sự và nhà ngoại giao phục vụ dưới trướng Tôn Quyền vào cuối thời Đông Hán.',
		introduction_en = 'Lu Su was a military commander and diplomat serving under Sun Quan at the end of the Eastern Han.',
		introduction_th = 'หลู่จู่ เป็นแม่ทัพและนักการทูตใต้บังคับบัญชาซุ่นชวนในปลายยุคตงฮั่น',
		introduction_idn = 'Lu Su adalah jenderal militer dan diplomat di bawah Sun Quan pada akhir Dinasti Han Timur.',
		introduction_cn = '卢植，东汉末军事将领和外交官，效力孙权。',
		introduction_kr = '노촉은 동한 말기 손권 휘하의 군사이자 외교관이었다.',
		location = 'Brutal DPSer: Tropical Combo',
		location_vn = 'Brutal DPSer: Tropical Combo',
		location_en = 'Brutal DPS: Tropical Combo',
		location_kr = '잔인한 딜러: 열대 콤보',
		location_th = 'ผู้สร้างความเสียหายโหดเหี้ยม: คอมโบเขตร้อน',
		location_idn = 'DPSer Brutal: Kombinasi Tropis',
		location_cn = '残暴输出手：热带连击'
	},
	[4121] = {
		id = 4121,
		name = 'Cuồng Quỷ Tư Mã Ý',
		name_vn = 'Cuồng Quỷ Tư Mã Ý',
		name_en = 'Mad Demon Sima Yi',
		name_th = 'ปีศาจคลั่งซื่อหม่าอี๋',
		name_idn = 'Setan Gila Sima Yi',
		name_cn = '狂鬼司马懿',
		name_kr = '광귀 사마의',
		cardMarkID = 4121,
		atkType = {7},
		zawakeID = 4121,
		zawakeFragID = 54121,
		gender = 0,
		chaRecom = {8, 9, 13, 12},
		nValueRnd = 3,
		specValue = {90, 125, 90, 80, 125, 90, 600},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {612, 613, 614, 604, 605},
		levelExpID = 4,
		unitID = 4121,
		innateSkillID = 41213,
		skillList = {41211, 41212, 41213, 41216, 61101, 61102},
		starSkillSeqID = 4121,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 24121,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 4121,
		abilitySeqID = 6,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {41211, 41212, 41213, 41214},
		townSkill = 1163,
		energy = 20,
		energyCorrection = 0.9,
		introduction = 'Tư Mã Ý học rộng biết nhiều, hành sự quả quyết, giỏi lập mưu tính kế và nhiều lần chinh phạt lập công. Tư Mã Ý có dã tâm nham hiểm và là người đặt nền móng cho vương triều Tây Tấn. ',
		introduction_vn = 'Tư Mã Ý học rộng biết nhiều, hành sự quả quyết, giỏi lập mưu tính kế và nhiều lần chinh phạt lập công. Tư Mã Ý có dã tâm nham hiểm và là người đặt nền móng cho vương triều Tây Tấn. ',
		introduction_en = 'Sima Yi was well-read and knowledgeable, decisive in action, skilled in strategy, and repeatedly victorious in battle. He was ambitious and laid the foundation for the Western Jin dynasty.',
		introduction_th = 'ซื่อหม่าอี้ มีความรู้กว้างไกล ดำเนินการเด็ดขาด เชี่ยวชาญวางแผนยุทธศาสตร์และสร้างผลงานหลายครั้ง มีความทะเยอทะยานและเป็นผู้วางรากฐานราชวงศ์ไต้จิน',
		introduction_idn = 'Sima Yi berwawasan luas, tegas dalam bertindak, ahli dalam merencanakan strategi, dan sering memimpin penaklukan. Sima Yi memiliki ambisi licik dan merupakan pendiri dinasti Jin Barat.',
		introduction_cn = '司马懿学识渊博，行事果断，善于谋划，多次征战立功。野心勃勃，奠定西晋王朝基础。',
		introduction_kr = '사마의는 박식하고 결단력이 있으며 책략에 능하고 여러 차례 전공을 세웠다. 교활한 야심가로 서진 왕조의 기초를 놓았다.',
		location = '鬼武神隐&影之传说',
		location_vn = '鬼武神隐&影之传说',
		location_en = '鬼武神隐&影之传说',
		location_kr = '鬼武神隐&影之传说',
		location_th = '鬼武神隐&影之传说',
		location_idn = '鬼武神隐&影之传说',
		location_cn = '鬼武神隐&影之传说'
	},
	[4161] = {
		id = 4161,
		name = 'Thương Lan Quan Vũ',
		name_vn = 'Thương Lan Quan Vũ',
		name_en = 'Thương Lan Quan Vũ',
		name_th = 'Thương Lan Quan Vũ',
		name_idn = 'Thương Lan Quan Vũ',
		name_cn = 'Thương Lan Quan Vũ',
		name_kr = 'Thương Lan Quan Vũ',
		cardMarkID = 4161,
		atkType = {7},
		zawakeID = 4161,
		zawakeFragID = 54161,
		gender = 0,
		chaRecom = {8, 9, 13, 12},
		nValueRnd = 3,
		specValue = {88, 143, 112, 75, 102, 80, 600},
		starTypeID = 4,
		advanceTypeID = 21,
		feelType = 4,
		feelItems = {612, 613, 614, 604, 605},
		levelExpID = 4,
		unitID = 4161,
		innateSkillID = 41613,
		skillList = {41611, 41612, 41613, 41616, 61101, 61102},
		starSkillSeqID = 4161,
		equipsList = {11004, 11104, 11204, 11304},
		fragID = 24161,
		fragNumType = 4,
		star = 4,
		effect = {['drawcard'] = 'effect_loop', __size = 1},
		starEffectIndex = 4161,
		abilitySeqID = 6,
		effortSeqID = 4,
		gemPosSeqID = 4,
		gemQualitySeqID = 4,
		fetterList = {41611, 41612, 41613, 41614},
		townSkill = 1183,
		energy = 20,
		energyCorrection = 0.88,
		introduction = 'Lighting Pokemon, the pads of both hands and feet can emit a large amount of current to create a strong magnetic field, making it float in the air and move at high speed against gravity. It is said that its max moving speed is not interior to the speed of falling thunder',
		introduction_vn = 'Lighting Pokemon, the pads of both hands and feet can emit a large amount of current to create a strong magnetic field, making it float in the air and move at high speed against gravity. It is said that its max moving speed is not interior to the speed of falling thunder'
	},

	__size = 124,
	__default = {
		__index = {
			name = '',
			name_vn = '',
			name_en = '',
			name_th = '',
			name_idn = '',
			name_cn = '',
			name_kr = '',
			languages = {'cn', 'br', 'en', 'vn', 'th', 'kr', 'id'},
			drawlanguages = {'cn', 'br', 'en', 'vn', 'th', 'kr', 'id'},
			cardType = 1,
			atkType = {7, 8},
			develop = 1,
			branch = 0,
			branchType = 1,
			megaIndex = 0,
			canDevelop = true,
			developType = 3,
			zawakeID = 0,
			zawakeFragID = 0,
			maleRate = 5000,
			chaRnd = 1,
			chaRecom = {2, 15},
			nValueRnd = 1,
			specValue = {39, 65, 52, 43, 60, 50, 309},
			fightingWeight = 1,
			starTypeID = 1,
			advanceTypeID = 1,
			feelType = 1,
			feelItems = {601, 602, 603},
			advanceMax = 26,
			advanceLevelReq = {1, 1, 8, 16, 22, 28, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100, 105, 110, 115, 120, 125, 130, 135, 140, 145},
			levelExpID = 1,
			unitID = 92,
			levelMax = 129,
			skinSkillMap = {__size = 0},
			innateSkillID = 60003,
			skillList = {60001, 60002, 60003, 61101, 61102},
			starSkillSeqID = 9001,
			starSkillMaxLevel = 100,
			equipsList = {11002, 11102, 11202, 11302},
			fragID = 21002,
			fragNumType = 1,
			star = 1,
			effect = {['gain'] = false, ['drawcard'] = false, __size = 2},
			starEffectIndex = 1,
			abilitySeqID = 1,
			effortSeqID = 1,
			gemPosSeqID = 1,
			gemQualitySeqID = 1,
			fetterList = {},
			townSkill = 0,
			energy = 10,
			energyCorrection = 1,
			introduction = 'Incredible creatures that live in the Hero world',
			introduction_vn = 'Incredible creatures that live in the Hero world',
			introduction_en = 'Incredible creatures that live in the Hero world',
			introduction_th = 'สิ่งมีชีวิตมหัศจรรย์ที่อาศัยอยู่ในโลกโปเกมอน',
			introduction_idn = 'Makhluk luar biasa yang hidup di dunia Hero',
			introduction_cn = '生活在宝可梦世界的不可思议的生物',
			introduction_kr = '포켓몬 세계에 사는 놀라운 생물들',
			location = 'Unknown',
			location_vn = 'Unknown',
			location_en = 'Unknown',
			location_kr = '알 수 없음',
			location_th = 'ไม่ทราบ',
			location_idn = 'Tidak Dikenal',
			location_cn = '未知',
			aidLevelSkillID = 0,
			contractPlan = 1,
			aidID = 0,
			aidAwakeSkillID = 0,
			aidStageSkillID = 0
		}
	}
}
return csv.cards