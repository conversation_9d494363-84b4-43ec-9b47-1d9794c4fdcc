# This .gitignore file should be placed at the root of your Unity project directory
#
# Get latest from https://github.com/github/gitignore/blob/main/Unity.gitignore
#
/[Ll]ibrary/
/[Tt]emp/
/[Oo]bj/
/[Bb]uild/
/[Bb]uilds/
/[Ll]ogs/
/[Uu]ser[Ss]ettings/

# MemoryCaptures can get excessive in size.
# They also could contain extremely sensitive data
/[Mm]emoryCaptures/

# Recordings can get excessive in size
/[Rr]ecordings/

# Uncomment this line if you wish to ignore the asset store tools plugin
# /[Aa]ssets/AssetStoreTools*

# Autogenerated Jetbrains Rider plugin
/[Aa]ssets/Plugins/Editor/JetBrains*

# Visual Studio cache directory
.vs/

# Gradle cache directory
.gradle/

# Autogenerated VS/MD/Consulo solution and project files
ExportedObj/
.consulo/
*.csproj
*.unityproj
*.sln
*.suo
*.tmp
*.user
*.userprefs
*.pidb
*.booproj
*.svd
*.pdb
*.mdb
*.opendb
*.VC.db

# Unity3D generated meta files
*.pidb.meta
*.pdb.meta
*.mdb.meta

# Unity3D generated file on crash reports
sysinfo.txt

# Builds
*.apk
*.aab
*.unitypackage
*.app

# Crashlytics generated file
crashlytics-build.properties

# Packed Addressables
/[Aa]ssets/[Aa]ddressable[Aa]ssets[Dd]ata/*/*.bin*

# Temporary auto-generated Android Assets
/[Aa]ssets/[Ss]treamingAssets/aa.meta
/[Aa]ssets/[Ss]treamingAssets/aa/*
*.log
simulator/win32/UserDefault.xml
Backup/
OkamiUI_Dev/
*.dmp
*.pyc
__pycache__/
OkamiTool/csv_dev/equips.csv.bak
src/config/equips.lua.bak
res/resources/3q/equip/equip.rar
configmod/UIProj/**/Resources
configmod/UIProj/**/Ruler

/configmod/lua2csv/lua_run/*
/configmod/lua2csv/csv_out/*
configmod/UIProj/link.ps1
*/.idea/
OkamiTool/LastModifyList.txt

/res/spine/effect3q/SkillUtil/images
/frameworks
/new_frameworks
/tjpack_tool
/repository
/repositoryx
/rb
/res/resources/city/adventure/gym_challenge
/res/spine/gym
/653dc71
/653dc7
/res/spine/okamiSpine
/OkamiTest
res/test.rar
res/release.rar
/server_dev
test_fox_coin_system.lua
