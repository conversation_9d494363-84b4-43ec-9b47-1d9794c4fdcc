
local languagePlist = cc.FileUtils:getInstance():getValueMapFromFile('res/language.plist')
globals.LOCAL_LANGUAGE = languagePlist.localization or 'en'
globals.OkamiLan = userDefault.getForeverLocalKey("OkamiLan", "en", {rawKey = true})

printInfo('LOCAL_LANGUAGE %s', LOCAL_LANGUAGE)

--
-- versionPlist
--
local plistRes = 'res/version.plist'
local versionLanguage = {
	trial = "cn",
	test = "cn",
}
local versionPlist = cc.FileUtils:getInstance():getValueMapFromFile(plistRes)
local getlanguage = userDefault.getForeverLocalKey("OkamiLan", "", {rawKey = true})
globals.UNIVERSAL_TIMEDELTA = 7 * 3600
if getlanguage == 'en' then
	UNIVERSAL_TIMEDELTA = 7 * 3600
elseif getlanguage == 'br' or getlanguage == 'vn' then
	UNIVERSAL_TIMEDELTA = 7 * 3600
else
	UNIVERSAL_TIMEDELTA = 7 * 3600
end
printInfo('UNIVERSAL_TIMEDELTA %d hours', UNIVERSAL_TIMEDELTA/3600)

if languagePlist.showlanguages then
	local t = string.split(languagePlist.showlanguages, ',')
	local curLanguage = getBaseLanguage() or LOCAL_LANGUAGE
	if not itertools.include(t, curLanguage) then
		table.insert(t, 1, curLanguage)
	end
	globals.SHOW_LANGUAGES = t
end

if LOCAL_LANGUAGE == "en" then
	globals.SHOW_LANGUAGES = {"en", "vn", "th", "kr", "idn"}


	-- 阿拉伯语需要支持 R2L, createWithSystemFont 操作系统本身已内置对复杂文本布局（CTL）的支持，自动处理R2L和L2R混合版本，遵循Unicode双向算法
	local systemFontName = "Arial"
	local _labelCreateWithTTF = cc.Label.createWithTTF
	local _labelSetSystemFontName = cc.Label.setSystemFontName
	local _textCreate = ccui.Text.create
	local _textSetFontName = ccui.Text.setFontName
	local _textFieldCreate = ccui.TextField.create
	local _textFieldSetFontName = ccui.TextField.setFontName
	local _richTextCreate = ccui.RichElementText.create
	globals.resetFontCreate = function()
		if not isShowLanguageR2L() then
			cc.Label.createWithTTF = _labelCreateWithTTF
			cc.Label.setSystemFontName = _labelSetSystemFontName
			ccui.Text.create = _textCreate
			ccui.Text.setFontName = _textSetFontName
			ccui.TextField.create = _textFieldCreate
			ccui.TextField.setFontName = _textFieldSetFontName
			ccui.RichElementText.create = _richTextCreate
			return
		end
		cc.Label.createWithTTF = function(cls, str, fontName, ...)
			local label = cc.Label:createWithSystemFont(str, systemFontName, ...)
			-- 多行默认设置为右对齐方式
			label:setHorizontalAlignment(cc.TEXT_ALIGNMENT_RIGHT)
			return label
		end
		cc.Label.setSystemFontName = function(cls, str)
			local ret = _labelSetSystemFontName(cls, systemFontName)
			cls:setSystemFontSize(cls:getSystemFontSize())
			return ret
		end

		ccui.Text.create = function(cls, str, fontName, ...)
			return _textCreate(cls, str, systemFontName, ...)
		end
		ccui.Text.setFontName = function(cls, str)
			local ret = _textSetFontName(cls, systemFontName)
			cls:setFontSize(cls:getFontSize())
			return ret
		end

		ccui.TextField.create = function(cls, str, fontName, ...)
			return _textFieldCreate(cls, str, systemFontName, ...)
		end
		ccui.TextField.setFontName = function(cls, str)
			local ret = _textFieldSetFontName(cls, systemFontName)
			cls:setFontSize(cls:getFontSize())
			return ret
		end

		ccui.RichElementText.create = function(cls, tag, color, opacity, left, ttf, ...)
			return _richTextCreate(cls, tag, color, opacity, left, systemFontName, ...)
		end
	end
	resetFontCreate()
end

globals.APILINK = "https://paydto.redfoxgames.vn/api/"
	globals.VERSION_CONF_URL = versionPlist.versionUrl
	globals.SERVER_CONF_URL = versionPlist.serverUrl
	globals.NOTICE_CONF_URL = versionPlist.noticeUrl
	globals.REPORT_CONF_URL = versionPlist.reportUrl
	globals.FEED_BACK_URL = versionPlist.feedBackUrl

	globals.SUPPORT_URL = "https://www.facebook.com/danhtuongorigin"
	globals.TIKTOK_URL = "https://www.tiktok.com/@danh.tuong.origin"
	globals.YTB_URL = "https://www.youtube.com/@danhtuongorigin"
	globals.GROUPFB_URL = "https://www.facebook.com/groups/dtotuuquan"
	globals.DISCORD_URL = "https://www.facebook.com/groups/dtotuuqua"
	globals.ZALO_URL = "https://zalo.me/g/tolgco164"
	globals.JUMP_SHOP_URL = "https://play.google.com/store/apps/details?id=OkamiAN" -- BR
	globals.PayUrl="https://paydto.redfoxgames.vn/api/PaymentGame?"

	globals.DISABLE_WORD_CHECK_URL = versionPlist.disableWordCheckUrl -- or "http://*************:1144/check"
	globals.FOR_SHENHE = string.lower(versionPlist.forShenhe or "") == "true"
	globals.LOGIN_SERVRE_HOSTS_TABLE = {versionPlist.loginServer}
	globals.USE_MUTILLAN= string.lower(versionPlist.MutilLanguage or "") == "true"
	printInfo(USE_MUTILLAN)

for i = 2, 10 do
	if versionPlist[string.format("loginServer%d",i)] then
		table.insert(LOGIN_SERVRE_HOSTS_TABLE, versionPlist[string.format("loginServer%d",i)])
	end
end
if next(LOGIN_SERVRE_HOSTS_TABLE) then
	globals.IPV6_TEST_HOST = string.gmatch(LOGIN_SERVRE_HOSTS_TABLE[1], '([-a-z0-9A-Z.]+):(%d+)')()
end

if ymdump then
	-- 获取最新plist中的reportUrl
	ymdump.setUserInfo("url", REPORT_CONF_URL)
	printInfo('REPORT_CONF_URL %s', REPORT_CONF_URL)
end

--userdefault里保存的app版本 只对前三位维护
globals.APP_VERSION = versionPlist.app_version
printInfo('APP_VERSION %s', APP_VERSION)

--
-- channelPlist
--
local channelPlist = cc.FileUtils:getInstance():getValueMapFromFile('res/channel.plist')
globals.APP_CHANNEL = channelPlist.channel
globals.APP_TAG = channelPlist.tag
printInfo('APP_CHANNEL %s', APP_CHANNEL)
printInfo('APP_TAG %s', APP_TAG)


globals.FAKE_APP = cc.FileUtils:getInstance():isFileExist('fake') or cc.FileUtils:getInstance():isFileExist('.fake')
printInfo('FAKE_APP %s', FAKE_APP)

local langCode = cc.Application:getInstance():getCurrentLanguageCode()
print('langCode debug', langCode)

--
-- dev.DEBUG_MODE
--
if APP_CHANNEL == "none" or APP_CHANNEL == "luo" then
--	dev.DEBUG_MODE = false
end

-- 服务器信息全局缓存
globals.SERVERS_INFO = {}

-- 区服前缀显示
if getlanguage == 'br' or getlanguage == 'vn' then
globals.SERVER_MAP = {
	en = {
		name = "",
		order = 100,
	},
	vn = {
		name = "[UTC-3]",
		order = 101,
	},
}
else 
globals.SERVER_MAP = {
	en = {
		name = "",
		order = 101,
	},
	vn = {
		name = "[UTC-3]",
		order = 100,
	},
}
end