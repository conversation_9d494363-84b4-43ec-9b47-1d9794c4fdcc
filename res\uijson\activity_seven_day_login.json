{"classname": null, "name": null, "animation": {"classname": null, "name": "AnimationManager", "actionlist": []}, "dataScale": 1, "designHeight": 1440, "designWidth": 2560, "textures": [], "texturesPng": ["activity/seven_day_login/bg_qr.png", "activity/seven_day_login/btn_close.png", "activity/seven_day_login/panel_qr0.png", "activity/seven_day_login/tip_1.png", "activity/seven_day_login/btn_qr.png", "activity/seven_day_login/tip_0.png"], "version": "*******", "widgetTree": {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 34791427, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 654, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.002734375, "positionPercentY": 0.0152777778, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.4640625, "sizePercentY": 0.454166681, "sizeType": 0, "tag": 727812626, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1188, "x": -7, "y": 22, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "activity/seven_day_login/bg_qr.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 654, "scale9Width": 1188}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnClose", "ZOrder": 1, "actiontag": 2884644, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 73, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.414843738, "positionPercentY": 0.342361122, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.028125, "sizePercentY": 0.0506944433, "sizeType": 0, "tag": 727812637, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 72, "x": 1062, "y": 493, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": "activity/seven_day_login/btn_close.png", "plistFile": "", "resourceType": 0}, "fontName": "font/youmi1.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "activity/seven_day_login/btn_close.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": "activity/seven_day_login/btn_close.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 73, "scale9Width": 72, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 3, "actiontag": 51833409, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 850, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.176953122, "positionPercentY": -0.379166663, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.6070312, "sizePercentY": 0.5902778, "sizeType": 0, "tag": 727812634, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1554, "x": -453, "y": -546, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 1, "editorClipAble": false, "gravity": 0, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "iconItem", "ZOrder": 1, "actiontag": 53450112, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.199609369, "positionPercentY": 0.8833333, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.078125, "sizePercentY": 0.1388889, "sizeType": 0, "tag": 727812678, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": 511, "y": 1272, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBg", "ZOrder": 0, "actiontag": 22954200, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 225, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 0.9375, "sizeType": 0, "tag": 727812636, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1554, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 36, "capInsetsY": 36, "fileName": null, "fileNameData": {"path": "activity/seven_day_login/panel_qr0.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 225, "scale9Width": 1554}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textTian", "ZOrder": 1, "actiontag": 36973338, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 47, "colorG": 36, "colorR": 102, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.335263848, "positionPercentY": 0.020833334, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.03217503, "sizePercentY": 0.208333328, "sizeType": 0, "tag": 727812642, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 148, "x": -521, "y": 5, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "Day(s)", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textDi", "ZOrder": 1, "actiontag": 57124168, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 47, "colorG": 36, "colorR": 102, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.444015443, "positionPercentY": 0.020833334, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.03217503, "sizePercentY": 0.208333328, "sizeType": 0, "tag": 727812644, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 78, "x": -690, "y": 5, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "No.", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgDayBg", "ZOrder": 1, "actiontag": 31904287, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 95, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.3912484, "positionPercentY": 0.0458333343, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.06113256, "sizePercentY": 0.395833343, "sizeType": 0, "tag": 727812646, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 95, "x": -608, "y": 11, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "activity/seven_day_login/tip_1.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 95, "scale9Width": 95}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textDay", "ZOrder": 2, "actiontag": 65328429, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 55, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 89, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.3912484, "positionPercentY": 0.05, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0257400256, "sizePercentY": 0.333333343, "sizeType": 0, "tag": 727812648, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 44, "x": -608, "y": 12, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 80, "hAlignment": 0, "text": "1", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textGet", "ZOrder": 0, "actiontag": 50540081, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 47, "colorG": 36, "colorR": 102, "customProperty": "", "flipX": false, "flipY": false, "height": 67, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.493421048, "sizePercentY": 0.5217391, "sizeType": 0, "tag": 727812660, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 151, "x": 0, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 60, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnGetFree", "ZOrder": 1, "actiontag": 8293825, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 115, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.03732304, "positionPercentY": 0.141666666, "positionType": 0, "rotation": 0, "scaleX": 0.7, "scaleY": 0.7, "sizePercentX": 0.1956242, "sizePercentY": 0.479166657, "sizeType": 0, "tag": 727812658, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 304, "x": 58, "y": 34, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": "activity/seven_day_login/tip_0.png", "plistFile": "", "resourceType": 0}, "fontName": "font/youmi1.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "activity/seven_day_login/btn_qr.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": "activity/seven_day_login/btn_qr.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 115, "scale9Width": 304, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "listFree", "ZOrder": 0, "actiontag": 12154512, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.247104242, "positionPercentY": -0.158333331, "positionType": 0, "rotation": 0, "scaleX": 0.7, "scaleY": 0.7, "sizePercentX": 0.257400244, "sizePercentY": 0.8333333, "sizeType": 0, "tag": 727812719, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 400, "x": -384, "y": -38, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 3, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "listPaid", "ZOrder": 0, "actiontag": 9068361, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.136422142, "positionPercentY": -0.166666672, "positionType": 0, "rotation": 0, "scaleX": 0.7, "scaleY": 0.7, "sizePercentX": 0.257400244, "sizePercentY": 0.8333333, "sizeType": 0, "tag": 727812722, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 400, "x": 212, "y": -40, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 3, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textGet", "ZOrder": 0, "actiontag": 61445107, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 47, "colorG": 36, "colorR": 102, "customProperty": "", "flipX": false, "flipY": false, "height": 67, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.493421048, "sizePercentY": 0.5217391, "sizeType": 0, "tag": 727812726, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 151, "x": 0, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 60, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnGetPaid", "ZOrder": 1, "actiontag": 66479557, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 115, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.405619919, "positionPercentY": 0.12361113, "positionType": 0, "rotation": 0, "scaleX": 0.7, "scaleY": 0.7, "sizePercentX": 0.1956242, "sizePercentY": 0.479166657, "sizeType": 0, "tag": 727812725, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 304, "x": 630, "y": 29, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": "activity/seven_day_login/tip_0.png", "plistFile": "", "resourceType": 0}, "fontName": "font/youmi1.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "activity/seven_day_login/btn_qr.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": "activity/seven_day_login/btn_qr.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 115, "scale9Width": 304, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item", "ZOrder": 0, "actiontag": 3282622, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 240, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.241796881, "positionPercentY": 0.884722233, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.6070312, "sizePercentY": 0.166666672, "sizeType": 0, "tag": 727812635, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1554, "x": -619, "y": 1274, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textCountDown", "ZOrder": 1, "actiontag": 61307380, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 119, "colorG": 255, "colorR": 141, "customProperty": "", "flipX": false, "flipY": false, "height": 55, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.31640625, "positionPercentY": 0.272222221, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.103125, "sizePercentY": 0.0333333351, "sizeType": 0, "tag": 727812664, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 342, "x": 810, "y": 392, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 48, "hAlignment": 0, "text": "6 days 23:59:59", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "Panel", "ZOrder": 0, "actiontag": -1, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1440, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727812589, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 2560, "x": 1280, "y": 720, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 0, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 1, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}}