{"classname": null, "name": null, "animation": {"classname": null, "name": "AnimationManager", "actionlist": []}, "dataScale": 1, "designHeight": 1440, "designWidth": 2560, "textures": [], "texturesPng": ["3q/first/bg.png", "3q/first/front.png", "3q/first/bg_gift.png", "3q/first/name.png", "3q/first/btnNap.png", "3q/common/btn_guanbi.png"], "version": "*******", "widgetTree": {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 50576524, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 813, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5253906, "positionPercentY": 0.5159722, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 0.447656244, "sizePercentY": 0.564583361, "sizeType": 0, "tag": 727835987, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1146, "x": 1345, "y": 743, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/first/bg.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 813, "scale9Width": 1146}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "front", "ZOrder": 2, "actiontag": 39808586, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 799, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5253906, "positionPercentY": 0.5159722, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 0.396093756, "sizePercentY": 0.5548611, "sizeType": 0, "tag": 727835989, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1014, "x": 1345, "y": 743, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/first/front.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 799, "scale9Width": 1014}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_5", "ZOrder": 3, "actiontag": 62839538, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 120, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.393359363, "positionPercentY": 0.3638889, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 0.273046881, "sizePercentY": 0.0833333358, "sizeType": 0, "tag": 727835991, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 699, "x": 1007, "y": 524, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/first/bg_gift.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 120, "scale9Width": 699}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_6", "ZOrder": 3, "actiontag": 14933473, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 217, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.7234375, "positionPercentY": 0.7048611, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 0.03046875, "sizePercentY": 0.150694445, "sizeType": 0, "tag": 727835992, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 78, "x": 1852, "y": 1015, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/first/name.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 217, "scale9Width": 78}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "hero", "ZOrder": 1, "actiontag": 43801132, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1430, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0613281243, "positionPercentY": 0.000694444461, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.9375, "sizePercentY": 0.9930556, "sizeType": 0, "tag": 727835993, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 2400, "x": 157, "y": 1, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 3, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label", "ZOrder": 0, "actiontag": 22360170, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 244, "colorG": 240, "colorR": 197, "customProperty": "", "flipX": false, "flipY": false, "height": 41, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.05882353, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.294117659, "sizePercentY": 0.193277314, "sizeType": 0, "tag": 727835997, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 159, "x": 0, "y": -7, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 36, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnBuy", "ZOrder": 3, "actiontag": 36929719, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 119, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.396484375, "positionPercentY": 0.246527776, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 0.119531251, "sizePercentY": 0.08263889, "sizeType": 0, "tag": 727835994, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 306, "x": 1015, "y": 355, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "微软雅黑", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/first/btnNap.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 119, "scale9Width": 306, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnClose", "ZOrder": 3, "actiontag": 50869092, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 64, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.723828137, "positionPercentY": 0.85625, "positionType": 0, "rotation": 0, "scaleX": 2.5, "scaleY": 2.5, "sizePercentX": 0.025, "sizePercentY": 0.0444444455, "sizeType": 0, "tag": 727835995, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 64, "x": 1853, "y": 1233, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "微软雅黑", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/common/btn_guanbi.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 64, "scale9Width": 64, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 3, "actiontag": 7666456, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 160, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.253125, "positionPercentY": 0.308333337, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.3125, "sizePercentY": 0.111111112, "sizeType": 0, "tag": 727835996, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 900, "x": 591, "y": 444, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 5, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "Panel_24", "ZOrder": 0, "actiontag": -1, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1440, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727835986, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 2560, "x": 0, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 0, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 1, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}}