{"classname": null, "name": null, "animation": {"classname": null, "name": "AnimationManager", "actionlist": []}, "dataScale": 1, "designHeight": 1440, "designWidth": 2560, "textures": [], "texturesPng": ["3q/thiluyen/bg_01.png", "common/btn/btn_sz.png", "3q/common/btn_info.png", "3q/thiluyen/bg_xuanze_1.png", "GUI/image.png", "3q/thiluyen/bg_thiluyen_02.png", "3q/thiluyen/bg_thiluyentinhanh_02.png", "3q/thiluyen/txt_gxtgjrdytly.png", "GUI/button.png", "3q/thiluyen/bg_info_01.png", "3q/thiluyen/bg_gongneng_01.png", "3q/thiluyen/icon_doihinh.png", "3q/thiluyen/icon_thuoctinhtang.png", "3q/thiluyen/icon_cuahang.png", "3q/thiluyen/icon_thuonghang.png", "3q/thiluyen/bg_listicon.png", "3q/thiluyen/bg_info_02.png", "3q/thiluyen/bg_01.png", "3q/thiluyen/bg_info_05.png", "3q/common/btn_fanhui_2.png", "3q/common/btn_fanhui_1.png", "3q/thiluyen/bg_02.png", "GUI/image.png", "common/btn/btn_nomal_2.png", "3q/xichbich/fightbtn.png", "common/icon/radio_normal.png", "common/icon/radio_selected.png", "3q/thiluyen/bg_info_02.png", "3q/thiluyen/pic_point_active.png", "3q/thiluyen/pic_point_normal.png", "city/adventure/random_tower/bar_dt1.png", "common/title/title_pop.png", "common/btn/btn_close.png", "3q/thiluyen/bg_maintittle_01.png", "common/btn/btn_normal.png", "3q/thiluyen/bg_xuanze_2.png", "3q/pic_baoxiang_big_off.png", "3q/thiluyen/btn_big_sp.png", "common/icon/icon_diamond.png", "3q/thiluyen/btn_big_on.png", "3q/achievement/Claimed.png", "GUI/image.png", "city/adventure/random_tower/icon/box_orange.png", "city/adventure/random_tower/icon/icon_hn.png", "common/btn/btn_recharge.png", "city/adventure/random_tower/icon/icon_sj.png", "city/adventure/random_tower/logo_yck.png", "3q/thiluyen/bg_01.png", "3q/thiluyen/bg_info_04.png", "GUI/image.png", "city/drawcard/perview/logo_fjx.png", "common/btn/btn_sz.png", "3q/thiluyen/pic_zhuangshi_01.png", "3q/thiluyen/bg_info_03.png", "city/adventure/random_tower/img_sjtd.png", "3q/thiluyen/btn_big_sp.png", "common/icon/icon_diamond.png", "3q/thiluyen/btn_big_on.png", "3q/endless_tower/bg.png", "common/title/title_pop.png", "common/btn/btn_close.png", "common/btn/btn_normal.png", "3q/endless_tower/listbg.png", "GUI/image.png", "common/icon/logo_ylq.png", "3q/thiluyen/bg_info_rank.png", "common/title/title_pop.png", "common/btn/btn_close.png", "3q/thiluyen/bg_list_normal.png", "3q/rank/icon_jp.png", "3q/iconvip/icon_vip1.png", "3q/thiluyen/bg_list_active.png", "3q/thiluyen/tab_1_on.png", "3q/thiluyen/bg_info_point.png", "common/btn/btn_normal.png", "3q/thiluyen/bg_list_rank.png", "GUI/image.png", "common/icon/logo_ylq.png"], "version": "*******", "widgetTree": {"classname": "Panel", "name": null, "children": [{"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBG", "ZOrder": 1, "actiontag": 34678900, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1080, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.385, "positionPercentY": -0.14, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 9.75, "sizePercentY": 5.4, "sizeType": 0, "tag": 727810693, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1950, "x": -77, "y": -28, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 62, "capInsetsY": 31, "fileName": null, "fileNameData": {"path": "3q/thiluyen/bg_info_rank.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 1080, "scale9Width": 1950}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBG", "ZOrder": 1, "actiontag": 20054284, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 120, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.999420643, "sizePercentY": 1, "sizeType": 0, "tag": 727810695, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 1725, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 700, "capInsetsY": 60, "fileName": null, "fileNameData": {"path": "common/title/title_pop.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 120, "scale9Width": 1725}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnClose", "ZOrder": 2, "actiontag": 46270833, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 117, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.500579357, "positionPercentY": -0.05, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.06778679, "sizePercentY": 0.975, "sizeType": 0, "tag": 727810696, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 117, "x": 864, "y": -6, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "common/btn/btn_close.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 117, "scale9Width": 117, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textTitle1", "ZOrder": 3, "actiontag": 13784314, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 237, "colorG": 252, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 67, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.464078784, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.12688297, "sizePercentY": 0.558333337, "sizeType": 0, "tag": 727810697, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 219, "x": -801, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 60, "hAlignment": 0, "text": "Ranking", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textTitle2", "ZOrder": 3, "actiontag": 49544889, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 237, "colorG": 252, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.39455387, "positionPercentY": -0.0583333336, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.07358053, "sizePercentY": 0.375, "sizeType": 0, "tag": 727810698, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 127, "x": -681, "y": -7, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "ranking", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "title", "ZOrder": 2, "actiontag": 23685915, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 120, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 2.535, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 8.63, "sizePercentY": 0.6, "sizeType": 0, "tag": 727810694, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1726, "x": 0, "y": 507, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 2, "actiontag": 43250817, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 750, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -4.075, "positionPercentY": -1.99, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 8.15, "sizePercentY": 3.75, "sizeType": 0, "tag": 727810699, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1630, "x": -815, "y": -398, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 1, "editorClipAble": false, "gravity": 2, "innerHeight": 0, "innerWidth": 0, "itemMargin": 31, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBG", "ZOrder": 1, "actiontag": 59542993, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 215, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.997546, "sizePercentY": 0.9907834, "sizeType": 0, "tag": 727810705, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1626, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 129, "capInsetsY": 108, "fileName": null, "fileNameData": {"path": "3q/thiluyen/bg_list_normal.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 215, "scale9Width": 1626}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgIcon", "ZOrder": 2, "actiontag": 33946734, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 101, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.419018418, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.07116564, "sizePercentY": 0.4654378, "sizeType": 0, "tag": 727810706, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 116, "x": -683, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/rank/icon_jp.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 101, "scale9Width": 116}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textRank2", "ZOrder": 3, "actiontag": 66227810, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 0, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.415950924, "positionPercentY": 0.004608295, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 6.134969e-06, "sizePercentY": 4.60829469e-05, "sizeType": 0, "tag": 727810715, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 0, "x": -678, "y": 1, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 60, "hAlignment": 0, "text": "", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textRank1", "ZOrder": 3, "actiontag": 21926369, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 0, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.415950924, "positionPercentY": -0.0322580636, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 6.134969e-06, "sizePercentY": 4.60829469e-05, "sizeType": 0, "tag": 727810718, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 0, "x": -678, "y": -7, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 65, "hAlignment": 0, "text": "", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "head", "ZOrder": 2, "actiontag": 27262051, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 185, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.296319, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.113496929, "sizePercentY": 0.852534533, "sizeType": 0, "tag": 727810707, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 185, "x": -483, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textName", "ZOrder": 2, "actiontag": 10226915, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.2, "positionPercentY": 0.101382487, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0748466253, "sizePercentY": 0.207373276, "sizeType": 0, "tag": 727810712, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 122, "x": -326, "y": 22, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "Trainer", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgVip", "ZOrder": 2, "actiontag": 37629522, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 156, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.00245398772, "positionPercentY": 0.110599078, "positionType": 0, "rotation": 0, "scaleX": 0.799999952, "scaleY": 0.799999952, "sizePercentX": 0.0957055241, "sizePercentY": 0.718894, "sizeType": 0, "tag": 727815558, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 156, "x": 4, "y": 24, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/iconvip/icon_vip1.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 156, "scale9Width": 156}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textLvNote", "ZOrder": 2, "actiontag": 43345798, "anchorPointX": 1, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.176073626, "positionPercentY": -0.244239628, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0251533743, "sizePercentY": 0.207373276, "sizeType": 0, "tag": 727810708, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 41, "x": -287, "y": -53, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "Lv", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textLv", "ZOrder": 2, "actiontag": 24521449, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 0, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.176687121, "positionPercentY": -0.225806445, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 6.134969e-06, "sizePercentY": 4.60829469e-05, "sizeType": 0, "tag": 727810710, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 0, "x": -288, "y": -49, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textPoint", "ZOrder": 2, "actiontag": 8525729, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 0, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.181595087, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 6.134969e-06, "sizePercentY": 4.60829469e-05, "sizeType": 0, "tag": 727810714, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 0, "x": 296, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 60, "hAlignment": 0, "text": "", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textProgressNote", "ZOrder": 2, "actiontag": 66487246, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.3576687, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0803681, "sizePercentY": 0.207373276, "sizeType": 0, "tag": 727815245, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 131, "x": 583, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "Layer 2", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textProgress", "ZOrder": 2, "actiontag": 47392222, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.4177914, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.03374233, "sizePercentY": 0.207373276, "sizeType": 0, "tag": 727815247, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 55, "x": 681, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "1/1", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item", "ZOrder": 5, "actiontag": 35707187, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 217, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 1.21, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 8.15, "sizePercentY": 1.085, "sizeType": 0, "tag": 727810704, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1630, "x": 0, "y": 242, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textRank", "ZOrder": 2, "actiontag": 33685918, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -3.395, "positionPercentY": 1.975, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.51, "sizePercentY": 0.25, "sizeType": 0, "tag": 727810700, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 102, "x": -679, "y": 395, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 44, "hAlignment": 0, "text": "Rank", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textName", "ZOrder": 2, "actiontag": 42109833, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -1.27, "positionPercentY": 1.975, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.68, "sizePercentY": 0.25, "sizeType": 0, "tag": 727810702, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 136, "x": -254, "y": 395, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 44, "hAlignment": 0, "text": "Trainer", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textPoint", "ZOrder": 2, "actiontag": 11569089, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 1.48, "positionPercentY": 1.97, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.605, "sizePercentY": 0.25, "sizeType": 0, "tag": 727810703, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 121, "x": 296, "y": 394, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 44, "hAlignment": 0, "text": "Points", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textProgress", "ZOrder": 2, "actiontag": 38255654, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 3.1, "positionPercentY": 1.975, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.71, "sizePercentY": 0.25, "sizeType": 0, "tag": 727815243, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 142, "x": 620, "y": 395, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 44, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON><PERSON> độ", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBG", "ZOrder": 1, "actiontag": 44240428, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 86, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.00981593, "sizePercentY": 0.955555558, "sizeType": 0, "tag": 727810722, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1646, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 51, "capInsetsY": 43, "fileName": null, "fileNameData": {"path": "3q/thiluyen/bg_list_active.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 86, "scale9Width": 1646}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textRank", "ZOrder": 2, "actiontag": 55323121, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.4184049, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.104294479, "sizePercentY": 0.5, "sizeType": 0, "tag": 727810723, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 170, "x": -682, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "Unranked", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textName", "ZOrder": 2, "actiontag": 48401170, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 0, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.20184049, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 6.134969e-06, "sizePercentY": 0.000111111105, "sizeType": 0, "tag": 727810728, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 0, "x": -329, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textPoint", "ZOrder": 2, "actiontag": 48729562, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 0, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.182822078, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 6.134969e-06, "sizePercentY": 0.000111111105, "sizeType": 0, "tag": 727810730, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 0, "x": 298, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 60, "hAlignment": 0, "text": "", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textProgressNote", "ZOrder": 2, "actiontag": 54925974, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.3582822, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0803681, "sizePercentY": 0.5, "sizeType": 0, "tag": 727810725, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 131, "x": 584, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "Layer 2", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textProgress", "ZOrder": 2, "actiontag": 59601233, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.4184049, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.03374233, "sizePercentY": 0.5, "sizeType": 0, "tag": 727810726, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 55, "x": 682, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "1/1", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "down", "ZOrder": 8, "actiontag": 7342435, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 90, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -2.43, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 8.15, "sizePercentY": 0.45, "sizeType": 0, "tag": 727810721, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1630, "x": 0, "y": -486, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img", "ZOrder": 0, "actiontag": 31438173, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 141, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.49444443, "positionPercentY": 0.466666669, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.3, "sizePercentX": 0.6666667, "sizePercentY": 0.783333361, "sizeType": 0, "tag": 727819647, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 120, "x": 89, "y": 84, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/thiluyen/tab_1_on.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 141, "scale9Width": 120}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "txt", "ZOrder": 0, "actiontag": 17795254, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 7, "colorG": 55, "colorR": 107, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5555556, "positionPercentY": 0.516666651, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.08333337, "sizePercentY": 0.2777778, "sizeType": 0, "tag": 727819648, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 195, "x": 100, "y": 93, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 44, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "rank", "ZOrder": 1, "actiontag": 33113998, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 180, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -5.23, "positionPercentY": 1.215, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.9, "sizePercentY": 0.9, "sizeType": 0, "tag": 727819646, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 180, "x": -1046, "y": 243, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img", "ZOrder": 0, "actiontag": 18578822, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 141, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.49444443, "positionPercentY": 0.466666669, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.3, "sizePercentX": 0.6666667, "sizePercentY": 0.783333361, "sizeType": 0, "tag": 727819662, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 120, "x": 89, "y": 84, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/thiluyen/tab_1_on.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 141, "scale9Width": 120}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "txt", "ZOrder": 0, "actiontag": 32991673, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 205, "colorG": 218, "colorR": 226, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5555556, "positionPercentY": 0.516666651, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5777778, "sizePercentY": 0.2777778, "sizeType": 0, "tag": 727819663, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 104, "x": 100, "y": 93, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 44, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "btnPoint", "ZOrder": 1, "actiontag": 16977394, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 180, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -5.23, "positionPercentY": 0.3, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.9, "sizePercentY": 0.9, "sizeType": 0, "tag": 727819661, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 180, "x": -1046, "y": 60, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "panelRank", "ZOrder": 0, "actiontag": 45318479, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.078125, "sizePercentY": 0.1388889, "sizeType": 0, "tag": 727819592, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": 0, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBG", "ZOrder": 1, "actiontag": 5722272, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1080, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.385, "positionPercentY": -0.14, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 9.75, "sizePercentY": 5.4, "sizeType": 0, "tag": 727819619, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1950, "x": -77, "y": -28, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 62, "capInsetsY": 31, "fileName": null, "fileNameData": {"path": "3q/thiluyen/bg_info_point.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 1080, "scale9Width": 1950}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBG", "ZOrder": 1, "actiontag": 45371013, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 120, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.04461182, "positionPercentY": -0.233333334, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727819621, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 1726, "x": -77, "y": -28, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 700, "capInsetsY": 60, "fileName": null, "fileNameData": {"path": "common/title/title_pop.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 120, "scale9Width": 1726}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnClose", "ZOrder": 2, "actiontag": 5685939, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 117, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.500579357, "positionPercentY": -0.0416666679, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.06778679, "sizePercentY": 0.975, "sizeType": 0, "tag": 727819622, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 117, "x": 864, "y": -5, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "common/btn/btn_close.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 117, "scale9Width": 117, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textTitle1", "ZOrder": 3, "actiontag": 51173455, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 237, "colorG": 252, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 67, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.464078784, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.09617613, "sizePercentY": 0.558333337, "sizeType": 0, "tag": 727819623, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 166, "x": -801, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 60, "hAlignment": 0, "text": "Points", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textTitle2", "ZOrder": 3, "actiontag": 3394136, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 237, "colorG": 252, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.39455387, "positionPercentY": -0.06666667, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0793742761, "sizePercentY": 0.375, "sizeType": 0, "tag": 727819624, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 137, "x": -681, "y": -8, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "<PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "title", "ZOrder": 2, "actiontag": 43420639, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 120, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 2.53, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 8.63, "sizePercentY": 0.6, "sizeType": 0, "tag": 727819620, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1726, "x": 0, "y": 506, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 2, "actiontag": 30547225, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 660, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -4.085, "positionPercentY": -1.53, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 8.15, "sizePercentY": 3.3, "sizeType": 0, "tag": 727819625, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1630, "x": -817, "y": -306, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 1, "editorClipAble": false, "gravity": 2, "innerHeight": 0, "innerWidth": 0, "itemMargin": 20, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textTodayNote", "ZOrder": 1, "actiontag": 55893555, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.489090919, "positionPercentY": 0.8074074, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.227272734, "sizePercentY": 0.333333343, "sizeType": 0, "tag": 727819627, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 375, "x": -807, "y": 109, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON><PERSON> tích luỹ hiện tại:", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textScore", "ZOrder": 1, "actiontag": 33279947, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.325454533, "positionPercentY": 0.8074074, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0133333337, "sizePercentY": 0.333333343, "sizeType": 0, "tag": 727819628, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 22, "x": -537, "y": 109, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "5", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNote", "ZOrder": 1, "actiontag": 31084067, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 116, "colorG": 130, "colorR": 135, "customProperty": "", "flipX": false, "flipY": false, "height": 35, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0.8148148, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.4709091, "sizePercentY": 0.259259254, "sizeType": 0, "tag": 727819629, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 777, "x": 0, "y": 110, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 30, "hAlignment": 0, "text": "(累计积分 = 今日积分 + 历史总积分 *0.08)", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 2, "actiontag": 11321148, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 160, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.2569697, "positionPercentY": -0.466666669, "positionType": 0, "rotation": 0, "scaleX": 0.8, "scaleY": 0.8, "sizePercentX": 0.6090909, "sizePercentY": 1.18518519, "sizeType": 0, "tag": 727819630, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1005, "x": -424, "y": -63, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 5, "innerHeight": 0, "innerWidth": 0, "itemMargin": 1, "vectorX": 0, "vectorY": -0.5}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNote", "ZOrder": 1, "actiontag": 44597790, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 0, "colorG": 63, "colorR": 101, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5903614, "sizePercentY": 0.467213124, "sizeType": 0, "tag": 727819632, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 196, "x": 0, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "Claim All", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnGet", "ZOrder": 2, "actiontag": 34781471, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 122, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.388484836, "positionPercentY": -0.05185185, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.201212123, "sizePercentY": 0.9037037, "sizeType": 0, "tag": 727819631, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 332, "x": 641, "y": -7, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 106, "capInsetsY": 61, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "common/btn/btn_normal.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": true, "scale9Height": 122, "scale9Width": 332, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textAwardNote", "ZOrder": 1, "actiontag": 65835792, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.489090919, "positionPercentY": 0.348148137, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.168484852, "sizePercentY": 0.333333343, "sizeType": 0, "tag": 727819633, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 278, "x": -807, "y": 47, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "Thưởng tích luỹ:", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "down", "ZOrder": 2, "actiontag": 58406178, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 135, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -2.325, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 8.25, "sizePercentY": 0.675, "sizeType": 0, "tag": 727819626, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1650, "x": 0, "y": -465, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item1", "ZOrder": 6, "actiontag": 11455315, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 190, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -1.575, "positionPercentY": 1.49, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.95, "sizePercentY": 0.95, "sizeType": 0, "tag": 727819634, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 190, "x": -315, "y": 298, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBG", "ZOrder": 1, "actiontag": 58679506, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 215, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.99141103, "sizePercentY": 0.986238539, "sizeType": 0, "tag": 727819636, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1616, "x": 0, "y": 0, "capInsetsHeight": 100, "capInsetsWidth": 700, "capInsetsX": 15, "capInsetsY": 5, "fileName": null, "fileNameData": {"path": "3q/thiluyen/bg_list_rank.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 215, "scale9Width": 1616}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 2, "actiontag": 55419526, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 190, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.252147228, "positionPercentY": -0.444954127, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5607362, "sizePercentY": 0.8715596, "sizeType": 0, "tag": 727819637, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 914, "x": -411, "y": -97, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 5, "innerHeight": 0, "innerWidth": 0, "itemMargin": 25, "vectorX": 0, "vectorY": -0.5}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNote", "ZOrder": 1, "actiontag": 1298623, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 0, "colorG": 63, "colorR": 101, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5565217, "sizePercentY": 0.467213124, "sizeType": 0, "tag": 727819639, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 128, "x": 0, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnGet", "ZOrder": 2, "actiontag": 59796615, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 122, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.387116551, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.1411043, "sizePercentY": 0.559633, "sizeType": 0, "tag": 727819638, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 230, "x": 631, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 106, "capInsetsY": 61, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "common/btn/btn_normal.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": true, "scale9Height": 122, "scale9Width": 230, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textScore", "ZOrder": 3, "actiontag": 64552444, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 237, "colorG": 252, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 0, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.4006135, "positionPercentY": 0.0275229365, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 6.134969e-06, "sizePercentY": 4.587156e-05, "sizeType": 0, "tag": 727819640, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 0, "x": -653, "y": 6, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 65, "hAlignment": 0, "text": "", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textScoreNote", "ZOrder": 3, "actiontag": 44101092, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 237, "colorG": 252, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 35, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.4006135, "positionPercentY": -0.2706422, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.101226993, "sizePercentY": 0.16055046, "sizeType": 0, "tag": 727819641, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 165, "x": -653, "y": -59, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 30, "hAlignment": 0, "text": "Points arrive", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgTexeBg", "ZOrder": 2, "actiontag": 61365508, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 32, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.4, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0490797535, "sizePercentY": 0.366972476, "sizeType": 0, "tag": 727819642, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 64, "x": -652, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "GUI/image.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 32, "scale9Width": 64}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgReceived", "ZOrder": 2, "actiontag": 15418662, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 107, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.387116551, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0656441748, "sizePercentY": 0.490825683, "sizeType": 0, "tag": 727819643, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 107, "x": 631, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/icon/logo_ylq.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 107, "scale9Width": 107}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item", "ZOrder": 5, "actiontag": 43802647, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 218, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 1.205, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 8.15, "sizePercentY": 1.09, "sizeType": 0, "tag": 727819635, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1630, "x": 0, "y": 241, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "awardItem", "ZOrder": 6, "actiontag": 4550236, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 160, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -4.66, "positionPercentY": -4.415, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.8, "sizePercentY": 0.8, "sizeType": 0, "tag": 727819644, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 160, "x": -932, "y": -883, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "pointTxt", "ZOrder": 3, "actiontag": 33495206, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -3.24, "positionPercentY": 1.975, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.465, "sizePercentY": 0.225, "sizeType": 0, "tag": 727819655, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 93, "x": -648, "y": 395, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "rewardTxt", "ZOrder": 3, "actiontag": 27090535, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.41, "positionPercentY": 1.975, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.16, "sizePercentY": 0.225, "sizeType": 0, "tag": 727819657, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 232, "x": 82, "y": 395, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "Phần Thưởng", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img", "ZOrder": 0, "actiontag": 15442702, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 141, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.49444443, "positionPercentY": 0.466666669, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.3, "sizePercentX": 0.6666667, "sizePercentY": 0.783333361, "sizeType": 0, "tag": 727819653, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 120, "x": 89, "y": 84, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/thiluyen/tab_1_on.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 141, "scale9Width": 120}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "txt", "ZOrder": 0, "actiontag": 4766594, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 7, "colorG": 55, "colorR": 107, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5555556, "positionPercentY": 0.516666651, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5777778, "sizePercentY": 0.2777778, "sizeType": 0, "tag": 727819654, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 104, "x": 100, "y": 93, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 44, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "point", "ZOrder": 1, "actiontag": 1715855, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 180, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -5.225, "positionPercentY": 0.335, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.9, "sizePercentY": 0.9, "sizeType": 0, "tag": 727819652, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 180, "x": -1045, "y": 67, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img", "ZOrder": 0, "actiontag": 45038082, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 141, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.49444443, "positionPercentY": 0.466666669, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.3, "sizePercentX": 0.6666667, "sizePercentY": 0.783333361, "sizeType": 0, "tag": 727819668, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 120, "x": 89, "y": 84, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/thiluyen/tab_1_on.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 141, "scale9Width": 120}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "txt", "ZOrder": 0, "actiontag": 16527477, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 205, "colorG": 218, "colorR": 226, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5555556, "positionPercentY": 0.516666651, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.08333337, "sizePercentY": 0.2777778, "sizeType": 0, "tag": 727819669, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 195, "x": 100, "y": 93, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 44, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "btnRank", "ZOrder": 1, "actiontag": 20858878, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 180, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -5.225, "positionPercentY": 1.21, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.9, "sizePercentY": 0.9, "sizeType": 0, "tag": 727819667, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 180, "x": -1045, "y": 242, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "panelPoint", "ZOrder": 0, "actiontag": 32433417, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.078125, "sizePercentY": 0.1388889, "sizeType": 0, "tag": 727819645, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 200, "x": 0, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "Panel_115", "ZOrder": 0, "actiontag": -1, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1440, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 2117, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 2560, "x": 1280, "y": 720, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 0, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}}