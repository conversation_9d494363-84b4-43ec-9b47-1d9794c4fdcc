{"classname": null, "name": null, "animation": {"classname": null, "name": "AnimationManager", "actionlist": []}, "dataScale": 1, "designHeight": 1440, "designWidth": 2560, "textures": [], "texturesPng": ["common/box/box_pop_bg.png", "common/title/title_pop.png", "common/btn/btn_close.png", "3q/guild/area/bg_list02.png", "3q/guild/area/bg_qizhi.png", "common/btn/btn_normal.png", "city/pvp/arena/icon_jf_jjc.png"], "version": "*******", "widgetTree": {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBG", "ZOrder": 1, "actiontag": 38094504, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1080, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.0194444451, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.6742188, "sizePercentY": 0.75, "sizeType": 0, "tag": 727810745, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1726, "x": 0, "y": -28, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 62, "capInsetsY": 31, "fileName": null, "fileNameData": {"path": "common/box/box_pop_bg.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 1080, "scale9Width": 1726}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBG", "ZOrder": 1, "actiontag": 53325338, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 120, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.10081112, "sizePercentY": 1, "sizeType": 0, "tag": 727810747, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1900, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 700, "capInsetsY": 60, "fileName": null, "fileNameData": {"path": "common/title/title_pop.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 120, "scale9Width": 1900}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnClose", "ZOrder": 2, "actiontag": 10165994, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 117, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.4889919, "positionPercentY": 0.1, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.06778679, "sizePercentY": 0.975, "sizeType": 0, "tag": 727810748, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 117, "x": 844, "y": 12, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "common/btn/btn_close.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 117, "scale9Width": 117, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textTitle1", "ZOrder": 3, "actiontag": 24385084, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 145, "colorG": 231, "colorR": 254, "customProperty": "", "flipX": false, "flipY": false, "height": 61, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.321552724, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.187137887, "sizePercentY": 0.5083333, "sizeType": 0, "tag": 727810749, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 323, "x": -555, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 54, "hAlignment": 0, "text": "Thưởng Hạng", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textTitle2", "ZOrder": 3, "actiontag": 59986642, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 145, "colorG": 231, "colorR": 254, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.232329085, "positionPercentY": -0.0166666675, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0793742761, "sizePercentY": 0.375, "sizeType": 0, "tag": 727810750, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 137, "x": -401, "y": -2, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "<PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "title", "ZOrder": 2, "actiontag": 39289027, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 120, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0.352777779, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.6742188, "sizePercentY": 0.0833333358, "sizeType": 0, "tag": 727810746, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1726, "x": 0, "y": 508, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 2, "actiontag": 3008871, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 860, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.317968756, "positionPercentY": -0.3125, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.63671875, "sizePercentY": 0.5972222, "sizeType": 0, "tag": 727810751, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1630, "x": -814, "y": -450, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 1, "editorClipAble": false, "gravity": 2, "innerHeight": 0, "innerWidth": 0, "itemMargin": 31, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBG", "ZOrder": 1, "actiontag": 36725861, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 215, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.997546, "sizePercentY": 0.9907834, "sizeType": 0, "tag": 727810783, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1626, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 129, "capInsetsY": 108, "fileName": null, "fileNameData": {"path": "3q/guild/area/bg_list02.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 215, "scale9Width": 1626}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgTextBG", "ZOrder": 2, "actiontag": 26141106, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 208, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.3981595, "positionPercentY": 0.02764977, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.202453986, "sizePercentY": 0.95852536, "sizeType": 0, "tag": 727810832, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 330, "x": -649, "y": 6, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/guild/area/bg_qizhi.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 208, "scale9Width": 330}}, {"classname": "LabelBMFont", "name": null, "children": [], "options": {"__type": "LabelBMFontSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "LabelBMFont", "name": "textRank", "ZOrder": 3, "actiontag": 44285664, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabelBMFont", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 0, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.398773, "positionPercentY": 0.0230414756, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 6.134969e-06, "sizePercentY": 4.60829469e-05, "sizeType": 0, "tag": 727817354, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 0, "x": -650, "y": 5, "fileNameData": {"path": "font/arenaFont.fnt", "plistFile": "", "resourceType": 0}, "text": ""}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNote", "ZOrder": 3, "actiontag": 56470587, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 237, "colorG": 252, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 35, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.3981595, "positionPercentY": -0.244239628, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.138650313, "sizePercentY": 0.161290318, "sizeType": 0, "tag": 727810790, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 226, "x": -649, "y": -53, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 30, "hAlignment": 0, "text": "Ranking reached", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNote", "ZOrder": 1, "actiontag": 54767232, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 36, "colorG": 84, "colorR": 126, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.551724136, "sizePercentY": 0.467213124, "sizeType": 0, "tag": 727810834, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 128, "x": 0, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnGet", "ZOrder": 2, "actiontag": 64007105, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 122, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.393865019, "positionPercentY": -0.004608295, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.142331287, "sizePercentY": 0.562212, "sizeType": 0, "tag": 727810833, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 232, "x": 642, "y": -1, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 106, "capInsetsY": 61, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi1.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "common/btn/btn_normal.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": true, "scale9Height": 122, "scale9Width": 232, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 2, "actiontag": 20594931, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 190, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.198773012, "positionPercentY": -0.437788, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5153374, "sizePercentY": 0.875576, "sizeType": 0, "tag": 727810835, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 840, "x": -324, "y": -95, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 5, "innerHeight": 0, "innerWidth": 0, "itemMargin": 35, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textCost", "ZOrder": 2, "actiontag": 36018229, "anchorPointX": 1, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 0, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.392638028, "positionPercentY": 0.2580645, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 6.134969e-06, "sizePercentY": 4.60829469e-05, "sizeType": 0, "tag": 727810845, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 0, "x": 640, "y": 56, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgIcon", "ZOrder": 2, "actiontag": 47398598, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 55, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.415950924, "positionPercentY": 0.267281115, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0349693261, "sizePercentY": 0.253456235, "sizeType": 0, "tag": 727810846, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 57, "x": 678, "y": 58, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "city/pvp/arena/icon_jf_jjc.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 55, "scale9Width": 57}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item", "ZOrder": 5, "actiontag": 38822154, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 217, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.00703125, "positionPercentY": -0.8354167, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.63671875, "sizePercentY": 0.150694445, "sizeType": 0, "tag": 727810782, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1630, "x": -18, "y": -1203, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNote", "ZOrder": 2, "actiontag": 53905506, "anchorPointX": 1, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.21484375, "positionPercentY": -0.34375, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.138671875, "sizePercentY": 0.0395833328, "sizeType": 0, "tag": 727810842, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 355, "x": -550, "y": -495, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 50, "hAlignment": 0, "text": "Hạng cao nhất:", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textRank", "ZOrder": 2, "actiontag": 56745122, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.21484375, "positionPercentY": -0.34375, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0109375, "sizePercentY": 0.0395833328, "sizeType": 0, "tag": 727810844, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 28, "x": -550, "y": -495, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 50, "hAlignment": 0, "text": "0", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "Panel_115", "ZOrder": 0, "actiontag": -1, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1440, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 2117, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 2560, "x": 1280, "y": 720, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 0, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}}