-- Test script to verify fox coin system implementation
-- This script tests the LoginGift fox coin purchase flow

local function testFoxCoinSystem()
    print("=== Testing Fox Coin System ===")
    
    -- Test 1: Check recharges.lua configuration
    print("\n1. Testing recharges.lua configuration:")
    local rechargeInfo = csv.recharges[102]
    if rechargeInfo then
        print("  ✓ Recharge ID 102 found")
        print("  - Fox coin cost (rmbDisplay):", rechargeInfo.rmbDisplay)
        print("  - Diamond reward (rmb):", rechargeInfo.rmb)
        print("  - Type:", rechargeInfo.type)
    else
        print("  ✗ Recharge ID 102 not found!")
        return false
    end
    
    -- Test 2: Check loginweal.lua configuration
    print("\n2. Testing loginweal.lua configuration:")
    local loginGiftConfig = csv.yunying.loginweal[10260] -- Day 1
    if loginGiftConfig then
        print("  ✓ LoginGift Day 1 config found")
        print("  - Recharge ID:", loginGiftConfig.rechargeID)
        print("  - Paid Award:", loginGiftConfig.paidAward)
        if loginGiftConfig.rechargeID == 102 then
            print("  ✓ Recharge ID matches expected value")
        else
            print("  ✗ Recharge ID mismatch!")
            return false
        end
    else
        print("  ✗ LoginGift Day 1 config not found!")
        return false
    end
    
    -- Test 3: Check purchase flow logic
    print("\n3. Testing purchase flow logic:")
    local testConfig = {
        rmb = nil,  -- This should trigger payDirect flow
        rechargeID = 102
    }
    
    if not testConfig.rmb then
        print("  ✓ Purchase flow: payDirect (fox coin)")
        print("  - Will use gGameApp:payDirect() with rechargeID:", testConfig.rechargeID)
    else
        print("  ✗ Purchase flow: requestServer (diamond) - incorrect!")
        return false
    end
    
    -- Test 4: Check fox coin cost calculation
    print("\n4. Testing fox coin cost calculation:")
    local function calculateFoxCoinCost(rechargeID)
        local rechargeInfo = csv.recharges[rechargeID]
        if rechargeInfo and rechargeInfo.rmbDisplay then
            return tonumber(rechargeInfo.rmbDisplay)
        end
        return 0
    end
    
    local foxCoinCost = calculateFoxCoinCost(102)
    print("  - Fox coin cost for recharge ID 102:", foxCoinCost)
    if foxCoinCost > 0 then
        print("  ✓ Fox coin cost calculation working")
        
        -- Test price increase
        local increasedCost = math.floor(foxCoinCost * 1.5)
        print("  - Increased cost (1.5x):", increasedCost)
        print("  ✓ Price increase calculation working")
    else
        print("  ✗ Fox coin cost calculation failed!")
        return false
    end
    
    print("\n=== All Tests Passed! ===")
    print("Fox coin system is properly configured and ready to use.")
    return true
end

-- Run the test
testFoxCoinSystem()
