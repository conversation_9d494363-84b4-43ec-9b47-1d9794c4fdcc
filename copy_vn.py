import os
import pandas as pd

# <PERSON><PERSON><PERSON>ng dẫn thư mục chứa các file CSV cần xử lý
folder_path = r'E:\3Q\3q_client_dev\OkamiTool\csv_dev'

def insert_column(df, insert_after_col, new_col_name, values):
    """Chèn một cột mới vào ngay sau cột insert_after_col"""
    cols = df.columns.tolist()
    if insert_after_col not in cols:
        return df  # Không tồn tại cột gốc thì bỏ qua

    insert_idx = cols.index(insert_after_col) + 1
    # Chia dataframe thành 2 phần: trái và phải của vị trí chèn
    left = df.iloc[:, :insert_idx].copy()
    right = df.iloc[:, insert_idx:]
    left[new_col_name] = values
    df_new = pd.concat([left, right], axis=1)
    return df_new

# <PERSON><PERSON><PERSON>t qua toàn bộ thư mục và các thư mục con
for root, dirs, files in os.walk(folder_path):
    for filename in files:
        if filename.lower().endswith('.csv'):
            file_path = os.path.join(root, filename)
            try:
                # Đọc file CSV với encoding phù hợp
                try:
                    df = pd.read_csv(file_path, encoding='utf-8')
                except UnicodeDecodeError:
                    df = pd.read_csv(file_path, encoding='utf-8-sig')

                modified = False
                idn_columns = [col for col in df.columns if col.endswith('_idn')]

                for idn_col in idn_columns:
                    base_col = idn_col[:-4]           # Ví dụ: "name" từ "name_idn"
                    vn_col = base_col + '_vn'

                    if base_col not in df.columns:
                        continue  # Bỏ qua nếu không có cột gốc

                    if vn_col in df.columns:
                        df[vn_col] = df[base_col]
                        modified = True
                    else:
                        df = insert_column(df, base_col, vn_col, df[base_col])
                        modified = True

                if modified:
                    df.to_csv(file_path, index=False, encoding='utf-8-sig')
                    print(f"✅ Đã xử lý: {file_path}")
                else:
                    print(f"ℹ️ Không thay đổi: {file_path}")

            except Exception as e:
                print(f"❌ Lỗi với file {file_path}: {e}")
