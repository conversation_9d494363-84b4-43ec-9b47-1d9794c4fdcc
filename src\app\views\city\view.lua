local YY_TYPE = game.YYHUODONG_TYPE_ENUM_TABLE

local CHANNELS = {
	news = {name = gLanguageCsv.system, color = cc.c4b(238, 115, 143, 255), colorHex = "#C0xFFEE738F#"},
	world = {name = gLanguageCsv.world, color = cc.c4b(139, 175, 223, 255), colorHex = "#C0xFF8BAFDF#"},
	union = {name = gLanguageCsv.guild, color = cc.c4b(204, 143, 223, 255), colorHex = "#C0xFFCC8FDF#"},
	team = {name = gLanguageCsv.formTeam, color = cc.c4b(236, 183, 43, 255), colorHex = "#C0xFFECB72B#"},
	huodong = {name = gLanguageCsv.activity, color = cc.c4b(255, 94, 66, 255), colorHex = "#C0xFFFF5E42#"},
	private = {name = gLanguageCsv.privateChat, color = cc.c4b(204, 143, 223, 255), colorHex = "#C0xFFCC8FDF#"},
}
local CHAT_PAGE_IDX = {
	news = 1,
	world = 2,
	union = 3,
	team = 4,
	huodong = 5,
	private = 6,
}
local SCHEDULE_TAG_SYSOPEN_TAG = 1000

local function isClickToday(key)
	local flag = userDefault.getForeverLocalKey(key, "")
	local today = time.getTodayStr()
	return flag == today
end

local function isClickVal(key, val)
	local flag = userDefault.getForeverLocalKey(key, "")
	return flag == tostring(val)
end


local function delURLConfig(str)
	local result = false
	local flags = tonumber(string.sub(str, 2), 2)
	if not flags then
		return str
	end
	local len = string.len(str)
	if len < 5 then
		return str
	end

	local idx = len - 5 + 1
	if string.sub(str, idx, idx) == "0" then
		return str
	end

	local result = string.sub(str, 1, idx - 1) .. "0"
	result = result .. string.sub(str, idx + 1)

	return result
end


local function getLeftBtnsData()
    local leftBtns = {
        {
            key = "cardReborn",
            unlockKey = "cardReborn",
            icon = "city/main/3Q/TopL/signIn.png",
            name = gLanguageCsv.cardRebirth,
            viewName = "city.card.rebirth.view",
        },
        {
            key = "mail",
            icon = "city/main/3Q/TopL/mail.png",
            name = gLanguageCsv.signIn,
            viewName = "city.mail",
            redHint = {
                class = "red_hint",
                props = {
                    specialTag = "mail",
                    onNode = function(node)
                        node:xy(90, 90)
                    end,
                }
            }
        },
        {
            key = "friend",
            unlockKey = "friend",
            icon = "city/main/3Q/TopL/friend.png",
            name = gLanguageCsv.friend,
            viewName = "city.friend",
            func = function(cb)
                local friendView = require("app.views.city.friend")
                local showType, param = friendView.initFriendShowType()
                friendView.sendProtocol(showType, param, cb)
            end,
            redHint = {
                class = "red_hint",
                props = {
                    specialTag = {
                        "friendStaminaRecv",
                        "friendReqs",
                    },
                    onNode = function(node)
                        node:xy(90, 90)
                    end,
                }
            }
        },
        {
            key = "rank",
            unlockKey = "rank",
            icon = "city/main/3Q/TopL/rank.png",
            name = gLanguageCsv.rank,
            viewName = "city.rank",
            styles = {full = true},
            func = function (cb)
                gGameApp:requestServer("/game/rank",function (tb)
                    cb(tb.view.rank)
                end, "fight", 0, 10)
            end
        },
    }

    local isGrowGuideHidden = userDefault.getForeverLocalKey("grow_guide_hidden", false)
    if not isGrowGuideHidden then
        table.insert(leftBtns, {
            key = "grow_guide",
            unlockKey = "huongDan",
            icon = "city/main/3Q/TopL/grow_guide.png",
            name = gLanguageCsv.signIn,
            viewName = "city.grow_guide",
            redHint = {
                class = "red_hint",
                props = {
                    state = bindHelper.self("growGuideRedHint"),
                    onNode = function(node)
                        node:xy(90, 90)
                    end,
                }
            }
        })
    end

    return leftBtns
end

local function getLeftBottomBtnsData()
	return {
		{
			key = "chatPrivataly",
			icon = "city/icon_xinxi.png",
			viewName = "city.chat.privataly",
			func = function(cb)
				local msg = gGameModel.messages:read('private')
				if itertools.isempty(msg) then
					gGameUI:showTip(gLanguageCsv.noPrivateChatList)
					return
				end
				cb()
			end,
		},{
			key = "mail",
			viewName = "city.mail",
		},{
			key = "setting",
			viewName = "city.setting.view",
		},
	}
end

local function getMainBtnsData()
	return {
		{
			key = "pvp",
			name = gLanguageCsv.pvp,
			viewName = "city.adventure.pvp",
			styles = {full = true},
			func = function(cb)
				cb("pvp")
			end,
		},{
			key = "pve",
			name = gLanguageCsv.adventure,
			viewName = "city.adventure.pve",
			styles = {full = true},
			func = function(cb)
				cb("pve")
			end,
		},{
			key = "gate",
			name = gLanguageCsv.gate,
			viewName = "city.gate.new_view",
			styles = {full = true}
		},{
			key = "union",
			unlockKey = "union",
			icon = "city/main/icon_gh@.png",
			name = gLanguageCsv.guild,
			func = function ()
				jumpEasy.jumpTo("union")
			end,
			redHint = {
				class = "red_hint",
				props = {
					specialTag = {
						"unionTraining",
						"unionSystemRedPacket",
						"unionMemberRedPacket",
						"unionSendedRedPacket",
						"unionDailyGift",
						"unionLobby",
						"unionContribute",
						"unionFuben",
						"unionFragDonate",
						"unionFightSignUp",
						"unionAnswer",
					},
					onNode = function(node)
						print("union red hint")
						--node:xy(162, 162)
					end,
				}
			}
		}
	}
end
local ViewBase = cc.load("mvc").ViewBase
local CityView = class("CityView", ViewBase)
local ActivityView = require "app.views.city.activity.view"

CityView.RESOURCE_FILENAME = "city.json"
CityView.RESOURCE_BINDING = {
	["bgPanel"] = "bgPanel",
	["item"] = "item",
	["item_0"] = "item_0",
	["leftTopPanel.bar"] = {
		varname = "bar",
		binds = {
			event = "extend",
			class = "loadingbar",
			props = {
				data = bindHelper.self("expSlider"),
				maskImg = "3q/mask_bar_red.png"
			},
		}
	},
	["activityItem"] = "activityItem",
	["activityItem.name"] = {
		binds = {
			{
				event = "effect",
				data = {outline = {color = cc.c4b(73, 53, 57, 255), size = 3}}
			},
		}
	},
	["activityItem.labelTime"] = {
		binds = {
			{
				event = "effect",
				data = {outline = {color = cc.c4b(73, 53, 57, 255), size = 3}}
			},
		}
	},
	["leftTopPanel"] = "leftTopPanel",

	["leftTopPanel.head"] = {
		binds = {
			{
				event = "touch",
				methods = {ended = bindHelper.defer(function(view)
					return view:onItemClick(nil, view.leftBottomBtns[3])
				end)}
		 	},
			{
				event = "extend",
				class = "role_logo",
				props = {
					logoId = bindHelper.self("logo"),
					frameId = bindHelper.self("frame"),
					level = false,
					vip = false,
				},
			},
		}
	},
	["leftTopPanel.name"] = "nameTxt",
	["leftTopPanel.level"] = "levelTxt",
	["leftTopPanel.vip"] = "roleVip",
	["leftTopPanel.vipPanel"] = {
		varname = "vipPanel",
		binds = {
			event = "touch",
			methods = {ended = function(view)
				view:stackUI("city.recharge", nil, {full = true}, {vipClick = true})
			end
			},
		},
	},
	["leftTopPanel.vipPanel.vipNum"] = {
		varname = "vipNum",
		binds = {
			event = "text",
			idler = bindHelper.model("role", "vip_level"),
		},
	},
	["leftTopPanel.power"] = "power",
	["leftTopPanel.powerNum"] = {
		varname = "powerNum",
		binds = {
			event = "text",
			idler = bindHelper.model("role", "top6_fighting_point"),
		},
	},
	["leftTopPanel.yeartime"] = "yeartime",
	["leftTopPanel.daytime"] = "daytime",
	["rightPanel"] = "rightPanel",
	["rightPanel.titlePanel"] = "titlePanel",
	["rightPanel.achievementPanel"] = "achievementPanel",
	["rightPanel.achievementPanel.txt"] = {
		binds = {
			{
				event = "effect",
				data = {outline = {color = cc.c4b(109, 54, 186, 255), size = 4}}
			},
		}
	},
	["rightPanel.achievementPanel.txt1"] = {
		binds = {
			{
				event = "effect",
				data = {outline = {color = cc.c4b(109, 54, 186, 255), size = 4}}
			},
		}
	},
	["rightPanel.achievementPanel.bg"] = {
		binds = {
			event = "touch",
			methods = {ended = bindHelper.defer(function(view)
				view:onClickAchievement()
			end)}
		},
	},
	["rightPanel.titlePanel.txt"] = {
		binds = {
			{
				event = "effect",
				data = {outline = {color = ui.COLORS.NORMAL.PINK, size = 4}}
			},
		}
	},
	["rightPanel.titlePanel.txt1"] = {
		binds = {
			{
				event = "effect",
				data = {outline = {color = ui.COLORS.NORMAL.PINK, size = 4}}
			},
		}
	},
	["rightPanel.titlePanel.bg"] = {
		binds = {
			event = "touch",
			methods = {ended = bindHelper.defer(function(view)
				idlereasy.do_(function (val)
					for i,v in ipairs(val) do
						if v.key == "title_book" then
							return view:onItemClick(nil, v)
						end
					end
				end, view.developBtns)
			end)}
		},
	},
	["leftTopPanel.rechargeItem"] = {
		varname = "rechargeItem",
		binds = {
			{
				event = "touch",
				methods = {ended = bindHelper.self("onRechargeClick")}
			},{
				event = "extend",
				class = "multi_text_effect",
				props = {
					data = gLanguageCsv.recharge,
					effects = {
						{outline = {color=cc.c4b(54,66,82,255), size=6}},
						{outline = {color=cc.c4b(255,255,255,255), size=12}},
					},
					onNode = function(node)
						node:xy(140, 72):z(5)
					end,
				},
			},
		},
	},
	["leftPanel"] = "leftPanel",
	["leftPanel.list"] = {
		varname = "leftList",
		binds = {
			event = "extend",
			class = "listview",
			props = {
				data = bindHelper.self("leftBtns"),
				growGuideRedHint = bindHelper.self("growGuideRedHint"),
				item = bindHelper.self("item_0"),
				margin = 30,
				onItem = function(list, node, k, v)
					node:name(v.key)
					node:get("icon"):texture(v.icon)
					node:removeChildByName("name")
					local name = label.create(v.name, {
						color = ui.COLORS.NORMAL.DEFAULT,
						fontSize = 42,
						fontPath = "font/youmi1.ttf",
					})
					text.addEffect(name, {outline = {color=ui.COLORS.NORMAL.WHITE, size=4}})



					if v.redHint and (v.key ~= "union" or gGameModel.union_training) then
						bind.extend(list, node, v.redHint)
					end
					bind.touch(list, node, {methods = {
						ended = functools.partial(list.clickCell, v)
					}})
					uiEasy.updateUnlockRes(v.unlockKey, node, {justRemove = not v.unlockKey, pos = cc.p(140, 140)})
						:anonyOnly(list, list:getIdx(k))
				end,

			},
			handlers = {
				clickCell = bindHelper.self("onItemClick"),
			},
		},
	},
	["leftBottomPanel.panel"] = "leftBottomBtnPanel",
	["leftBottomPanel.panel.btnTalk"] = {
		varname = "btnTalk",
		binds = {
			{
				event = "touch",
				methods = {ended = bindHelper.defer(function(view)
					return view:onItemClick(nil, view.leftBottomBtns[1])
				end)}
			},
			{
				event = "extend",
				class = "red_hint",
				props = {
					state = bindHelper.self("talkRedHint"),
					onNode = function(node)
						node:xy(210, 76)
					end,
				}
			}
		},
	},
	["leftBottomPanel.panel.btnMsg"] = {
		varname = "btnMsg",
		binds = {
			{
				event = "touch",
				methods = {ended = bindHelper.defer(function(view)
					gGameApp:slientRequestServer("/game/sync")
					return view:onItemClick(nil, view.leftBottomBtns[2])
				end)}
			},
		},
	},
	["leftBottomPanel.panel.btnSet"] = {
		varname = "btnSetting",
		binds = {
			event = "touch",
			methods = {ended = bindHelper.defer(function(view)
				return view:onItemClick(nil, view.leftBottomBtns[3])
			end)}
		},
	},
	["leftBottomPanel.talkPanel.baseTalkItem"] = "baseTalkItem",
	["leftBottomPanel.talkPanel.ChatList"] = {
		varname = "ChatList",
		binds = {
			{
				event = "extend",
				class = "listview",
				props = {
					data = bindHelper.self("chatContents"),
					item = bindHelper.self("baseTalkItem"),
					onItem = function(list, node, k, v)
						local function CheckData(data)
							local str = "#I" .. "city/chat/pic_" .. data.channel .. ".png" .. "-78-36#" .. "  "
							if itertools.first(game.MESSAGE_SHOW_TYPE[data.type], 1) then
								str = str .. data.msg
							elseif itertools.first(game.MESSAGE_SHOW_TYPE[data.type], 2) then
								str = str .. "#C0xFFE7E0CF#" .. data.role.name .. ":  "
								local emojiKey = string.match(data.msg, "%[(%w+)%]")
								if gEmojiCsv[emojiKey] then
									str = str .. "#I" .. gEmojiCsv[emojiKey].resource .. "-50-50#"
								else
									str = str .. "#C0xFFD17379#" .. data.msg
								end
							elseif itertools.first(game.MESSAGE_SHOW_TYPE[data.type], 4) then
								str = str .. "#C0xFFE7E0CF#" .. data.role.name .. ":  "
								str = str .. "#C0xFFD17379#" .. data.msg
							end
							return str
						end
						local Info = node:getChildByName("click")
						local content = CheckData(v);
						local richText = rich.createWithWidth(content, 35, nil, 720, 10, cc.p(0, 0))
						richText:anchorPoint(0, 0):xy(20, 10):addTo(node)
						node:size(node:size().width, richText:size().height + 20)
						bind.touch(list, node, {methods = {
							ended = functools.partial(list.itemClick, k, v)
						}})
					end,
					preloadBottom = true,

				},
				handlers = {
					itemClick = bindHelper.self("onTalkClick"),
				},
			},
			{
				event = "touch",
				methods = {ended = function(view)
					view:onTalkClickCustom(2) -- mở world chat khi bấm vào khoảng không vô vọng
				end}
			}
		},
	},
	["rightPanel.showList"] = "topShowList",
	["rightPanel.btnExpand"] = {
		varname = "activityExpandBtn",
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onActivityExpandClick")}
		},
	},
	["rightPanel.panel"] = "activityPanel",
	["rightPanel.panel.list"] = {
		varname = "activityList",
		binds = {
			event = "extend",
			class = "listview",
			props = {
				data = bindHelper.self("activityBtns"),
				dataOrderCmp = function(a, b)
					return a.sortWeight > b.sortWeight
				end,
				item = bindHelper.self("activityItem"),
				margin = ACTIVITY_LIST_MARGIN,
				itemAction = {isAction = true},
				onBeforeBuild = function(list)
					list:enableSchedule():unScheduleAll()
				end,
				onItem = function(list, node, k, v)
					node:name(v.actionExpandName)
					local childs = node:multiget("icon", "labelTime", "name")
					childs.icon:texture(v.icon)
					childs.name:text(v.name)
					if v.endTime then
						CityView.setCountdown(list, childs.labelTime, {endTime = v.endTime, tag = v.tag})
					else
						childs.labelTime:hide()
					end
					if v.redHint then
						bind.extend(list, node, v.redHint)
					end
					bind.touch(list, node, {methods = {
						ended = functools.partial(list.clickCell, v)
					}})
				end,
			},
			handlers = {
				clickCell = bindHelper.self("onItemClick"),
			},
		},
	},
	["rightPanel.panel2"] = "activityPanel2",
	["rightPanel.panel2.list"] = {
		varname = "activityList2",
		binds = {
			event = "extend",
			class = "listview",
			props = {
				data = bindHelper.self("activityBtns2"),
				dataOrderCmp = function(a, b)
					return a.sortWeight > b.sortWeight
				end,
				item = bindHelper.self("activityItem"),
				margin = ACTIVITY_LIST_MARGIN,
				itemAction = {isAction = true},
				onBeforeBuild = function(list)
					list:enableSchedule():unScheduleAll()
				end,
				onItem = function(list, node, k, v)
					local childs = node:multiget("icon", "labelTime", "name")
					childs.icon:texture(v.icon)
					childs.name:text(v.name)
					if v.endTime then
						CityView.setCountdown(list, childs.labelTime, {endTime = v.endTime, tag = v.tag})
					else
						childs.labelTime:hide()
					end
					if v.redHint then
						bind.extend(list, node, v.redHint)
					end
					bind.touch(list, node, {methods = {
						ended = functools.partial(list.clickCell, v)
					}})
				end,
			},
			handlers = {
				clickCell = bindHelper.self("onItemClick"),
			},
		},
	},
	["developPanel.Image_70"] = "bgItem",
	["developPanel.Image_70.Image_73"] = "bgItem2",
	["centerBottomPanel"] = "centerBottomPanel",
	["centerBottomPanel.Image_70.Image_73"] = "spineGirl",
	["centerBottomPanel.Image_70"] = "bgAction",
	["centerBottomPanel.actionList"] = {
		varname = "actionList",
		binds = {
			event = "extend",
			class = "listview",
			props = {
				data = bindHelper.self("actionBtns"),
				item = bindHelper.self("item"), 
				actionBtnsCount = bindHelper.self("actionBtnsCount"),
				margin =40,
				onItem = function(list, node, k, v)
					node:name(v.key)
					node:get("icon"):texture(v.icon)
					node:get("bg"):texture("city/main/panel_icon.png")
					node:get("bg"):scale(1)
					node:removeChildByName("name")
					local fontSize = 40
					local name = label.create(v.name, {
						color = ui.COLORS.NORMAL.DEFAULT,
						fontSize = fontSize,
						fontPath = "font/youmi1.ttf",
					})
					text.addEffect(name, {color = cc.c4b(255, 242, 236, 255), outline = {color=cc.c4b(81, 59, 57, 255)}})
					name:addTo(node, 5, "name")
						:xy(84, 20)
					if v.redHint then
						bind.extend(list, node, v.redHint)
					end
					if v.actionExpandName then
						bind.touch(list, node, {methods = {
							ended = functools.partial(list.clickDevelop, node, v)
						}})
					else
						bind.touch(list, node, {methods = {
							ended = functools.partial(list.clickCell, v)
						}})
					end
					uiEasy.updateUnlockRes(v.unlockKey, node, {justRemove = not v.unlockKey,pos = cc.p(144, 144)})
						:anonyOnly(list, list:getIdx(k))
				end,
				onAfterBuild = function (list)
					list:setItemAlignCenter()
					local count = #list:getChildren()
					list.actionBtnsCount:set(count)
					list.OkamiFix()
				end
			},
			handlers = {
				OkamiFix= bindHelper.self("OkamiFix"),
				clickCell = bindHelper.self("onItemClick"),
				clickDevelop = bindHelper.self("onItemDevelopClick"),
			},
		},
	},
	["developPanel"] = {
		varname = "developPanel",
		binds = {
			event = "visible",
			idler = bindHelper.self("actionExpandName"),
			method = function(name)
				return name ~= ""
			end,
		}
	},
	["developPanel.listItem"] = "listItem",
	["developPanel.bg"] = "developBg",
	["developPanel.developList"] = {
		varname = "developList",
		binds = {
			event = "extend",
			class = "tableview",
			props = {
				data = bindHelper.self("developBtns"),
				item = bindHelper.self("listItem"),
				cell = bindHelper.self("item"),
				columnSize = 4,
				backupCached = false,
				onCell = function(list, node, k, v)
					node:scaleY(-1)
					node:name(v.key)
					node:get("icon"):texture(v.icon)
						:y(84)
					node:get("bg"):hide()
					bind.touch(list, node, {methods = {
						ended = functools.partial(list.clickCell, v)
					}})
					node:removeChildByName("name")
					local name = label.create(v.name, {
						color = ui.COLORS.NORMAL.DEFAULT,
						fontSize = matchLanguage({"kr"}) and 34 or 40,
						fontPath = "font/youmi1.ttf",
					})
					text.addEffect(name, {outline = {color=ui.COLORS.NORMAL.WHITE, size=4}})
					name:addTo(node, 5, "name")
						:xy(90, 20)

					if name:width() > 180 then
						name:scale(180 / name:width())
					end
					if v.key == 'mega' then
						node:get("icon"):scale(0.85)
					end
					if v.redHint then
						bind.extend(list, node, v.redHint)
					end
					v.unlockRes = uiEasy.updateUnlockRes(v.unlockKey, node, {justRemove = not v.unlockKey, pos = cc.p(140, 140)})
										:anonyOnly(list, list:getIdx(k))
				end,
				dataOrderCmp = function (a, b)
					local keyA = gUnlockCsv[a.unlockKey]
					local keyB = gUnlockCsv[b.unlockKey]
					if keyA and keyB then
						if csv.unlock[keyA].startLevel == csv.unlock[keyB].startLevel then
							return keyA < keyB
						end
						return csv.unlock[keyA].startLevel < csv.unlock[keyB].startLevel
					elseif not keyA and not keyB then
						return false
					else
						return keyA == nil
					end
				end,
				onAfterBuild = function (list)
					list:adaptTouchEnabled()

				end
			},
			handlers = {
				clickCell = bindHelper.self("onItemClick"),
			},
		},
	},
	["rightBottomPanel"] = "rightBottomPanel",
	["rightBottomPanel.mainList"] = "mainList",
	["rightBottomPanel.btnPvp"] = {
		varname = "itemPvp",
		binds = {
			{
				event = "touch",
				methods = {ended = bindHelper.self("onBtnPvpClick")}
			},{
				event = "extend",
				class = "red_hint",
				props = {
					specialTag = {
						"arenaAward",
						"crossArenaAward",
						"onlineFightAward",
					},
					onNode = function(node)
						node:xy(125, 135)
					end,
				}
			}
		},
	},
	["rightBottomPanel.btnPvp.namePanel"] = {
		binds = {
			event = "extend",
			class = "multi_text_effect",
			props = {
				data = gLanguageCsv.pvp,
				effects = {
					{outline = {color = ui.COLORS.NORMAL.WHITE, size = 4}}
				},
				labelParams = {
					color = ui.COLORS.NORMAL.DEFAULT,
					fontSize = 60,
					fontPath = "font/youmi1.ttf",
				},
				onNode = function(node)
					node:xy(95, 50):z(5)
				end
			}

		}
	},
	["rightBottomPanel.btnAdventure"] = {
		varname = "itemAdventure",
		binds = {
			{
				event = "touch",
				methods = {ended = bindHelper.self("onBtnAdventureClick")}
			},{
				event = "extend",
				class = "red_hint",
				props = {
					specialTag = {
						"dispatchTask",
						"randomTower",
						"randomTowerPoint",
						"gymChallenge",
						"cloneBattle",
						"braveChallengeAch",
					},
					listenData = {
						sign = game.BRAVE_CHALLENGE_TYPE.common,
					},
					onNode = function(node)
						node:xy(125, 135)
					end,
				}
			}
		},
	},
	["rightBottomPanel.btnAdventure.namePanel"] = {
		binds = {
			event = "extend",
			class = "multi_text_effect",
			props = {
				data = gLanguageCsv.adventure,
				effects = {
					{outline = {color = ui.COLORS.NORMAL.WHITE, size = 4}}
				},
				labelParams = {
					color = ui.COLORS.NORMAL.DEFAULT,
					fontSize = 60,
					fontPath = "font/youmi1.ttf",
				},
				onNode = function(node)
					node:xy(95, 50):z(5)
				end
			},
		}
	},
	["rightBottomPanel.btnPve"] = {
		varname = "itemPve",
		binds = {
			{
				event = "touch",
				clicksafe = true,
				methods = {ended = bindHelper.defer(function(view)
					return view:onItemClick(nil, view.mainBtns[3])
				end)}
			},
			{
				event = "extend",
				class = "red_hint",
				props = {
					specialTag = "pve",
					onNode = function(node)
						node:xy(190, 210)
					end,
				}
			}
		},
	},
	["rightBottomPanel.btnPve.namePanel"] = {
		binds = {
			event = "extend",
			class = "multi_text_effect",
			props = {
				data = gLanguageCsv.gate,
				effects = {
					{outline = {color = ui.COLORS.NORMAL.WHITE, size = 4}}
				},
				labelParams = {
					color = ui.COLORS.NORMAL.DEFAULT,
					fontSize = 60,
					fontPath = "font/youmi1.ttf",
				},
				onNode = function(node)
					node:xy(95, 50):z(5)
				end
			},
		}
	},
	["rightBottomPanel.btnGuild"] = {
		varname = "itemGuild",
		binds = {
			{
				event = "touch",
				clicksafe = true,
				methods = {ended = bindHelper.defer(function(view)
					return view:onItemClick(nil, view.mainBtns[4])
				end)}
			},
			{
				event = "extend",
				class = "red_hint",
				props = {
					specialTag = {
						"crossUnionFight",
						"unionSystemRedPacket",
						"unionMemberRedPacket",
						"unionSendedRedPacket",
						--"trainingRedHint",
						"unionDailyGift",
						"unionLobby",
						"unionContribute",
						"unionFuben",
						"unionFragDonate",
						"unionFightSignUp",
					},
					onNode = function(node)
						node:xy(140, 140)
					end,
				}
			}
		},
	},
	["rightPanel.onlineGiftPanel"] = "onlineGiftPanelItem",
	["activityTip"] = "activityTip",
	["rightPanel.go"] = "go",
	["effectPanel"] = "effectPanel",
	["activityTip.textNote1"] = "textNote1",
	["activityTip.textNote2"] = "textNote2",
	["rightBottomPanel.growGuide"] = "growGuide",
	["rightBottomPanel.growGuide.textNote1"] = {
		binds = {
			event = "effect",
			data = {outline = {color = ui.COLORS.OUTLINE.DEFAULT, size = 4}}
		},
	},
	["cardImg"] = {
		binds = {
			event = "extend",
			class = "role_figure",
			props = {
				data = bindHelper.model("role", "figure"),
				onNode = function(node)
					node:z(7)
						:y(580)
				end,
				spine = true,
				onSpine = function(spine)
					spine:scale(2.5)
						 :y(-1200)
				end,
			},
		}
	},
	-- ["cardImg"] = {
	-- 	binds = {
	-- 		{
	-- 			event = "extend",
	-- 			class = "role_figure",
	-- 			props = {
	-- 				data = bindHelper.model("role", "figure"),
	-- 				onNode = function(node)
	-- 					node:z(7)
	-- 						:y(580)
	-- 				end,
	-- 				spine = true,
	-- 				onSpine = function(spine)
	-- 					spine:scale(2.5)
	-- 						 :y(-1200)
	-- 				end,
	-- 			},
	-- 		},
	-- 		{
	-- 			event = "touch",
	-- 			methods = {
	-- 				ended = function(view)
	-- 					local figureId = gGameModel.role:read("figure")
	-- 					local cfg = gRoleFigureCsv[figureId]
	-- 					if cfg then
	-- 						view:getResourceNode():removeChildByName("speechBubble")
	-- 						local talkTexts = {cfg.talk1, cfg.talk2}
	-- 						local randomText = talkTexts[math.random(1, #talkTexts)]

	-- 						if randomText and randomText ~= "" then
	-- 							local bubble = ccui.ImageView:create("3q/bg_qipao_01.png")
	-- 								:setAnchorPoint(cc.p(0.5, 0))
	-- 								:setScale(1.2)
	-- 								:setName("speechBubble")
	-- 								:opacity(0)

	-- 							local textLabel = ccui.Text:create(randomText, "font/youmi1.ttf", 28)
	-- 								:setAnchorPoint(cc.p(0.5, 0.5))
	-- 								:setTextAreaSize(cc.size(400, 0))
	-- 								:setTextHorizontalAlignment(cc.TEXT_ALIGNMENT_CENTER)
	-- 								:setColor(cc.c3b(73, 53, 57))

	-- 							bubble:addTo(view:getResourceNode():getChildByName("cardImg"), 100)
	-- 								:xy(view:getResourceNode():getChildByName("cardImg"):getContentSize().width - 200, view:getResourceNode():getChildByName("cardImg"):getContentSize().height) -- Position above character

	-- 							textLabel:addTo(bubble)
	-- 								:xy(bubble:getContentSize().width/2, bubble:getContentSize().height/2 + 20)

	-- 							bubble:runAction(cc.Sequence:create(
	-- 								cc.FadeIn:create(0.2),
	-- 								cc.DelayTime:create(4.0),
	-- 								cc.FadeOut:create(0.5),
	-- 								cc.CallFunc:create(function() 
	-- 									bubble:removeFromParent() 
	-- 								end)
	-- 							))
	-- 						end
	-- 					end
	-- 				end
	-- 			},
	-- 		},
	-- 	},
	-- },
	["rightBottomPanel.world"] = {
		varname = "world",
		binds = {
			event = "touch",
			methods = { ended = bindHelper.self("onWorld") }
		},
	},
	["rightBottomPanel.world.textNote2"] = {
		binds = {
			event = "effect",
			data = {outline = {color = cc.c4b(99, 67, 74, 255), size = 4}}
		},
	},
	["marqueePanel"] = {
		varname = "marqueePanel",
		binds = {
			event = "extend",
			class = "marquee",
		}
	},
	["carouselPanel"] = "carouselPanel",
	["carouselListPanel"] = "carouselListPanel",
	["carouselListPanel.close"] = {
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onCloseBtn")},
		},
	},
	["carouselListPanel.list"] = "carouselList",
	["carouselListPanel.item"] = "carouselItem",
}

function CityView:safePerformWithDelay(callback, delay)
    local weakSelf = self
    local scheduler = cc.Director:getInstance():getScheduler()
    local scheduleEntry = nil
    
    scheduleEntry = scheduler:scheduleScriptFunc(function()
        scheduler:unscheduleScriptEntry(scheduleEntry)
        
        if weakSelf and not tolua.isnull(weakSelf) and weakSelf:getParent() then
            callback()
        end
    end, delay or 0.1, false)
    
    return scheduleEntry
end

function CityView:onCreate()
	gGameUI.topuiManager:createView("city", self):init()
	self:enableSchedule()
	self:initModel()
	self.actionBtnsCount = idler.new(0)
	self.chatContents = idlers.newWithMap({})
	self.bannerList = idlers.new({})
	local subInject = {
		"app.views.city.view_scene",
		"app.views.city.view_action",
		"app.views.city.view_activity",
	}
	for _, name in ipairs(subInject) do
		local inject = require(name)
		inject(CityView)
	end

	idlereasy.when(self.vipHide, function(_, vipHide)
		self.roleVip:visible(not vipHide)
		self.vipNum:visible(not vipHide)
	end)

	idlereasy.when(self.allChannel, function(_, messages)
        local chatMessages = {}
        for _, v in ipairs(messages) do
            if v.channel == "world" or v.channel == "union" or v.channel == "private" then
                table.insert(chatMessages, v)
            end
        end

        self.chatContents:update(chatMessages)
    end)

	self.btnSetting:hide()

	idlereasy.any({self.level, self.roleName}, function (obj, level, name)
		self.levelTxt:text(level)
		self.nameTxt:text(name)
		text.addEffect(self.nameTxt, {color=cc.c4b(255,255,255,255)})
	end)


	self.topShowList:setScrollBarEnabled(false)

	self.actionExpandName = idler.new("")

	self.autoNewPlayerWealId = idler.new(0)

	self:initData()


	self:initGrowPanel()
	self:achievementTip()

	self.autoPopBoxInfo = {}
	self:initPoster()

	local lastSignInDay = gGameModel.monthly_record:read("last_sign_in_day")
	if lastSignInDay < time.getNowDate().day then
		table.insert(self.autoPopBoxInfo, {viewName = "city.sign_in", params = {true}})
	end

	self:initAutoNewPlayerWeal()
	self:checkPopBox(true)

	local curMusicIdx = userDefault.getForeverLocalKey("musicIdx", 1)
	local cfg = csv.citysound[curMusicIdx]
	if cfg then
		audio.playMusic(cfg.path)
	else
		printWarn("music index not exist",curMusicIdx)
	end

	uiEasy.updateUnlockRes(gUnlockCsv.lichLuyen, self.itemAdventure, {pos = cc.p(self.itemAdventure:getContentSize().width / 2, self.itemAdventure:getContentSize().height / 2)})
	uiEasy.updateUnlockRes(gUnlockCsv.dienVo, self.itemPvp, {pos = cc.p(self.itemPvp:getContentSize().width / 2, self.itemPvp:getContentSize().height / 2)})
	uiEasy.updateUnlockRes(gUnlockCsv.union, self.itemGuild, {pos = cc.p(self.itemGuild:getContentSize().width / 2, self.itemGuild:getContentSize().height / 2)})

	self.isRefresh = idler.new(false)
	idlereasy.any({self.level, self.isRefresh}, function(_, level, isRefresh)
		self:unSchedule(SCHEDULE_TAG_SYSOPEN_TAG)
		local cfgT = {}
		local date = time.getNowDate()
		local curTime = date.hour * 100 + date.min
		local wday = date.wday
		wday = wday == 1 and 7 or wday - 1
		local nextTime = math.huge
		local function isOK(v)
			if v.reminder ~= 2 and v.roundKey then

				local roundState = gGameModel.role:read(v.roundKey)
				if roundState ~= v.roundState then
					return false
				end
			elseif v.reminder == 2 then
				local roundState = gGameModel.role:read(v.roundKey)
				if roundState == nil or tostring(roundState) == v.roundState then
					return false
				end
			end

			if v.unionlevel then
				local unionId = gGameModel.role:read("union_db_id")
				local unionLv = gGameModel.union:read("level")
				if not unionId or unionLv < v.unionlevel then
					return false
				end
			end

			if dataEasy.serverOpenDaysLess(v.serverday) then
				return false
			end
			if not itertools.include(v.openseq, wday) then
				return false
			end

			if curTime < v.startTime or curTime >= v.endTime then
				return false
			end
			return true
		end

		for k,v in orderCsvPairs(csv.sysopen) do
			if v.feature == "" or dataEasy.isUnlock(v.feature) then
				if v.reminder == 0
					or (v.reminder == 1 and not isClickToday("sysOpen" .. v.sighid))
					or (v.reminder == 2 and not isClickVal("sysOpen" .. v.sighid, gGameModel.role:read(v.roundKey))) then
					if isOK(v) then
						table.insert(cfgT, v)
					end
				end

				local todayNum = tonumber(time.getTodayStr())
				if csvSize(v.startTimes) > 0 then
					for _, startTime in csvMapPairs(v.startTimes) do
						local dt = time.getNumTimestamp(todayNum, startTime/100, startTime%100) - time.getTime()
						if dt < 0 then
							dt = dt + 3600 * 24
						end
						nextTime = math.min(nextTime, dt)
					end
				else
					local dt = time.getNumTimestamp(todayNum, v.startTime/100, v.startTime%100) - time.getTime()
					if dt < 0 then
						dt = dt + 3600 * 24
					end
					nextTime = math.min(nextTime, dt)
				end
			end
		end

		table.sort(cfgT, function(a, b)
			return a.priority > b.priority
		end)

		self:initActivityTip(cfgT[1])
		self:schedule(function()
			self.isRefresh:notify()
			return false
		end, nextTime, nextTime, SCHEDULE_TAG_SYSOPEN_TAG)
	end)

	self:enableMessage():registerMessage("adapterNotchScreen", function(flag)
		adaptUI(self:getResourceNode(), "city.json", flag)
	end)
	self._stackUIViewExitHandler = function(_, parentName)
        if parentName == "city.view" then
            local weakSelf = self
            
            local scheduler = cc.Director:getInstance():getScheduler()
            local scheduleEntry = nil
            
            scheduleEntry = scheduler:scheduleScriptFunc(function()
                scheduler:unscheduleScriptEntry(scheduleEntry)
                
                if weakSelf and not tolua.isnull(weakSelf) and weakSelf:getParent() then
                    weakSelf:checkPopBox()
                    weakSelf:checkAchievementForGameUI()
                    if weakSelf.isRefresh then
                        weakSelf.isRefresh:notify()
                    end
                end
            end, 0, false)
        end
    end
    
    self:registerMessage("stackUIViewExit", self._stackUIViewExitHandler)

	adapt.oneLinePos(self.power, self.powerNum, cc.p(-20,0), "left")
	self.powerNum:setAnchorPoint(cc.p(0, 0.5))

	self:setGameSyncTimer()

	self:setHorseRaceTimer()

	self:specialGiftLink()

	self:setTimeLabel()

	self.talkRedHint = idler.new(false)
	self.chatPrivatalyLastId = gGameModel.forever_dispatch:getIdlerOrigin("chatPrivatalyLastId")
	idlereasy.any({self.friendMessage, self.chatPrivatalyLastId}, function (_, msg, chatPrivatalyLastId)
		local msgSize = itertools.size(msg)
		local lastMsg = msg[msgSize]
		if msg and msgSize ~= 0 and lastMsg.id > chatPrivatalyLastId and not lastMsg.isMine then
			self.talkRedHint:set(true, true)
		else
			self.talkRedHint:set(false)
		end
	end)

	if device.platform == "windows" then

		for _, card in gGameModel.cards:pairs() do
			local cardId = card:read("card_id")
			if not csv.cards[cardId] then
				gGameUI:showDialog({content = string.format("数据中包含未开放的卡牌%d, 检查本地 language 与服务器是否一致", cardId)})
				break
			end
		end
	end

	self.expSlider = idler.new(0)
	local maxRoleLv = table.length(gRoleLevelCsv)
	idlereasy.any({self.level, self.levelExp}, function(_, level, levelExp)
		local percent = 100
		if level < maxRoleLv then
			percent = cc.clampf(100 * levelExp / gRoleLevelCsv[level].levelExp, 0, 100)
		end
		self.expSlider:set(percent)
	end)

	self.isGrowGuideAnimating = false

	widget.addAnimation(self.spineGirl, "effect3q/minigirl.skel", "effect_loop", 10):scale(3)

	self.carouselListPanel:hide()


	self:initActivityCarousel()

	self.activityExpandBtn:setVisible(false)
end

function CityView:onClose()
    self:unregisterMessage("stackUIViewExit")
    self:unregisterMessage("adapterNotchScreen")
    
    if self._activityScrollTimer then
        cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self._activityScrollTimer)
        self._activityScrollTimer = nil
    end

    if self._activityUpdateTimer then
        cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self._activityUpdateTimer)
        self._activityUpdateTimer = nil
    end
    
    if self.autoPopBoxInfo then
        self.autoPopBoxInfo = {}
    end

    self.onlineGiftPanel = nil

    ViewBase.onClose(self)
end

function CityView:onCloseBtn()
	local arrow = self.carouselListPanel:get("arrow")
	local origX = self.carouselList:getPositionX()
	local origY = self.carouselList:getPositionY()
	local aorigX = arrow:getPositionX()
	local aorigY = arrow:getPositionY()
	local offset = 300
	self.carouselListPanel:stopAllActions()
	self.carouselListPanel:setCascadeOpacityEnabled(true)
	self.carouselListPanel:setOpacity(255)
	self.carouselListPanel:runAction(cc.FadeOut:create(0.5))

	self.carouselList:stopAllActions()
	self.carouselList:setPosition(origX, origY)
	self.carouselList:setScale(1.2)
	self.carouselList:setAnchorPoint(cc.p(0, 0))

	local moveAction = cc.EaseBackOut:create(cc.MoveTo:create(0.2, cc.p(origX - offset, origY)))
	local scaleAction = cc.ScaleTo:create(0.2, 0, 1.2)
	local spawnAction = cc.Spawn:create(moveAction, scaleAction)
	local hideAction = cc.CallFunc:create(function()
		self.carouselList:setVisible(false)
	end)
	local sequence = cc.Sequence:create(spawnAction, hideAction)
	self.carouselList:runAction(sequence)

	arrow:stopAllActions()
	arrow:setPosition(aorigX, aorigY)
	arrow:runAction(cc.EaseBackOut:create(cc.MoveTo:create(0.5, cc.p(aorigX - offset, aorigY))))
	performWithDelay(self.carouselListPanel, function()
		self.carouselListPanel:hide()
	end, 0.5)

	self:unregisterMessage("stackUIViewExit")
end

function CityView:initModel()
	self.level = gGameModel.role:getIdler("level")
	self.roleName = gGameModel.role:getIdler("name")
	self.levelExp = gGameModel.role:getIdler("level_exp")
	self.vipLevel = gGameModel.role:getIdler("vip_level")
	self.vipHide = gGameModel.role:getIdler("vip_hide")
	self.logo = gGameModel.role:getIdler("logo")
	self.frame = gGameModel.role:getIdler("frame")
	self.allChannel = gGameModel.messages:getIdler("all")
	self.friendMessage = gGameModel.messages:getIdler('private')
	self.id = gGameModel.role:getIdler('id')
	self.figure = gGameModel.role:getIdler("figure")
	self.title = gGameModel.role:getIdler('title_id')
	self.titles = gGameModel.role:getIdler('titles')
	self.yyOpen = gGameModel.role:getIdler('yy_open')
	self.yyhuodongs = gGameModel.role:getIdler('yyhuodongs')
	self.yy_endtime = gGameModel.role:read("yy_endtime")
	self.citySprites = gGameModel.role:getIdler("city_sprites")
	self.spriteGiftTimes =  gGameModel.daily_record:getIdler("city_sprite_gift_times")


	self.growGuideRedHint = idler.new(false)
	self.growGuideData = gGameModel.role:getIdler("grow_guide")

	self.tasks = gGameModel.role:getIdler("achievement_tasks")
	self.achiBoxes = gGameModel.role:getIdler("achievement_box_awards")
	self.redHintRefresh = idler.new(true)
	self.crossFishingRound = gGameModel.role:getIdler("cross_fishing_round")
	self.fishingSelectScene = gGameModel.fishing:getIdler("select_scene")
	self.fishingIsAuto = gGameModel.fishing:getIdler("is_auto")
	self.reunion = gGameModel.role:getIdler("reunion")
end

function CityView:stackUI(name, handlers, styles, ...)
	gGameUI:stackUI(name, handlers, styles, ...)
end


function CityView:initData()
	self.leftBtns = getLeftBtnsData()
	self.leftBottomBtns = getLeftBottomBtnsData()
	self.mainBtns = getMainBtnsData()
	self:initActionData()
	self:initSceneData()
	self:initActivityData()
end

function CityView:onPersonalInfo()
	self:stackUI("city.personal.info", nil, {full = true})
end


function CityView:initTitlePanel()
	local originX = self.titlePanel:x()
	idlereasy.when(self.titles, function (_, val)
		if gGameModel.role.title_queue then
			self.titlePanel:show()
			transition.executeSequence(self.titlePanel, true)
				:moveTo(2, originX - self.titlePanel:width())
				:delay(1)
				:moveTo(2, originX)
				:done()
			gGameModel.role.title_queue = nil
		else
			self.titlePanel:hide()
		end
	end)
end

function CityView:onItemClick(list, v)
	if v.unlockKey and not dataEasy.isUnlock(v.unlockKey) then
		gGameUI:showTip(dataEasy.getUnlockTip(v.unlockKey))
		return
	end

	if v.func then
		v.func(function(...)
			local params = {}
			if v.independent == 4 or  v.independent == 5 then
				if v.independent == 4 then
					params = {"main", v.params[1]}
				else
					params = {"award", v.params[1]}
				end
			else
				params = clone(v.params or {})
				for _,v in ipairs({...}) do
					table.insert(params, v)
				end
			end
			self:stackUI(v.viewName, nil, v.styles, unpack(params))
		end, v.params or {})

	elseif v.viewName then
		if v.viewName == "city.grow_guide" then
            if self.isGrowGuideAnimating then
                return
            end
            
            self.isGrowGuideAnimating = true
            
            if self.growguidespine then
				if not tolua.isnull(self.growguidespine) and self.growguidespine:getParent() then
					self.growguidespine:removeFromParent()
				end
				self.growguidespine = nil
			end
            self.growguidespine = widget.addAnimationByKey(self.effectPanel, "effect3q/animation_uihuongdan.skel", "grow_guide", "effect", 6):scaleX(1.62):scaleY(1.62):y(-10):x(-10)
            
            self:safePerformWithDelay(function()
                if self.isGrowGuideAnimating then
                    self.isGrowGuideAnimating = false
                end
            end, 3)
            
            self:safePerformWithDelay(function()
                self.isGrowGuideAnimating = false
                self:stackUI(v.viewName, nil, v.styles, unpack(v.params or {}))
            end, 0.7)
        else
            self:stackUI(v.viewName, nil, v.styles, unpack(v.params or {}))
        end
	end
	self.actionExpandName:set("")
end

function CityView:onItemDevelopClick(list, node, v)
	local x, y = node:xy()
	local pos = list:convertToWorldSpace(cc.p(x, y))
	self.developPanel:x(pos.x)
	self.actionExpandName:modify(function(name)
		if v.actionExpandName ~= name then
			return true, v.actionExpandName
		end
		return true, ""
	end)
end

function CityView:onRechargeClick()
	self:stackUI("city.recharge", nil, {full = true})
end

function CityView:OkamiFix()
	local x = self.actionList:size()
	local xx = self.bgAction:size()
	local size = self.actionBtnsCount:read() * (250)
	print("OkamiFix", size)
	self.bgAction:size(size, xx.height)
	self.centerBottomPanel:size(x.width+300, x.height)
end


function CityView:onTalkClick(list,k,v)
	local idx = CHAT_PAGE_IDX[v.channel]
	self:stackUI("city.chat.view", nil, {clickClose = true}, idx)
end

function CityView:onTalkClickCustom(dx)
	self:stackUI("city.chat.view", nil, {clickClose = true}, dx)
end

function CityView:onTalkExpandClick()
	self.talkExpand:modify(function(val)
		return true, not val
	end)
end

function CityView:onActivityExpandClick()
	-- self.activityExpand:modify(function(val)
	-- 	return true, not val
	-- end)
end




function CityView:initPoster()

	local posterShow = userDefault.getForeverLocalKey("posterLoginShow", false, {rawKey = true})
	if posterShow then
		return
	end

	self.posterState = idler.new(false)
	local data = {}
	self.posterIds = {}

	local posterNotShowInfo = userDefault.getCurrDayKey("posterNotShowInfo", {})
	local newPlayerWeffare = gGameModel.currday_dispatch:getIdlerOrigin("newPlayerWeffare"):read()
	for _, id in ipairs(self.yyOpen:read()) do
		local cfg = csv.yunying.yyhuodong[id]
		if cfg.type == YY_TYPE.clientShow and cfg.independent == -1 then
			if not posterNotShowInfo[id] then
				local function insertPoster()
					self.posterIds[id] = true
					table.insert(data, {
						viewName = "city.activity.poster",
						id = id,
						sortWeight = cfg.sortWeight,
						params = {{cfg = cfg, state = self:createHandler("posterState")}},
					})
				end

				if cfg.clientParam.isReunion then
					local reunion = self.reunion:read()
					if reunion and reunion.role_type == 1 then
						for k, v in ipairs(self.yyOpen:read()) do
							if v == reunion.info.yyID and reunion.info.end_time > time.getTime() then
								insertPoster()
							end
						end
					end
				else
					insertPoster()
				end
			end
		end
	end
	if not itertools.isempty(data) then
		table.sort(data, function (a, b)
			if a.sortWeight ~= b.sortWeight then
				return a.sortWeight < b.sortWeight
			end
			return a.id < b.id
		end)
		data[#data].params[1].cb = self:createHandler("onPosterCb")
		arraytools.merge_inplace(self.autoPopBoxInfo, {data})
	end
end

function CityView:onPosterCb()
	if self.posterState:read() then
		userDefault.setCurrDayKey("posterNotShowInfo", self.posterIds)
	end
end


function CityView:checkPopBox(isFirst)
	local function normalPopBox()
        if dev.IGNORE_POPUP_BOX then
            return
        end
        if isFirst and self.hasCheckPopBoxFirst then
            return
        end
        self.hasCheckPopBoxFirst = true

        local currentPopBoxInfo = self.autoPopBoxInfo
        
        local scheduler = cc.Director:getInstance():getScheduler()
        local weakSelf = self
        local scheduleEntry = nil
        
        scheduleEntry = scheduler:scheduleScriptFunc(function()
            scheduler:unscheduleScriptEntry(scheduleEntry)
            
            if tolua.isnull(weakSelf) or not weakSelf:getParent() then
                return
            end
            
            if gGameUI.guideManager:isInGuiding() then
                return
            end
            
            if not currentPopBoxInfo or itertools.isempty(currentPopBoxInfo) then
                if sdk.loginInfo then
                    local t = json.decode(sdk.loginInfo)
                    sdk.loginInfo = nil
                    if t and t.channelId == "39" then
                        gGameUI:showDialog({
                            title = "提示",
                            content = string.format("由于平台运营策略调整，《口袋觉醒》华为平台返利代金券活动将会暂停2-3周时间，已有的代金券不受影响，调整结束后返利代金券活动将恢复正常。\n\n感谢您对游戏的支持和喜爱，祝您游戏愉快。"),
                            align = "left",
                            fontSize = 40,
                            dialogParams = {clickClose = true},
                        })
                    end
                end
                return
            end
            
            if tolua.isnull(weakSelf) or not weakSelf.autoPopBoxInfo or #weakSelf.autoPopBoxInfo == 0 then
                return
            end
            
            local data = weakSelf.autoPopBoxInfo[1]
            if data.viewName == "city.activity.poster" then
                userDefault.setForeverLocalKey("posterLoginShow", true, {rawKey = true})
            end
            
            table.remove(weakSelf.autoPopBoxInfo, 1)
            
            if tolua.isnull(weakSelf) or not weakSelf:getParent() then
                return
            end
            
            if data.func then
                data.func(function()
                    if not tolua.isnull(weakSelf) and weakSelf:getParent() then
                        weakSelf:stackUI(data.viewName, nil, data.styles, unpack(data.params or {}))
                    end
                end)
            else
                weakSelf:stackUI(data.viewName, nil, data.styles, unpack(data.params or {}))
            end
        end, 0, false)
    end


	if not game.hasCheckOnlineFight and gGameModel.role:read("in_cross_online_fight_battle") then
		game.hasCheckOnlineFight = true
		gGameUI:showDialog({
			content = "#C0x5B545B#" .. gLanguageCsv.onlineFightReconnection,
			isRich = true,
			cb = function()
				dataEasy.onlineFightLoginServer(self, normalPopBox)
			end,
			closeCb = normalPopBox,
			btnType = 2,
			clearFast = true,
		})
	else
		normalPopBox()
	end
end


function CityView:initAutoNewPlayerWeal()

	local newPlayerWeffare = gGameModel.currday_dispatch:getIdlerOrigin("newPlayerWeffare"):read()
	if newPlayerWeffare == true then
		return
	end
	idlereasy.when(self.autoNewPlayerWealId, function(_, id)
		if id > 0 then
			table.insert(self.autoPopBoxInfo, {
				viewName = "city.activity.recharge_feedback.new_player_welfare",
				params = {id},
				func = function(cb)
					gGameApp:requestServer("/game/yy/active/get", function(tb)
						cb()
					end)
				end,
			})
			self.autoNewPlayerWealId:set(-1)
		end
	end)
end

function CityView:initActivityTip(data)
	if not data then
		self.activityTip:hide()
		self.activityTip:removeChildByName("gojt")
		return
	end

	widget.addAnimationByKey(self.activityTip, "effect3q/go.skel", "gojt", "effect_loop", 1)
		:alignCenter(self.activityTip:size())
	local name = data.name or csv.unlock[gUnlockCsv[data.feature]].name
	adapt.setTextScaleWithWidth(self.activityTip:get("textNote1"), name, 180)
	adapt.setTextScaleWithWidth(self.activityTip:get("textNote2"), data.txt, 180)

	bind.touch(self, self.activityTip, {methods = {ended = function()
		if data.reminder == 2 then
			userDefault.setForeverLocalKey("sysOpen" .. data.sighid, tostring(gGameModel.role:read(data.roundKey)))
		else
			userDefault.setForeverLocalKey("sysOpen" .. data.sighid, time.getTodayStr())
		end
		jumpEasy.jumpTo(data.goto)
		if data.reminder ~= 0 then
			self.isRefresh:notify()
		end
	end}})

	self.activityTip:show()
end

function CityView:initGrowPanel()
	if userDefault.getForeverLocalKey("grow_guide_hidden", false) then
        return
    end

    local growGuideListen = {self.growGuideData}
    for _, v in ipairs(gGrowGuideCsv) do
        table.insert(growGuideListen, dataEasy.getListenUnlock(v.feature))
    end

    idlereasy.any(growGuideListen, function(_, growGuideData, ...)
        local unlocks = {...}
        local itemDatas = {}
        local count = 0
        local hasClaimableReward = false

        for _, v in ipairs(gGrowGuideCsv) do
            local csvId = v.id
            count = count + 1
            if not growGuideData or not growGuideData[csvId] or growGuideData[csvId][1] ~= 0 then
                local data = {}
                data.cfg = v
                data.csvId = csvId

                local state = 3
                if growGuideData and growGuideData[csvId] and growGuideData[csvId][1] == 1 then
                    state = 1
                    hasClaimableReward = true
                elseif not unlocks[count] then
                    state = 2
                end

                if v.feature == "craft" and state ~= 2 then
                    local state1, day = dataEasy.judgeServerOpen("craft")
                    if not state1 and day then
                        state = 2
                        data.serverDay = day
                    end
                end
                data.state = state
                table.insert(itemDatas, data)
            end
        end

        self.growGuideRedHint:set(hasClaimableReward)

        local hasItems = #itemDatas > 0

        if not hasItems then
            for i, btn in ipairs(self.leftBtns) do
                if btn.key == "grow_guide" then
                    table.remove(self.leftBtns, i)

                    userDefault.setForeverLocalKey("grow_guide_hidden", true)

                    if self.leftList and not tolua.isnull(self.leftList) then
                        self:safePerformWithDelay(function()
                            if self.leftList and not tolua.isnull(self.leftList) then
                                self.leftList:removeAllItems()

                                for _, btnData in ipairs(self.leftBtns) do
                                    local node = self.item_0:clone()
                                    node:get("icon"):texture(btnData.icon)

                                    if btnData.redHint and (btnData.key ~= "union" or gGameModel.union_training) then
                                        bind.extend(self.leftList, node, btnData.redHint)
                                    end

                                    bind.touch(self.leftList, node, {methods = {
                                        ended = function() self:onItemClick(self.leftList, btnData) end
                                    }})

                                    uiEasy.updateUnlockRes(btnData.unlockKey, node, {justRemove = not btnData.unlockKey, pos = cc.p(140, 140)})

                                    self.leftList:pushBackCustomItem(node)
                                end

                                self.leftList:forceDoLayout()
                            end
                        end, 0.1)
                    end
                    break
                end
            end
        else
            if self.leftList and not tolua.isnull(self.leftList) then
                for i, btn in ipairs(self.leftList:getChildren()) do
                    if btn:getName() == "grow_guide" then
                        btn:setVisible(true)
                        break
                    end
                end
            end
        end

        local useData, selIdx

        local target = 999
        for i,v in ipairs(itemDatas) do
            if v.state < target then
                useData = v
                target = v.state
                selIdx = i
            end
            if target == 1 then
                break
            end
        end
        if not useData then
            self.growGuide:hide()
            return
        end

        self.growGuide:get("textNote1"):text(useData.cfg.name)
        self.growGuide:removeChildByName("getEffect")
        self.growGuide:get("imgBg"):show()
        local str = ""

        if useData.state == 1  then
            str = gLanguageCsv.rewardCanGet
            self.growGuide:get("imgBg"):hide()
        elseif useData.state == 2 then
            local key = gUnlockCsv[useData.cfg.feature]
            local cfg = csv.unlock[key]
            str = string.format(gLanguageCsv.arrivalLevelOpen, cfg.startLevel)
            if useData.serverDay then
                str = string.format(gLanguageCsv.unlockServerOpen, useData.serverDay)
            end
        elseif useData.state == 3 then
            str = gLanguageCsv.stateFighting
        end
        self.growGuide:get("textNote2"):text(str)
        adapt.setTextScaleWithWidth(self.growGuide:get("textNote2"), nil, 170)
        self.growGuide:hide()

        bind.touch(self, self.growGuide, {methods = {ended = function()
            gGameUI:stackUI("city.grow_guide", nil, nil, selIdx)
        end}})
    end)
end

function CityView:achievementTip()
	local width = self.achievementPanel:width()
	local originX = self.rightPanel:size().width + width

	idlereasy.any({
		self.tasks,
		self.achiBoxes
	}, function(_, tasks, box)
		self:checkAchievementForGameUI()
	end):anonyOnly(self, "achievementTasks")
end

function CityView:checkAchievementForGameUI()
	if not dataEasy.isUnlock(gUnlockCsv.achievement) then
		return
	end

	if not gGameUI:findStackUI("city.view") or not gGameModel.role.achievement_queue then
		return
	end

	for csvId, state in pairs(gGameModel.role.achievement_queue) do
		gGameUI:showAchievement(csvId)
	end

	gGameModel.role.achievement_queue = nil
end

function CityView:onClickAchievement()
	if not dataEasy.isUnlock(gUnlockCsv.achievement) then
		return
	end
	self:stackUI("city.achievement", nil, {full = true})
end

function CityView.setCountdown(view, uiTime, params)
	view:enableSchedule()
	view:unSchedule(params.tag)
	local countTime = params.endTime - time.getTime()
	if countTime <= 0 then
		uiTime:hide()
		if params.cb then
			params.cb()
		end
		return
	end
	view:schedule(function()
		countTime = params.endTime - time.getTime()
		local times = time.getCutDown(countTime)
		local hour = times.day*24 + times.hour
		if times.day and times.day > 0 then
			uiTime:text(string.format("%s" .. gLanguageCsv.day,gLanguageCsv.exclusiveIconTime,times.day))
		else
	 	    uiTime:text(string.format("%02d:%02d:%02d",times.hour, times.min, times.sec))
	 	end

		if countTime <= 0 then
			CityView.setCountdown(view, uiTime, params)
		end
	end, 1, 0, params.tag)
end

function CityView:onReFreshRedHint()
	self.redHintRefresh:modify(function(val)
		return true, not val
	end)
end

function CityView:setGameSyncTimer()
	local timer = {5*3600, 21*3600}
	timer[#timer + 1] = timer[1] + 24 * 3600
	local currTime = time.getNowDate()
	local currSec = currTime.hour * 3600 + currTime.min*60 + currTime.sec
	local delta = 1
	for i = 1, #timer do
		if timer[i] > currSec then
			delta = timer[i] - currSec + 1
			break
		end
	end
	self:safePerformWithDelay(function()
		gGameApp:slientRequestServer("/game/sync", functools.handler(self, "setGameSyncTimer"))
	end, delta)
end

function CityView:setFishingGameTimer()

	local timer = {5*3600+15, 23*3600}
	timer[#timer + 1] = timer[1] + 24 * 3600
	local currTime = time.getNowDate()
	local currSec = currTime.hour * 3600 + currTime.min*60 + currTime.sec
	local delta = 1
	for i = 1, #timer do
		if timer[i] > currSec then
			delta = timer[i] - currSec + 1
			break
		end
	end
	self:safePerformWithDelay(function()
		gGameApp:slientRequestServer("/game/sync", functools.handler(self, "setFishingGameTimer"))
	end, delta)
end

function CityView:specialGiftLink()
	if matchLanguage({"kr"}) then

		local btn = ccui.ImageView:create("city/main/icon_krgift.png")
			:setTouchEnabled(true)
			:scale(0.95)
			:xy(128, 410)
		bind.touch(self, btn, {methods = { ended = function()
			gGameUI:stackUI("city.kr_gift_link_view")
		end}})
		self.rightBottomPanel:addChild(btn, 2, "krGift")
	end
end

function CityView:setSecialSupport()
	if matchLanguage({"en"}) then
		local btn = ccui.ImageView:create("login/icon_kfzx.png")
			:setTouchEnabled(true)
			:scale(0.86)
			:xy(443, 55)
		bind.touch(self, btn, {methods = {ended = function()
			cc.Application:getInstance():openURL(SUPPORT_URL)
		end}})
		self.leftBottomBtnPanel:addChild(btn, 2, "enSupport")

		local x, y = 443, 55
		if display.sizeInView.width / display.sizeInView.height >= 2.0 then
			x = x + 120
		else
			y = y - 120
		end

		local btnDiscord = ccui.ImageView:create("login/icon_discord.png")
			:setTouchEnabled(true)
			:scale(0.86)
			:xy(x, y)
		bind.touch(self, btnDiscord, {methods = {ended = function()
			cc.Application:getInstance():openURL(DISCORD_URL)
		end}})

	end
end


function CityView:setTimeLabel()
	if matchLanguage({"en"}) then
		self.daytime:visible(true)
		self.yeartime:visible(true)
		self:enableSchedule():schedule(function()
			local T = time.getTimeTable()
			self.yeartime:text(string.format("%02d/%02d/%04d",T.day,T.month,T.year))
			self.daytime:text(string.format("%02d:%02d",T.hour, T.min))
		end, 1, 0)
	else
		self.daytime:visible(false)
		self.yeartime:visible(false)
	end
end

function CityView:onWorld()
	gGameUI:showTip(gLanguageCsv.featureNotOpen)
    --gGameUI:stackUI("city.adventure.fishing.sence_select", nil, {full = true})
end

function CityView:getRuleContext(view)
    local c = adaptContext
    local context = {
        c.clone(view.title, function(item)
            item:get("text"):text(gLanguageCsv.featureNotOpen)
        end),


    }
    return context
end

function CityView:onBtnAdventureClick()
	if not dataEasy.isUnlock(gUnlockCsv.lichLuyen) then
		gGameUI:showTip(dataEasy.getUnlockTip(gUnlockCsv.lichLuyen))
		return
	else
   		return self:onItemClick(nil, self.mainBtns[2])
	end
end

function CityView:onBtnPvpClick()
	if not dataEasy.isUnlock(gUnlockCsv.dienVo) then
		gGameUI:showTip(dataEasy.getUnlockTip(gUnlockCsv.dienVo))
		return
	else
   		return self:onItemClick(nil, self.mainBtns[1])
	end
end

function CityView:initActivityCarousel()

    if self.activityCarouselPanel then
        self.activityCarouselPanel:removeFromParent()
        self.activityCarouselPanel = nil
    end


    if self._activityScrollTimer then
        cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self._activityScrollTimer)
        self._activityScrollTimer = nil
    end

    local activityData = {}
    local yyhuodongs = self.yyhuodongs:read()
    local yyOpen = gGameModel.role:read("yy_open") or {}
    for _, id in ipairs(yyOpen) do
        local cfg = csv.yunying.yyhuodong[id]
        if cfg and cfg.isShow == 1 then

            local hasRewards = false
            if yyhuodongs[id] and yyhuodongs[id].stamps then
                for k, v in pairs(yyhuodongs[id].stamps) do
                    if v == 1 then
                        hasRewards = true
                        break
                    end
                end
            end
            local iconPath = nil
            if cfg.bannerBig then
                iconPath = cfg.bannerBig
            elseif cfg.icon1 and cfg.icon1 > 0 then
                iconPath = string.format("3q/carousel_home/banner_event/event_small_%d.jpg", cfg.icon1)
            else
                iconPath = "3q/carousel_home/banner_event/event_small_1.jpg"
            end

            table.insert(activityData, {
                id = id,
                type = cfg.type,
                icon = iconPath,
				banner = cfg.bannerSmall,
                sortWeight = cfg.sortWeight or 0,
                hasRewards = hasRewards,
                name = cfg.name or ""
            })
        end
    end


    table.sort(activityData, function(a, b)
        return (a.sortWeight or 0) < (b.sortWeight or 0)
    end)
    self.allActivityData = activityData
    self.carouselPanel:show()
    local carouselSize = cc.size(405, 179)
	local bgBanner = ccui.ImageView:create("3q/carousel_home/bg_banner.png")
    bgBanner:setAnchorPoint(cc.p(0.5, 0.5))
    self.carouselPanel:addChild(bgBanner)

    local pageView = ccui.PageView:create()
    pageView:setContentSize(carouselSize)
    pageView:setAnchorPoint(cc.p(0.5, 0.5))

    pageView:setTouchEnabled(true)
    self.carouselPanel:addChild(pageView)
    self.activityPageView = pageView


    local dotContainer = ccui.Layout:create()
    dotContainer:setContentSize(cc.size(200, 20))
    dotContainer:setAnchorPoint(cc.p(0.5, 0.5))
    dotContainer:setPosition(cc.p(-50, -70))
    self.carouselPanel:addChild(dotContainer)


    self.activityInactiveDots = {}
    local dotSpacing = 30
    local activeDot = ccui.ImageView:create("3q/carousel_home/pic_slide_active.png")
    activeDot:setAnchorPoint(cc.p(0.5, 0.5))
    dotContainer:addChild(activeDot, 1)
    self.activityActiveDot = activeDot


    for i, entry in ipairs(activityData) do

        local layout = ccui.Layout:create()
        layout:setContentSize(carouselSize)


        local image = ccui.ImageView:create(entry.banner)
        image:setAnchorPoint(cc.p(0.5, 0.5))
        image:setPosition(cc.p(carouselSize.width/2, carouselSize.height/2))
        image:setScale9Enabled(true)
        image:setContentSize(carouselSize)
        layout:addChild(image)


        local spine = widget.addAnimationByKey(layout, "effect3q/effect_banner_event.skel", "spineBanner_" .. i, "eventsmall_loop", 99):scale(2)
        spine:setPosition(cc.p(carouselSize.width / 2, carouselSize.height / 2))
        -- local notif = ccui.ImageView:create("3q/icon_hongdian_s.png")
        -- notif:setAnchorPoint(cc.p(1, 1))
        -- notif:setPosition(cc.p(carouselSize.width - 10, carouselSize.height - 10))
        -- notif:setName("notif")
        -- notif:setVisible(entry.hasRewards)
        -- layout:addChild(notif, 2)
        layout.data = {
            id = entry.id,
            type = entry.type
        }


        pageView:addPage(layout)


        local inactiveDot = ccui.ImageView:create("3q/carousel_home/pic_slide_normal.png")
        inactiveDot:setAnchorPoint(cc.p(0.5, 0.5))
        inactiveDot:setPosition(cc.p((i - 1) * dotSpacing, 0))
        dotContainer:addChild(inactiveDot)
        table.insert(self.activityInactiveDots, inactiveDot)
    end


    if #self.activityInactiveDots > 0 then
        activeDot:setPosition(self.activityInactiveDots[1]:getPosition())
    end


    local pageIndicatorBg = ccui.ImageView:create("3q/carousel_home/icon_hongdian_s.png")
    pageIndicatorBg:setAnchorPoint(cc.p(0.5, 0.5))
    pageIndicatorBg:setPosition(cc.p(200, 94))
    self.carouselPanel:addChild(pageIndicatorBg)


	self.pageNumberIndicator = cc.LabelAtlas:create(count, battle.MainAreaRes.fontCP2, 30, 43, string.byte('0')):scale(0.7)
    self.pageNumberIndicator:setAnchorPoint(cc.p(0.5, 0.5))
    self.pageNumberIndicator:setPosition(cc.p(pageIndicatorBg:getContentSize().width/2,
                                             pageIndicatorBg:getContentSize().height/2))
    pageIndicatorBg:addChild(self.pageNumberIndicator)


    self:updatePageIndicator(1)


    local origX = self.carouselList:getPositionX()
    local origY = self.carouselList:getPositionY()
    local arrow = self.carouselListPanel:get("arrow")
    local aorigX = arrow:getPositionX()
    local aorigY = arrow:getPositionY()
    local offset = 300

    bind.touch(pageView, pageView, {methods = {ended = function()

        self:populateActivityList()


        self.carouselListPanel:show()
        self.carouselListPanel:stopAllActions()
        self.carouselListPanel:setCascadeOpacityEnabled(true)
        self.carouselListPanel:setOpacity(0)
        self.carouselListPanel:setVisible(true)
        self.carouselListPanel:runAction(cc.FadeIn:create(0.2))

        self.carouselList:setScale(1.2)
        self.carouselList:stopAllActions()
        self.carouselList:setPosition(origX - offset, origY)
        self.carouselList:setVisible(true)
        self.carouselList:runAction(cc.EaseBackOut:create(cc.MoveTo:create(0.5, cc.p(origX, origY))))

        arrow:stopAllActions()
        arrow:setPosition(aorigX - offset, aorigY)
        arrow:setVisible(true)
        arrow:runAction(cc.EaseBackOut:create(cc.MoveTo:create(0.5, cc.p(aorigX, aorigY))))
    end}})


    local function startAutoScroll()
        if self._activityScrollTimer then
            cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self._activityScrollTimer)
        end

        self._activityScrollTimer = cc.Director:getInstance():getScheduler():scheduleScriptFunc(function()
            if tolua.isnull(self) or tolua.isnull(pageView) then
                cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self._activityScrollTimer)
                return
            end

            local currentPage = pageView:getCurPageIndex()
            local nextPage = (currentPage + 1) % pageView:getChildrenCount()
            pageView:scrollToPage(nextPage)
            self:updateActivityDots(nextPage + 1)
            self:updatePageIndicator(nextPage + 1)
        end, 5, false)
    end


    startAutoScroll()


    pageView:addEventListener(function(sender, eventType)
        if eventType == ccui.PageViewEventType.turning then
            local pageIdx = pageView:getCurPageIndex() + 1
            self:updateActivityDots(pageIdx)
            self:updatePageIndicator(pageIdx)
            startAutoScroll()
        end
    end)


    self._activityUpdateTimer = cc.Director:getInstance():getScheduler():scheduleScriptFunc(function()
        if tolua.isnull(self) or tolua.isnull(pageView) then
            cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self._activityUpdateTimer)
            return
        end

        self:updateActivityStatus()
    end, 5, true)

    if self.activityPageView:getChildrenCount() == 0 then

        self.carouselPanel:hide()


        if self._activityScrollTimer then
            cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self._activityScrollTimer)
            self._activityScrollTimer = nil
        end

        if self._activityUpdateTimer then
            cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self._activityUpdateTimer)
            self._activityUpdateTimer = nil
        end

        return
    end


	idlereasy.when(self.yyhuodongs, function(_, yyData)
		self:updateFirstRechargeUI()
	end)

	-- Thêm trình lắng nghe cho các thay đổi của yyOpen idler.
    idlereasy.when(self.yyOpen, function(_, yyOpen)
        -- Chỉ làm mới nếu carousel đã được khởi tạo.
        if self.activityPageView and not tolua.isnull(self.activityPageView) then
            self:refreshActivityCarousel()
        end
    end):anonyOnly(self, "yyOpenCarouselListener")

    -- Đồng thời lắng nghe những thay đổi từ yyhuodongs để cập nhật trạng thái hoạt động.
    idlereasy.when(self.yyhuodongs, function(_, yyData)
        self:updateFirstRechargeUI()
        self:updateActivityStatus()
    end):anonyOnly(self, "yyhuodongsCarouselListener")
end


function CityView:populateActivityList()
    if not self.allActivityData or not self.carouselList then return end


    self.carouselList:removeAllChildren()


    local function onCarouselItemClicked(item, callback)
        item:runAction(cc.Sequence:create(
            cc.ScaleTo:create(0.05, 1.1),
            cc.ScaleTo:create(0.05, 1.0),
            cc.CallFunc:create(callback)
        ))
    end


    for i, entry in ipairs(self.allActivityData) do
        local item = self.carouselItem:clone()
        item:show()


        local iconNode = item:getChildByName("img")
        if iconNode then
            iconNode:texture(entry.icon)
        end


        local animKey = "listEventAnim_" .. i
        local anim = widget.addAnimationByKey(item:getChildByName("img"), "effect3q/effect_banner_event.skel", animKey, "event_loop", 99):scale(3.1)
        anim:setPosition(cc.p(item:getChildByName("img"):getContentSize().width/2, item:getChildByName("img"):getContentSize().height/2))
        item:getChildByName("img"):setAnchorPoint(cc.p(0.5, 0.5))


        item:getChildByName("notif"):setVisible(entry.hasRewards)


        -- local idLabel = ccui.Text:create("ID: " .. entry.id, "Arial", 16)
        -- idLabel:setAnchorPoint(cc.p(0, 0))
        -- idLabel:setPosition(cc.p(10, 10))
        -- idLabel:setTextColor(cc.c4b(255, 255, 255, 180))
        -- item:addChild(idLabel, 10)


        item:addClickEventListener(function()
            onCarouselItemClicked(item:getChildByName("img"), function()

                self.carouselListPanel:hide()


                self:openActivityById(entry.id, entry.type)
            end)
        end)

        self.carouselList:pushBackCustomItem(item)
    end


    local count = 0
    for _, item in ipairs(self.carouselList:getItems()) do
        if item:getChildByName("notif"):isVisible() then
            count = count + 1
        end
    end

    if self.pageIndicator then
        self.pageIndicator:setString(tostring(count))
    end
end


function CityView:openActivityById(activityId, activityType)
    if activityId == 880807 then
        gGameUI:stackUI("city.activity.first_recharge_daily", nil, nil, activityId)
        return
    end

	if activityType == YY_TYPE.timeLimitDraw then
		gGameUI:stackUI("city.drawcard.drawUR", nil, {full = true},activityId)
        return
    end

    local containerActivities = {
        [YY_TYPE.monthlyCard] = true,
        [YY_TYPE.itemExchange] = true,
        [YY_TYPE.generalTask] = true,
        [YY_TYPE.itemBuy] = true,
        [YY_TYPE.directBuyGift] = true,
        [YY_TYPE.clientShow] = true,
        [YY_TYPE.regainStamina] = true,
        [YY_TYPE.levelFund] = true,
        [YY_TYPE.rechargeGift] = true,
        [YY_TYPE.Retrieve] = true,
        [YY_TYPE.weeklyCard] = true,
        [YY_TYPE.qualityExchange] = true,
		[YY_TYPE.LoginGift] = true,
    }

    if containerActivities[activityType] then
        gGameApp:requestServer("/game/yy/active/get", function(tb)
            gGameUI:stackUI("city.activity.view", nil, {full = true}, "main", activityId)
        end)
        return
    end

    local activityInfo = self:getActivityViewInfo(activityType, activityId)

    if activityInfo and activityInfo.viewName then
		if activityType == YY_TYPE.serverOpen then
            local cfg = csv.yunying.yyhuodong[activityId]
            if cfg and cfg.clientParam and cfg.clientParam.type then
                if cfg.clientParam.type == "springFestival" then
                    activityInfo.viewName = "city.activity.server_open.view_spring_festival"
                elseif cfg.clientParam.type == "mayDay" or cfg.clientParam.type == "vacation" or cfg.clientParam.type == "national" then
                    activityInfo.viewName = "city.activity.server_open.view_may_day"
                elseif cfg.clientParam.type == "doubleYearsDay" then
                    activityInfo.viewName = "city.activity.server_open.view_double_years_day"
                elseif cfg.clientParam.type == "anniversary" then
                    activityInfo.viewName = "city.activity.server_open.view_anniversary"
                end
            end
		elseif activityType == YY_TYPE.customizeGift then
            local cfg = csv.yunying.yyhuodong[activityId]
            if cfg and cfg.clientParam and cfg.clientParam.type == "preferential" then
                activityInfo.viewName = "city.activity.preferential_goods"
            end
        end
        if activityInfo.func then
            activityInfo.func(function(...)
                local params = {activityId}
                for _, v in ipairs({...}) do
                    table.insert(params, v)
                end
                gGameUI:stackUI(activityInfo.viewName, nil, activityInfo.styles, unpack(params))
            end, {activityId})
        else

            if activityInfo.styles then
                gGameUI:stackUI(activityInfo.viewName, nil, activityInfo.styles, activityId)
            else
                gGameUI:stackUI(activityInfo.viewName, nil, nil, activityId)
            end
        end
    else
        gGameApp:requestServer("/game/yy/active/get", function(tb)
            gGameUI:stackUI("city.activity.view", nil, {full = true}, "main", activityId)
        end)
    end
end


function CityView:getActivityViewInfo(activityType, activityId)

    local YY_TYPE = game.YYHUODONG_TYPE_ENUM_TABLE


    local defaultFunc = function(cb)
        gGameApp:requestServer("/game/yy/active/get", function(tb)
            cb(tb)
        end)
    end


    local viewMapping = {

        [1] = { viewName = "city.activity.first_recharge" },
        [2] = { viewName = "city.activity.recharge_feedback.new_player_welfare", func = defaultFunc },
        [4] = { viewName = "city.activity.recharge_gift" },
        [6] = { viewName = "city.activity.month_card" },
        --[12] = { viewName = "city.activity.rmbgold_reward", func = defaultFunc },
        [14] = {
            viewName = "city.activity.lucky_cat",
            func = defaultFunc
        },
        [15] = {
            viewName = "city.activity.server_open.view",
            func = defaultFunc
        },
        [16] = {
            viewName = "city.activity.activity_fight_rank",
            func = function(cb, params)
                gGameApp:requestServer("/game/yy/fightrank/get", function(tb)
                    cb(tb)
                end, params[1])
            end
        },
        [22] = { viewName = "city.activity.level_fund", styles = {isLevel = false} },
        
        [24] = { viewName = "city.activity.coupon_shop", styles = {full = true}, func = defaultFunc },
        [25] = {
            viewName = "city.activity.passport.game_view",
            styles = {full = true},
            func = defaultFunc
        },
        [26] = {
            viewName = "city.activity.world_boss.view",
            styles = {full = true},
            func = function(cb, params)
                gGameApp:requestServer("/game/yy/world/boss/main", function(tb)
                    cb(tb)
                end, params[1])
            end
        },
        [29] = {
            viewName = "city.activity.horse_race.view",
            styles = {full = true},
            func = function(cb, params)
                gGameApp:requestServer("/game/yy/horse/race/main", function(tb)
                    cb(tb)
                end, params[1])
            end
        },
        [30] = { viewName = "city.activity.dispatch.view", styles = {full = true}, func = defaultFunc },
        [31] = {
            viewName = "city.activity.summer_challenge.view",
            styles = {full = true},
            func = defaultFunc
        },
		[35] = {
            viewName = "city.activity.passport.view",
            styles = {full = true},
            func = defaultFunc
        },
		[37] = { viewName = "city.activity.recharge_feedback.seven_day_login" },
        [48] = {
            viewName = "city.activity.gold_lucky_cat",
            func = defaultFunc
        },
        [54] = { viewName = "city.activity.exclusive_limit", func = defaultFunc },
		[58] = { viewName = "city.activity.rmbgold_reward", func = defaultFunc },
		[70] = { viewName = "city.activity.customize_gift", styles = {full = false}, func = defaultFunc }
    }

    return viewMapping[activityType]
end

function CityView:updateActivityDots(currentPageIndex)
    if not self.activityActiveDot or not self.activityInactiveDots then return end

    local idx = currentPageIndex or (self.activityPageView and self.activityPageView:getCurPageIndex() + 1 or 1)
    if idx > #self.activityInactiveDots then idx = 1 end

    if self.activityInactiveDots[idx] then
        self.activityActiveDot:setPosition(self.activityInactiveDots[idx]:getPosition())
    end
end

function CityView:updateActivityStatus()
    if not self.activityPageView or tolua.isnull(self.activityPageView) then return end

    local pages = self.activityPageView:getPages()
    if not pages then return end

    for i, page in ipairs(pages) do
        if not tolua.isnull(page) and page.data and page.data.id then
            local activityId = page.data.id
            local yyhuodongs = self.yyhuodongs:read()
            local yydata = yyhuodongs[activityId] or {}


            if yydata.flag == 2 then

                local cfg = csv.yunying.yyhuodong[activityId]
                if cfg and cfg.type == YY_TYPE.firstRecharge then

                    self:safePerformWithDelay(function()
                        if not tolua.isnull(self) and not tolua.isnull(self.activityPageView) then
                            self.activityPageView:removePage(page)

                            self:updateActivityDots()
                        end
                    end, 0.2)
                    return
                end
            end


            local hasRewards = false
            if yydata.stamps then
                for k, v in pairs(yydata.stamps) do
                    if v == 1 then
                        hasRewards = true
                        break
                    end
                end
            end

            local notif = page:getChildByName("notif")
            if notif and not tolua.isnull(notif) then
                notif:setVisible(hasRewards)
            end
        end
    end

    if self.activityPageView:getChildrenCount() > 0 then
        self:updatePageIndicator(self.activityPageView:getCurPageIndex() + 1)
    end
end

function CityView:onExit()

    if self._activityScrollTimer then
        cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self._activityScrollTimer)
        self._activityScrollTimer = nil
    end

    if self._activityUpdateTimer then
        cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self._activityUpdateTimer)
        self._activityUpdateTimer = nil
    end
end




function CityView:updateFirstRechargeUI()

    local yyhuodongs = self.yyhuodongs:read()
    if not yyhuodongs then return end


    local completedFirstRechargeIds = {}
    for id, yyData in pairs(yyhuodongs) do
        local cfg = csv.yunying.yyhuodong[id]
        if cfg and cfg.type == game.YYHUODONG_TYPE_ENUM_TABLE.firstRecharge and yyData.flag == 2 then
            completedFirstRechargeIds[id] = true
        end
    end


    if next(completedFirstRechargeIds) == nil then return end


    local needDotsUpdate = false
    if self.activityPageView and not tolua.isnull(self.activityPageView) then
        local pages = self.activityPageView:getPages()
        if pages then
            for i = #pages, 1, -1 do
                local page = pages[i]
                if page and not tolua.isnull(page) and page.data and completedFirstRechargeIds[page.data.id] then

                    local pageIndex = i
                    needDotsUpdate = true


                    self.activityPageView:removePageAtIndex(pageIndex - 1)


                    if self.activityInactiveDots and #self.activityInactiveDots >= pageIndex then

                        if not tolua.isnull(self.activityInactiveDots[pageIndex]) then
                            self.activityInactiveDots[pageIndex]:removeFromParent()
                        end
                        table.remove(self.activityInactiveDots, pageIndex)


                        local dotSpacing = 30
                        for j = 1, #self.activityInactiveDots do
                            if not tolua.isnull(self.activityInactiveDots[j]) then
                                self.activityInactiveDots[j]:setPosition(cc.p((j - 1) * dotSpacing, 0))
                            end
                        end
                    end
                end
            end


            if needDotsUpdate and self.activityActiveDot and not tolua.isnull(self.activityActiveDot) and
               self.activityInactiveDots and #self.activityInactiveDots > 0 then
                local currentPageIndex = self.activityPageView:getCurPageIndex() + 1
                if currentPageIndex > #self.activityInactiveDots then
                    currentPageIndex = 1

                    self.activityPageView:scrollToPage(0)
                end

                if currentPageIndex <= #self.activityInactiveDots and
                   not tolua.isnull(self.activityInactiveDots[currentPageIndex]) then
                    self.activityActiveDot:setPosition(
                        self.activityInactiveDots[currentPageIndex]:getPosition()
                    )
                end
            end


            if self.activityPageView:getChildrenCount() == 0 and self.carouselPanel then
                self.carouselPanel:setVisible(false)
            end
        end
    end


    if self.allActivityData then
        for i = #self.allActivityData, 1, -1 do
            local entry = self.allActivityData[i]
            if completedFirstRechargeIds[entry.id] then
                table.remove(self.allActivityData, i)
            end
        end
    end


    if self.carouselList and not tolua.isnull(self.carouselList) then

        self:populateActivityList()
    end


    if needDotsUpdate then
        self:updatePageIndicator(self.activityPageView:getCurPageIndex() + 1)
    end
end



function CityView:buildCarouselList(carouselItemsData)
    if not self.carouselList or tolua.isnull(self.carouselList) then return end


    self.firstRechargeItems = self.firstRechargeItems or {}
    self.firstRechargeItems = {}


    local filteredItems = {}
    for _, item in ipairs(carouselItemsData) do
        local cfg = csv.yunying.yyhuodong[item.id]
        if cfg and cfg.isShow == 1 then
            table.insert(filteredItems, item)
        end
    end


    for i, entry in ipairs(filteredItems) do

        local item = self:createCarouselListItem(entry, i)
        if item then
            self.carouselList:addChild(item)


            if entry.type == game.YYHUODONG_TYPE_ENUM_TABLE.firstRecharge then
                table.insert(self.firstRechargeItems, item)
            end
        end
    end


    if self.carouselList.requestDoLayout then
        self.carouselList:requestDoLayout()
    elseif self.carouselList.forceDoLayout then
        self.carouselList:forceDoLayout()
    end


    if self.carouselListPanel then
        self.carouselListPanel:setVisible(true)
    end
end


function CityView:createCarouselListItem(entry, index)




    local item = ccui.Layout:create()
    item:setContentSize(cc.size(300, 100))


    item.data = {
        id = entry.id,
        type = entry.type,
        viewName = entry.viewName
    }


    local img = ccui.ImageView:create(entry.icon)
    img:setName("img")
    img:setAnchorPoint(cc.p(0.5, 0.5))
    img:setPosition(cc.p(item:getContentSize().width/2, item:getContentSize().height/2))
    item:addChild(img)


    local notif = ccui.ImageView:create("3q/icon_hongdian_s.png")
    notif:setAnchorPoint(cc.p(1, 1))
    notif:setName("notif")
    notif:setPosition(cc.p(item:getContentSize().width - 10, item:getContentSize().height - 10))
    notif:setVisible(entry.hasRewards)
    item:addChild(notif)


    local function onCarouselItemClicked(target, callback)
        if target then
            target:runAction(cc.Sequence:create(
                cc.ScaleTo:create(0.05, 1.1),
                cc.ScaleTo:create(0.05, 1.0),
                cc.CallFunc:create(callback)
            ))
        else
            callback()
        end
    end

    item:addClickEventListener(function()
        onCarouselItemClicked(img, function()
            self:onItemClick(nil, item.data)
        end)
    end)

    return item
end


function CityView:refreshCarouselList()
    if not self.carouselList or tolua.isnull(self.carouselList) then
        return
    end


    if self.carouselList.forceDoLayout and type(self.carouselList.forceDoLayout) == "function" then
        self.carouselList:forceDoLayout()
    elseif self.carouselList.refreshView and type(self.carouselList.refreshView) == "function" then
        self.carouselList:refreshView()
    end


    if self.carouselList:getChildrenCount() == 0 and self.carouselListPanel then
        self.carouselListPanel:setVisible(false)
    end
end


function CityView:safeDelayedCall(callback, delay)

    local scheduler = cc.Director:getInstance():getScheduler()
    local scheduleEntry = nil


    scheduleEntry = scheduler:scheduleScriptFunc(function()

        if scheduleEntry then
            scheduler:unscheduleScriptEntry(scheduleEntry)
            scheduleEntry = nil
        end


        if not tolua.isnull(self) then
            callback()
        end
    end, delay or 0.1, false)


    return scheduleEntry
end


function CityView:updateFirstRechargeUI()

    local yyhuodongs = self.yyhuodongs:read()
    if not yyhuodongs then return end


    local completedFirstRechargeIds = {}
    for id, yyData in pairs(yyhuodongs) do
        local cfg = csv.yunying.yyhuodong[id]
        if cfg and cfg.type == game.YYHUODONG_TYPE_ENUM_TABLE.firstRecharge and yyData.flag == 2 then
            completedFirstRechargeIds[id] = true
        end
    end


    if next(completedFirstRechargeIds) == nil then return end


    self:safeDelayedCall(function()

        if tolua.isnull(self) then return end


        local needDotsUpdate = false
        if self.activityPageView and not tolua.isnull(self.activityPageView) then
            local pages = self.activityPageView:getPages()
            if pages then
                for i = #pages, 1, -1 do
                    local page = pages[i]
                    if page and not tolua.isnull(page) and page.data and completedFirstRechargeIds[page.data.id] then

                        local pageIndex = i
                        needDotsUpdate = true


                        self.activityPageView:removePageAtIndex(pageIndex - 1)


                        if self.activityInactiveDots and #self.activityInactiveDots >= pageIndex then

                            if not tolua.isnull(self.activityInactiveDots[pageIndex]) then
                                self.activityInactiveDots[pageIndex]:removeFromParent()
                            end
                            table.remove(self.activityInactiveDots, pageIndex)


                            local dotSpacing = 30
                            for j = 1, #self.activityInactiveDots do
                                if not tolua.isnull(self.activityInactiveDots[j]) then
                                    self.activityInactiveDots[j]:setPosition(cc.p((j - 1) * dotSpacing, 0))
                                end
                            end
                        end
                    end
                end


                if needDotsUpdate and self.activityActiveDot and not tolua.isnull(self.activityActiveDot) and
                   self.activityInactiveDots and #self.activityInactiveDots > 0 then
                    local currentPageIndex = self.activityPageView:getCurPageIndex() + 1
                    if currentPageIndex > #self.activityInactiveDots then
                        currentPageIndex = 1

                        self.activityPageView:scrollToPage(0)
                    end

                    if currentPageIndex <= #self.activityInactiveDots and
                       not tolua.isnull(self.activityInactiveDots[currentPageIndex]) then
                        self.activityActiveDot:setPosition(
                            self.activityInactiveDots[currentPageIndex]:getPosition()
                        )
                    end
                end


                if self.activityPageView:getChildrenCount() == 0 and self.carouselPanel then
                    self.carouselPanel:setVisible(false)
                end
            end
        end


        if self.allActivityData then
            for i = #self.allActivityData, 1, -1 do
                local entry = self.allActivityData[i]
                if completedFirstRechargeIds[entry.id] then
                    table.remove(self.allActivityData, i)
                end
            end
        end


        if self.carouselList and not tolua.isnull(self.carouselList) then

            self:populateActivityList()
        end
    end, 0.1)
end

function CityView:updatePageIndicator(currentPage)
    if self.pageNumberIndicator and not tolua.isnull(self.pageNumberIndicator) then

        local pages = self.activityPageView and self.activityPageView:getPages() or {}
        local validPages = {}


        for _, page in ipairs(pages) do
            if page and not tolua.isnull(page) and page.data then
                table.insert(validPages, page)
            end
        end

        local totalPages = #validPages

        if totalPages > 0 then

            if currentPage > totalPages then
                currentPage = totalPages
            end
            self.pageNumberIndicator:setString(string.format("%d", totalPages))
        else
            self.pageNumberIndicator:setString("0")
        end
    end
end


function CityView:isActivityShowInCarousel(id)
    local cfg = csv.yunying.yyhuodong[id]
    if not cfg then
        return false
    end


    if cfg.isShow ~= 1 then
        return false
    end
    return true
end

function CityView:onRechargeClick()
	self:stackUI("city.recharge", nil, {full = true})
end

function CityView:refreshActivityCarousel()
    -- Lưu chỉ mục trang hiện tại để khôi phục vị trí sau nếu có thể.
    local currentPageIndex = self.activityPageView and not tolua.isnull(self.activityPageView) and
                             self.activityPageView:getCurPageIndex() or 0

    -- Dọn dẹp các bộ đếm giờ hiện có.
    if self._activityScrollTimer then
        cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self._activityScrollTimer)
        self._activityScrollTimer = nil
    end

    if self._activityUpdateTimer then
        cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self._activityUpdateTimer)
        self._activityUpdateTimer = nil
    end

    -- Tạm thời lưu trữ dấu chấm nếu nó tồn tại.
    local dotContainer
    if self.activityPageView and not tolua.isnull(self.activityPageView) then
        dotContainer = self.activityPageView:getParent():getChildByName("dotContainer")
    end

    -- Xóa carousel hiện có.
    if self.activityPageView and not tolua.isnull(self.activityPageView) then
        self.activityPageView:removeAllPages()
        self.activityPageView:removeFromParent()
        self.activityPageView = nil
    end

    -- Xóa các chỉ báo dấu chấm.
    self.activityInactiveDots = {}
    if self.activityActiveDot and not tolua.isnull(self.activityActiveDot) then
        self.activityActiveDot:removeFromParent()
        self.activityActiveDot = nil
    end

    -- Khởi tạo lại cảousel.
    local activityData = {}
    local yyhuodongs = self.yyhuodongs:read()
    local yyOpen = self.yyOpen:read() or {}

    for _, id in ipairs(yyOpen) do
        local cfg = csv.yunying.yyhuodong[id]
        if cfg and cfg.isShow == 1 then
            local hasRewards = false
            if yyhuodongs[id] and yyhuodongs[id].stamps then
                for k, v in pairs(yyhuodongs[id].stamps) do
                    if v == 1 then
                        hasRewards = true
                        break
                    end
                end
            end

            local iconPath = nil
            if cfg.bannerBig then
                iconPath = cfg.bannerBig
            elseif cfg.icon1 and cfg.icon1 > 0 then
                iconPath = string.format("3q/carousel_home/banner_event/event_small_%d.jpg", cfg.icon1)
            else
                iconPath = "3q/carousel_home/banner_event/event_small_1.jpg"
            end

            table.insert(activityData, {
                id = id,
                type = cfg.type,
                icon = iconPath,
                banner = cfg.bannerSmall,
                sortWeight = cfg.sortWeight or 0,
                hasRewards = hasRewards,
                name = cfg.name or ""
            })
        end
    end

    table.sort(activityData, function(a, b)
        return (a.sortWeight or 0) < (b.sortWeight or 0)
    end)

    self.allActivityData = activityData

    if #activityData == 0 then
        self.carouselPanel:hide()
        return
    end

    self.carouselPanel:show()

    -- Tạo lại giao diện trang và các chỉ báo.
    local carouselSize = cc.size(405, 179)
    local pageView = ccui.PageView:create()
    pageView:setContentSize(carouselSize)
    pageView:setAnchorPoint(cc.p(0.5, 0.5))
    pageView:setTouchEnabled(true)
    self.carouselPanel:addChild(pageView)
    self.activityPageView = pageView

    local dotContainer = ccui.Layout:create()
    dotContainer:setContentSize(cc.size(200, 20))
    dotContainer:setAnchorPoint(cc.p(0.5, 0.5))
    dotContainer:setPosition(cc.p(-50, -70))
    dotContainer:setName("dotContainer")
    self.carouselPanel:addChild(dotContainer)

    self.activityInactiveDots = {}
    local activeDot = ccui.ImageView:create("3q/carousel_home/pic_slide_active.png")
    activeDot:setAnchorPoint(cc.p(0.5, 0.5))
    dotContainer:addChild(activeDot, 1)
    self.activityActiveDot = activeDot

    for i, entry in ipairs(activityData) do
        local layout = ccui.Layout:create()
        layout:setContentSize(carouselSize)

        local image = ccui.ImageView:create(entry.banner)
        image:setAnchorPoint(cc.p(0.5, 0.5))
        image:setPosition(cc.p(carouselSize.width/2, carouselSize.height/2))
        image:setScale9Enabled(true)
        image:setContentSize(carouselSize)
        layout:addChild(image)

        local spine = widget.addAnimationByKey(layout, "effect3q/effect_banner_event.skel", "spineBanner_" .. i, "eventsmall_loop", 99):scale(2)
        spine:setPosition(cc.p(carouselSize.width / 2, carouselSize.height / 2))

        layout.data = {
            id = entry.id,
            type = entry.type
        }

        pageView:addPage(layout)

        local inactiveDot = ccui.ImageView:create("3q/carousel_home/pic_slide_normal.png")
        inactiveDot:setAnchorPoint(cc.p(0.5, 0.5))
        inactiveDot:setPosition(cc.p((i - 1) * 30, 0))
        dotContainer:addChild(inactiveDot)
        table.insert(self.activityInactiveDots, inactiveDot)
    end

    if #self.activityInactiveDots > 0 then
        activeDot:setPosition(self.activityInactiveDots[1]:getPosition())
    end

    local pageIndicatorBg = ccui.ImageView:create("3q/carousel_home/icon_hongdian_s.png")
    pageIndicatorBg:setAnchorPoint(cc.p(0.5, 0.5))
    pageIndicatorBg:setPosition(cc.p(200, 94))
    self.carouselPanel:addChild(pageIndicatorBg)

    self.pageNumberIndicator = cc.LabelAtlas:create(tostring(#activityData), battle.MainAreaRes.fontCP2, 30, 43, string.byte('0')):scale(0.7)
    self.pageNumberIndicator:setAnchorPoint(cc.p(0.5, 0.5))
    self.pageNumberIndicator:setPosition(cc.p(pageIndicatorBg:getContentSize().width/2,
                                             pageIndicatorBg:getContentSize().height/2))
    pageIndicatorBg:addChild(self.pageNumberIndicator)

    -- Khôi phục vị trí trang nếu có thể, nếu không thì bắt đầu từ trang đầu tiên.
    local restoredPageIndex = math.min(currentPageIndex, pageView:getChildrenCount() - 1)
    if restoredPageIndex >= 0 then
        pageView:scrollToPage(restoredPageIndex)
    end

    self:updatePageIndicator(restoredPageIndex + 1)

    -- Gắn lại các trình lắng nghe sự kiện.
    local origX = self.carouselList:getPositionX()
    local origY = self.carouselList:getPositionY()
    local arrow = self.carouselListPanel:get("arrow")
    local aorigX = arrow:getPositionX()
    local aorigY = arrow:getPositionY()
    local offset = 300

    bind.touch(pageView, pageView, {methods = {ended = function()
        self:populateActivityList()

        self.carouselListPanel:show()
        self.carouselListPanel:stopAllActions()
        self.carouselListPanel:setCascadeOpacityEnabled(true)
        self.carouselListPanel:setOpacity(0)
        self.carouselListPanel:setVisible(true)
        self.carouselListPanel:runAction(cc.FadeIn:create(0.2))

        self.carouselList:setScale(1.2)
        self.carouselList:stopAllActions()
        self.carouselList:setPosition(origX - offset, origY)
        self.carouselList:setVisible(true)
        self.carouselList:runAction(cc.EaseBackOut:create(cc.MoveTo:create(0.5, cc.p(origX, origY))))

        arrow:stopAllActions()
        arrow:setPosition(aorigX - offset, aorigY)
        arrow:setVisible(true)
        arrow:runAction(cc.EaseBackOut:create(cc.MoveTo:create(0.5, cc.p(aorigX, aorigY))))
    end}})

    -- Thiết lập lại bộ đếm giờ tự động.
    local function startAutoScroll()
        if self._activityScrollTimer then
            cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self._activityScrollTimer)
        end

        self._activityScrollTimer = cc.Director:getInstance():getScheduler():scheduleScriptFunc(function()
            if tolua.isnull(self) or tolua.isnull(pageView) then
                cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self._activityScrollTimer)
                return
            end

            local currentPage = pageView:getCurPageIndex()
            local nextPage = (currentPage + 1) % pageView:getChildrenCount()
            pageView:scrollToPage(nextPage)
            self:updateActivityDots(nextPage + 1)
            self:updatePageIndicator(nextPage + 1)
        end, 5, false)
    end

    startAutoScroll()

    pageView:addEventListener(function(sender, eventType)
        if eventType == ccui.PageViewEventType.turning then
            local pageIdx = pageView:getCurPageIndex() + 1
            self:updateActivityDots(pageIdx)
            self:updatePageIndicator(pageIdx)
            startAutoScroll()
        end
    end)

    -- Thiết lập bộ đếm giờ cập nhật trạng thái hoạt động.
    self._activityUpdateTimer = cc.Director:getInstance():getScheduler():scheduleScriptFunc(function()
        if tolua.isnull(self) or tolua.isnull(pageView) then
            cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self._activityUpdateTimer)
            return
        end

        self:updateActivityStatus()
    end, 5, true)

    -- Cũng làm mới danh sách hoạt động trong panel.
    self:populateActivityList()
end

return CityView
