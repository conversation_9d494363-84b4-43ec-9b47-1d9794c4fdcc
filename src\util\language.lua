--
-- Copyright (c) 2014 YouMi Information Technology Inc.
-- Copyright (c) 2017 TianJi Information Technology Inc.
--
-- 语言本地化处理
--

local format = string.format
local insert = table.insert

function globals.getShowLanguage()
	local showLanguage = getBaseLanguage() or LOCAL_LANGUAGE
	if (APP_CHANNEL =="none" or APP_CHANNEL =="luo") then
		if SHOW_LANGUAGES then
			local lang = userDefault.getForeverLocalKey("SHOW_LANGUAGE", nil, {rawKey = true})
			-- 初始未选择显示语言，根据设备语言显示
			if not lang then
				local langCode = cc.Application:getInstance():getCurrentLanguageCode()
				if langCode == "vi" then
					lang = "vn"
				elseif langCode == "ko" then
					lang = "kr"
				elseif langCode == "id" then
					lang = "idn"
				elseif langCode == "th" then
					lang = "th"
				end
			end
			local hash = arraytools.hash(SHOW_LANGUAGES, true)
			if hash[lang] then
				showLanguage = lang
			end
		end
		return showLanguage
	else
		return "vn"
	end
end

-- 是否小语种
function globals.isShowLanguage()
	local showLanguage = getShowLanguage()
	return itertools.include({"th", "kr", "idn", "cn", "en"}, showLanguage)
end

-- 是否小语种 阿拉伯语需要支持 R2L, 重新设置使其变为 createWithSystemFont
function globals.isShowLanguageR2L()
	local showLanguage = getShowLanguage()
	return itertools.include({"are"}, showLanguage)
end

-- cn < en < en_us < esp < en_esp < en_us_esp
function globals.getShowLanguageList()
	local showLanguage = getShowLanguage()
	local baseLanguage = getBaseLanguage()
	local t = {}
	-- 三位国家码 为显示语言 优先级最高
	if #showLanguage == 3 then
		insert(t, format('%s_%s', LOCAL_LANGUAGE, showLanguage))
		if baseLanguage then
			insert(t, format('%s_%s', baseLanguage, showLanguage))
		end
		insert(t, showLanguage)

		insert(t, LOCAL_LANGUAGE)
		if baseLanguage then
			insert(t, baseLanguage)
		end
	else
		if baseLanguage == showLanguage then
			insert(t, LOCAL_LANGUAGE)
		end
		insert(t, showLanguage)
	end
	if not itertools.include(t, "vn") then
		insert(t, "vn")
	end
	return t
end

-- @desc en_us baseLanguage en
function globals.getBaseLanguage(language)
	language = language or LOCAL_LANGUAGE
	local p = string.find(language, '_')
	if p then
		return string.sub(language, 1, p-1)
	end
	return nil
end

-- @desc 返回本地化描述文字的字段
function globals.getL10nField(field, language)
	language = language or getShowLanguage()
	if language == 'vn' then
		return field
	else
		local path = format('%s_%s', field, language)
		local pathBase = getBaseLanguage(language)
		if pathBase then
			pathBase = format('%s_%s', field, pathBase)
		end
		return path, pathBase
	end
end

-- @desc 返回本地化描述文字
function globals.getL10nStr(csv, field, language)
	language = language or getShowLanguage()
	if language == 'vn' then
		return csv[field]
	else
		return csv[format('%s_%s', field, language)]
	end
end

-- @desc 判断是否是本地版本
function globals.checkLanguage(language)
	language = language or 'vn'
	return matchLanguage({language})
end

-- @desc 判断t是否包含本地语言, en_us 严格匹配
function globals.matchLanguageForce(t, language)
	language = language or LOCAL_LANGUAGE
	t = t or {}
	for k,v in pairs(t) do
		if v == language then
			return true
		end
	end
	return false
end

-- @desc 判断t是否包含本地语言, en_us 同 en
function globals.matchLanguage(t, language)
	language = language or LOCAL_LANGUAGE
	t = t or {}
	local baseLanguage = getBaseLanguage(language)
	for k,v in pairs(t) do
		if v == language or v == baseLanguage then
			return true
		end
	end
	return false
end

-- @desc 根据servKey获得tag
function globals.getServerTag(servKey)
	if servKey then
		return string.split(servKey, ".")[2]
	end
end

-- 从servKey中获取区服id
-- 默认判定合服处理, gamemerge.cn.1 -> csv.server.merge[servKey].serverID
function globals.getServerId(servKey, isOrgin)
	if gDestServer[servKey] then
		return gDestServer[servKey].id
	end
	if not isOrgin and gServersMergeID[servKey] then
		return csv.server.merge[gServersMergeID[servKey]].serverID
	end
	return tonumber(string.split(servKey, ".")[3])
end

-- 从servKey中获取区服area，如官方1区, showShort：true显示为 官方.S1
-- 默认判定合服处理, gamemerge.cn.1 -> 官方1区
-- 登录服和跨服中匹配的服务器需要显示给定服，玩家区服信息一般默认合服显示
function globals.getServerArea(servKey, showShort, isOrgin)
	local tag = getServerTag(servKey)
	local id = getServerId(servKey, isOrgin)
	local channelName = SERVER_MAP[tag] and SERVER_MAP[tag].name or ""
	if showShort then
		local str = "S" .. id
		if channelName ~= "" then
			str = string.format("%s.%s", channelName, str)
		end
		return str
	end
	return string.format("%s%d%s", channelName, id, (matchLanguageForce({"kr", "en_us", "en_eu"}) and "" or gLanguageCsv.serverArea))
end

-- 从servKey中获取区服名, 如皮卡丘
-- 默认判定合服处理
function globals.getServerName(servKey, isOrgin)
	local tag = getServerTag(servKey)
	local id = getServerId(servKey, isOrgin)
	local mergeKey = string.format("game.%s.%s", tag, id)
	if not SERVERS_INFO[mergeKey] then
		return ""
	end
	return SERVERS_INFO[mergeKey].name
end

-- 获得合服后的名称缩写, 非合服返回原名
function globals.getShortMergeRoleName(name)
	local gameKey = userDefault.getForeverLocalKey("serverKey", nil, {rawKey = true})
	if gServersMergeID[gameKey] then
		local id = getServerId(gameKey)
		local pos = string.find(name, string.format(".s%d$", id))
		if pos then
			return string.sub(name, 1, pos-1)
		end
	end
	return name
end

-- 判断是否包含当前服, servKey 可为 gamemerge.cn.1
function globals.isCurServerContainMerge(servKey)
	local gameKey = userDefault.getForeverLocalKey("serverKey", nil, {rawKey = true})
	if gDestServer[servKey] then
		return itertools.include(gDestServer[servKey].servers, gameKey)
	end
	return servKey == gameKey
end

-- key: pwAwardVer randomTowerAwardVer craftAwardVer
function globals.getVersionContainMerge(key)
	local gameKey = userDefault.getForeverLocalKey("serverKey", nil, {rawKey = true})
	local mergeId = gServersMergeID[gameKey]
	local version = mergeId and csv.server.merge[mergeId][key] or 0
	return version
end

function globals.getMergeServers(servers)
	local hash = {}
	-- 按序加入
	local mergeServers = {}
	for _, server in ipairs(servers) do
		local tag = getServerTag(server)
		local id = getServerId(server)
		local mergeKey = string.format("game.%s.%s", tag, id)
		if not hash[mergeKey] then
			hash[mergeKey] = true
			table.insert(mergeServers, mergeKey)
		end
	end
	return mergeServers
end

-- 判断本服的tag是不是cross跨服组。cn_huawei 属于 cn_qd 跨服组
-- cross dev or crossmine.dev.1
function globals.isServerTagInCross(cross)
	local gameKey = userDefault.getForeverLocalKey("serverKey", nil, {rawKey = true})
	local tag = getServerTag(gameKey)
	if tag == "cn_huawei" then
		tag = "cn_qd"
	end
	local crossTag = getServerTag(cross) or cross
	return tag == crossTag
end

-- @desc 根据当前语言获取voice路径
function globals.getVoicePath(basePath)
	if not basePath or basePath == "" then
		return basePath
	end
	
	local showLanguage = getShowLanguage()
	if isShowLanguage() and showLanguage ~= "vn" then
		local langPath = string.gsub(basePath, "res/sound/", string.format("res/sound_%s/", showLanguage))
		if cc.FileUtils:getInstance():isFileExist(langPath) then
			printInfo('Voice path found for language %s: %s', showLanguage, langPath)
			return langPath
		else
			printInfo('Voice path not found for language %s: %s, using default: %s', showLanguage, langPath, basePath)
		end
	end
	return basePath
end
