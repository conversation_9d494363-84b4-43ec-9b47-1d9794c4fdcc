
local GET_TYPE = {
	GOTTEN = 0, 	--已领取
	CAN_GOTTEN = 1, --可领取
	CAN_NOT_GOTTEN = 2, --未完成
}

local REWARD_TYPE = {
	FREE = 1,	-- 免费奖励
	PAID = 2,	-- 付费奖励
}

local ActivitySevenDayLoginDialog = class("ActivitySevenDayLoginDialog", Dialog)

ActivitySevenDayLoginDialog.RESOURCE_BINDING = {
	["btnClose"] = {
		varname = "btnClose",
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onClose")}
		},
	},
	["textCountDown"] = {
		varname = "textCountDown",
		binds = {
			{
				event = "effect",
				data = {outline = {color = cc.c4b(132, 29, 31, 255), size = 3}},
			},
		}
	},
	["textCountDown1"] = "textCountDown1",
	["item"] = "item",
	["iconItem"] = "iconItem",
	["list"] = {
		varname = "list",
		binds = {
			event = "extend",
			class = "listview",
			props = {
				data = bindHelper.self("itemsData"),
				item = bindHelper.self("item"),
				itemAction = {isAction = true},
				onItem = function(list, node, k, v)
					list.initItem(node, k, v)
				end,
				asyncPreload = 4,
			},
			handlers = {
				initItem = bindHelper.self("initItem"),
			},
		},
	},
}

function ActivitySevenDayLoginDialog:onCreate( activityId )
	Dialog.onCreate(self,{blackType = 1})
	self.activityId = activityId
	self:initModel()
	self:initData()
	self:initCountDown()
	gGameModel.currday_dispatch:getIdlerOrigin("newPlayerWeffare"):set(true)
end

-- 初始化model
function ActivitySevenDayLoginDialog:initModel()
	self.yyhuodongs = gGameModel.role:getIdler("yyhuodongs")
	self.itemsData = idlertable.new({}) -- 存放 奖励和领取状态
	self.date = idler.new("")  -- 活动时间
end
--初始化界面
function ActivitySevenDayLoginDialog:initData()
	-- 每日奖励数据
	local loginwealData = {}
	local yyCfg = csv.yunying.yyhuodong[self.activityId]
	local huodongID = yyCfg.huodongID
	for k, v in csvPairs(csv.yunying.loginweal) do
		if v.huodongID == huodongID then
			loginwealData[v.daySum] = {
				award = v.award,
				paidAward = v.paidAward,
				paidCost = v.paidCost,
				id = k
			}
		end
	end
	idlereasy.when(self.yyhuodongs,function(_, yyhuodong)
		local yydata = yyhuodong[self.activityId]
		local itemsData = {}
		for daySum, wealData in pairs(loginwealData) do
			-- 免费奖励状态
			local freeGetType = GET_TYPE.CAN_NOT_GOTTEN
			if yydata.stamps[wealData.id] ~= nil then
				freeGetType = yydata.stamps[wealData.id]
			end

			-- 付费奖励状态 (检查paid_stamps字段，基于对应天数是否可用)
			local paidId = -wealData.id
			local paidGetType = GET_TYPE.CAN_NOT_GOTTEN
			local paidStamps = yydata.paid_stamps or {}

			-- 付费奖励在对应天数可用时就能购买（不需要等免费奖励领取）
			if freeGetType == GET_TYPE.CAN_GOTTEN or freeGetType == GET_TYPE.GOTTEN then
				if paidStamps[wealData.id] == 1 then
					paidGetType = GET_TYPE.GOTTEN  -- 已购买
				else
					paidGetType = GET_TYPE.CAN_GOTTEN  -- 可购买
				end
			else
				-- 对应天数未到达，付费奖励不可购买
				paidGetType = GET_TYPE.CAN_NOT_GOTTEN
			end

			-- 计算付费奖励的当前价格和倒计时
			local currentPaidCost = wealData.paidCost
			local isPriceIncreased = false
			local timeUntilPriceIncrease = 0

			if wealData.paidCost and wealData.paidCost > 0 then
				local freeClaimTimes = yydata.free_claim_times or {}
				local claimTime = freeClaimTimes[wealData.id]

				if claimTime then
					-- 免费奖励已领取，检查是否超过24小时
					local currentTime = time.getTime()
					local timeSinceClaim = currentTime - claimTime

					if timeSinceClaim > 24 * 3600 then
						-- 超过24小时，价格增加1.5倍
						currentPaidCost = math.floor(wealData.paidCost * 1.5)
						isPriceIncreased = true
					else
						-- 还在24小时内，计算剩余时间
						timeUntilPriceIncrease = (24 * 3600) - timeSinceClaim
					end
				end
				-- 如果免费奖励未领取，则不显示倒计时，保持原价
			end

			itemsData[daySum] = {
				award = wealData.award,
				paidAward = wealData.paidAward,
				paidCost = wealData.paidCost,
				currentPaidCost = currentPaidCost,
				isPriceIncreased = isPriceIncreased,
				timeUntilPriceIncrease = timeUntilPriceIncrease,
				id = wealData.id,
				paidId = paidId,
				freeGetType = freeGetType,
				paidGetType = paidGetType
			}
		end
		dataEasy.tryCallFunc(self.list, "updatePreloadCenterIndex")
		self.itemsData:set(itemsData)
	end)
	-- 活动日期
	local _, beginMonth, beginDay = time.getYearMonthDay(yyCfg.beginDate)
	local _, endMonth, endDay = time.getYearMonthDay(yyCfg.endDate)
	self.date:set(beginMonth.."."..beginDay.."-"..endMonth.."."..endDay)

	-- 春节活动特殊处理 TODO 特殊在继承view里处理
	if self.springFestival then
		local pnode = self:getResourceNode()
		widget.addAnimationByKey(pnode, "login_gift_spring_festival/chunjieqiridenglu.skel","bg", "effect_h_loop", 0)
		:xy(pnode:width()/2, pnode:height()/2 + 50)
		:anchorPoint(cc.p(0.5,0.5))
		:scale(2)

		widget.addAnimationByKey(pnode, "login_gift_spring_festival/chunjieqiridenglu.skel", "detail","effect_q_loop", 4)
		:xy(pnode:width()/2, pnode:height()/2 + 50)
		:anchorPoint(cc.p(0.5,0.5))
		:scale(2)
		-- self.list:y(100)
	end
end

--倒计时
function ActivitySevenDayLoginDialog:initCountDown()
	local textTime = self.textCountDown
	local yyCfg = csv.yunying.yyhuodong[self.activityId]
	local hour, min = time.getHourAndMin(yyCfg.endTime)
	local endTime = time.getNumTimestamp(yyCfg.endDate,hour,min)
	local function setLabel()
		local remainTime = time.getCutDown(endTime - time.getTime())
		textTime:text(remainTime.str)
		if self.textCountDown1 then
			adapt.oneLinePos(self.textCountDown1,textTime, cc.p(5,0))
		end
		if endTime - time.getTime() <= 0 then
			textTime:text(gLanguageCsv.activityOver)
			self:unSchedule(1)
			return false
		end
		return true
	end
	self:enableSchedule()
	self:schedule(function(dt)
		if not setLabel() then
			return false
		end
	end, 1, 0, 1)
end

--初始化list的item
function ActivitySevenDayLoginDialog:initItem(list, node, k, itemData)
	local childs = node:multiget("textDay", "btnGet", "btnGetPaid", "list", "listPaid", "imgBg", "imgDayBg", "countDownPaid")

	-- 天数
	local dayNum = childs.textDay:setString(k)

	-- 免费奖励
	local awards = itemData.award
	local param = {}
	if self.springFestival then
		param = {
			onNode = function(panel, v )
				if v.key ~= "card" then
					panel:get("box"):texture("activity/spring_festival/box_cjhd.png")
					panel:get("imgFG"):hide()
					text.addEffect(panel:get("num"), {outline={color=cc.c4b(204, 61, 73,255)}})
				else
					panel.panel:get("imgBG"):texture("activity/spring_festival/box_cjhd.png")
					panel.panel:get("imgFG"):hide()
				end
			end
		}
	end
	uiEasy.createItemsToList(list, childs.list, awards, param)

	-- 付费奖励
	if itemData.paidAward then
		uiEasy.createItemsToList(list, childs.listPaid, itemData.paidAward, param)
	end

	-- 免费按钮处理
	self:setupButton(childs.btnGet, itemData.freeGetType, "textGet", gLanguageCsv.spaceReceive, itemData.id, REWARD_TYPE.FREE)

	-- 付费按钮处理
	if itemData.paidAward then
		-- 显示当前价格（可能已涨价）
		local currentCost = itemData.currentPaidCost or itemData.paidCost
		local paidBtnText = string.format("购买 %d钻", currentCost)

		-- 如果价格已上涨，显示原价和涨价提示
		if itemData.isPriceIncreased then
			paidBtnText = string.format("购买 %d钻\n(原价:%d)", currentCost, itemData.paidCost)
		end

		self:setupButton(childs.btnGetPaid, itemData.paidGetType, "textGet", paidBtnText, itemData.paidId, REWARD_TYPE.PAID)

		-- 倒计时显示逻辑：只有在免费奖励已领取后才显示24小时倒计时
		if itemData.freeGetType == GET_TYPE.GOTTEN and itemData.timeUntilPriceIncrease > 0 and not itemData.isPriceIncreased then
			self:setupPaidCountdown(childs.countDownPaid, itemData.timeUntilPriceIncrease, k)
			childs.countDownPaid:setVisible(true)
		else
			childs.countDownPaid:setVisible(false)
		end
	else
		-- 如果没有付费奖励，隐藏付费按钮和倒计时
		childs.btnGetPaid:setVisible(false)
		childs.countDownPaid:setVisible(false)
	end

	-- 背景处理
	if itemData.freeGetType == GET_TYPE.CAN_GOTTEN or itemData.paidGetType == GET_TYPE.CAN_GOTTEN then
		if self.springFestival then
			childs.imgBg:texture("activity/spring_festival/panel_qr1.png")
		else
			childs.imgBg:texture("activity/seven_day_login/panel_qr1.png")
		end
	else
		if self.springFestival then
			childs.imgBg:texture("activity/spring_festival/panel_qr0.png")
		else
			childs.imgBg:texture("activity/seven_day_login/panel_qr0.png")
		end
	end

	local centerPos = cc.p(node:get("imgDayBg"):xy())
	adapt.oneLineCenterPos(centerPos, {node:get("textDi"), node:get("imgDayBg"), node:get("textTian")}, cc.p(5, 0))
	childs.textDay:x(node:get("imgDayBg"):x())
end

-- 设置按钮状态的辅助函数
function ActivitySevenDayLoginDialog:setupButton(button, getType, textNodeName, defaultText, rewardId, rewardType)
	if getType == GET_TYPE.CAN_NOT_GOTTEN then
		-- 未达成
		if rewardType == REWARD_TYPE.FREE then
			local text = gLanguageCsv.haveNotLogin
			adapt.setTextScaleWithWidth(button:get(textNodeName), text, 250)
		else
			-- 付费奖励在对应天数未达成时不可购买
			local text = gLanguageCsv.haveNotLogin
			adapt.setTextScaleWithWidth(button:get(textNodeName), text, 250)
		end
		button:setEnabled(false)
		if self.springFestival then
			button:get(textNodeName):setTextColor(cc.c4b(156, 33, 51, 255))
		else
			button:get(textNodeName):setTextColor(cc.c4b(196, 92, 82, 255))
		end
	elseif getType == GET_TYPE.CAN_GOTTEN then
		-- 可领取
		adapt.setTextScaleWithWidth(button:get(textNodeName), defaultText, 250)
		button:get(textNodeName):setTextColor(cc.c4b(102, 36, 47, 255))
		button:setEnabled(true)
	elseif getType == GET_TYPE.GOTTEN then
		-- 已领取
		local text = (rewardType == REWARD_TYPE.FREE) and gLanguageCsv.received or "已购买"
		button:get(textNodeName):setString(text)
		if self.springFestival then
			button:get(textNodeName):setTextColor(cc.c4b(156, 33, 51, 255))
		else
			button:get(textNodeName):setTextColor(cc.c4b(196, 92, 82, 255))
		end
		button:setEnabled(false)
	end

	bind.touch(self, button, {methods = {ended = functools.partial(self.sendGetAward, self, rewardId, rewardType)}})
end
-- 发送领取
function ActivitySevenDayLoginDialog:sendGetAward(id, rewardType)
	if rewardType == REWARD_TYPE.PAID then
		-- 付费奖励需要先检查钻石是否足够，然后扣除钻石
		local itemData = nil
		for k, v in pairs(self.itemsData:read()) do
			if v.paidId == id then
				itemData = v
				break
			end
		end

		if itemData and itemData.paidCost then
			-- 使用当前价格（可能已涨价）
			local currentCost = itemData.currentPaidCost or itemData.paidCost
			local currentRmb = gGameModel.role:read("rmb") or 0
			if currentRmb < currentCost then
				uiEasy.showDialog("rmb") -- 显示钻石不足对话框
				return
			end

			-- 显示确认购买对话框，显示当前价格
			local content = string.format("是否花费 %d 钻石购买该礼包？", currentCost)
			if itemData.isPriceIncreased then
				content = string.format("是否花费 %d 钻石购买该礼包？\n(原价:%d，已涨价)", currentCost, itemData.paidCost)
			end

			gGameUI:showDialog({
				title = "购买确认",
				content = content,
				btnType = 2,
				cb = function()
					-- 发送购买付费奖励请求，使用特殊的负数ID来标识付费奖励
					gGameApp:requestServer("/game/yy/award/get", function(tb)
						gGameUI:showGainDisplay(tb)
					end, self.activityId, id)
				end
			})
		end
	else
		-- 免费奖励
		gGameApp:requestServer("/game/yy/award/get", function(tb)
			gGameUI:showGainDisplay(tb)
		end, self.activityId, id)
	end
end

-- 设置付费奖励倒计时
function ActivitySevenDayLoginDialog:setupPaidCountdown(countdownLabel, timeRemaining, dayIndex)
	-- 启动倒计时
	local countdownTag = 1000 + dayIndex  -- 使用唯一标签
	self:unSchedule(countdownTag)

	local remainingTime = timeRemaining
	local function updateCountdown()
		if remainingTime <= 0 then
			countdownLabel:setString("价格已上涨!")
			self:unSchedule(countdownTag)
			return false
		end

		local hours = math.floor(remainingTime / 3600)
		local minutes = math.floor((remainingTime % 3600) / 60)
		local seconds = remainingTime % 60

		local timeStr = string.format("%02d:%02d:%02d", hours, minutes, seconds)
		countdownLabel:setString("涨价倒计时\n" .. timeStr)

		remainingTime = remainingTime - 1
		return true
	end

	-- 立即更新一次
	updateCountdown()

	-- 启动定时器
	self:enableSchedule()
	self:schedule(function(dt)
		return updateCountdown()
	end, 1, 0, countdownTag)
end

return ActivitySevenDayLoginDialog










