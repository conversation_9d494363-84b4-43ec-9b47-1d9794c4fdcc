
local GET_TYPE = {
	GOTTEN = 0, 	--已领取
	CAN_GOTTEN = 1, --可领取
	CAN_NOT_GOTTEN = 2, --未完成
}

local REWARD_TYPE = {
	FREE = 1,	-- 免费奖励
	PAID = 2,	-- 付费奖励
}

local ActivitySevenDayLoginDialog = class("ActivitySevenDayLoginDialog", Dialog)

ActivitySevenDayLoginDialog.RESOURCE_BINDING = {
	["btnClose"] = {
		varname = "btnClose",
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onClose")}
		},
	},
	["textCountDown"] = {
		varname = "textCountDown",
		binds = {
			{
				event = "effect",
				data = {outline = {color = cc.c4b(132, 29, 31, 255), size = 3}},
			},
		}
	},
	["textCountDown1"] = "textCountDown1",
	["item"] = "item",
	["iconItem"] = "iconItem",
	["list"] = {
		varname = "list",
		binds = {
			event = "extend",
			class = "listview",
			props = {
				data = bindHelper.self("itemsData"),
				item = bindHelper.self("item"),
				itemAction = {isAction = true},
				onItem = function(list, node, k, v)
					list.initItem(node, k, v)
				end,
				asyncPreload = 4,
			},
			handlers = {
				initItem = bindHelper.self("initItem"),
			},
		},
	},
}

function ActivitySevenDayLoginDialog:onCreate( activityId )
	Dialog.onCreate(self,{blackType = 1})
	self.activityId = activityId
	self:initModel()
	self:initData()
	self:initCountDown()
	gGameModel.currday_dispatch:getIdlerOrigin("newPlayerWeffare"):set(true)
end

-- 初始化model
function ActivitySevenDayLoginDialog:initModel()
	self.yyhuodongs = gGameModel.role:getIdler("yyhuodongs")
	self.itemsData = idlertable.new({}) -- 存放 奖励和领取状态
	self.date = idler.new("")  -- 活动时间
end
--初始化界面
function ActivitySevenDayLoginDialog:initData()
	-- 每日奖励数据
	local loginwealData = {}
	local yyCfg = csv.yunying.yyhuodong[self.activityId]
	local huodongID = yyCfg.huodongID
	for k, v in csvPairs(csv.yunying.loginweal) do
		if v.huodongID == huodongID then
			loginwealData[v.daySum] = {
				award = v.award,
				paidAward = v.paidAward,
				paidCost = v.paidCost,
				id = k
			}
		end
	end
	idlereasy.when(self.yyhuodongs,function(_, yyhuodong)
		local yydata = yyhuodong[self.activityId]
		local itemsData = {}
		for daySum, wealData in pairs(loginwealData) do
			-- 免费奖励状态
			local freeGetType = GET_TYPE.CAN_NOT_GOTTEN
			if yydata.stamps[wealData.id] ~= nil then
				freeGetType = yydata.stamps[wealData.id]
			end

			-- 付费奖励状态 (使用负数ID来区分付费奖励)
			local paidId = -wealData.id
			local paidGetType = GET_TYPE.CAN_NOT_GOTTEN
			if yydata.stamps[paidId] ~= nil then
				paidGetType = yydata.stamps[paidId]
			end

			itemsData[daySum] = {
				award = wealData.award,
				paidAward = wealData.paidAward,
				paidCost = wealData.paidCost,
				id = wealData.id,
				paidId = paidId,
				freeGetType = freeGetType,
				paidGetType = paidGetType
			}
		end
		dataEasy.tryCallFunc(self.list, "updatePreloadCenterIndex")
		self.itemsData:set(itemsData)
	end)
	-- 活动日期
	local _, beginMonth, beginDay = time.getYearMonthDay(yyCfg.beginDate)
	local _, endMonth, endDay = time.getYearMonthDay(yyCfg.endDate)
	self.date:set(beginMonth.."."..beginDay.."-"..endMonth.."."..endDay)

	-- 春节活动特殊处理 TODO 特殊在继承view里处理
	if self.springFestival then
		local pnode = self:getResourceNode()
		widget.addAnimationByKey(pnode, "login_gift_spring_festival/chunjieqiridenglu.skel","bg", "effect_h_loop", 0)
		:xy(pnode:width()/2, pnode:height()/2 + 50)
		:anchorPoint(cc.p(0.5,0.5))
		:scale(2)

		widget.addAnimationByKey(pnode, "login_gift_spring_festival/chunjieqiridenglu.skel", "detail","effect_q_loop", 4)
		:xy(pnode:width()/2, pnode:height()/2 + 50)
		:anchorPoint(cc.p(0.5,0.5))
		:scale(2)
		-- self.list:y(100)
	end
end

--倒计时
function ActivitySevenDayLoginDialog:initCountDown()
	local textTime = self.textCountDown
	local yyCfg = csv.yunying.yyhuodong[self.activityId]
	local hour, min = time.getHourAndMin(yyCfg.endTime)
	local endTime = time.getNumTimestamp(yyCfg.endDate,hour,min)
	local function setLabel()
		local remainTime = time.getCutDown(endTime - time.getTime())
		textTime:text(remainTime.str)
		if self.textCountDown1 then
			adapt.oneLinePos(self.textCountDown1,textTime, cc.p(5,0))
		end
		if endTime - time.getTime() <= 0 then
			textTime:text(gLanguageCsv.activityOver)
			self:unSchedule(1)
			return false
		end
		return true
	end
	self:enableSchedule()
	self:schedule(function(dt)
		if not setLabel() then
			return false
		end
	end, 1, 0, 1)
end

--初始化list的item
function ActivitySevenDayLoginDialog:initItem(list, node, k, itemData)
	local childs = node:multiget("textDay", "btnGet", "list", "imgBg", "imgDayBg")
	-- 尝试获取付费相关的UI元素（如果存在的话）
	local btnGetPaid = node:getChildByName("btnGetPaid")
	local listPaid = node:getChildByName("listPaid")

	-- 天数
	local dayNum = childs.textDay:setString(k)

	-- 免费奖励
	local awards = itemData.award
	local param = {}
	if self.springFestival then
		param = {
			onNode = function(panel, v )
				if v.key ~= "card" then
					panel:get("box"):texture("activity/spring_festival/box_cjhd.png")
					panel:get("imgFG"):hide()
					text.addEffect(panel:get("num"), {outline={color=cc.c4b(204, 61, 73,255)}})
				else
					panel.panel:get("imgBG"):texture("activity/spring_festival/box_cjhd.png")
					panel.panel:get("imgFG"):hide()
				end
			end
		}
	end
	uiEasy.createItemsToList(list, childs.list, awards, param)

	-- 付费奖励（如果UI支持的话）
	if itemData.paidAward and listPaid then
		uiEasy.createItemsToList(list, listPaid, itemData.paidAward, param)
	elseif itemData.paidAward then
		-- 如果没有专门的付费奖励列表，将付费奖励添加到主列表的右侧
		-- 创建一个分隔符或标签来区分免费和付费奖励
		local separatorData = {["separator"] = "付费奖励", __size = 1}
		uiEasy.createItemsToList(list, childs.list, separatorData, param)
		uiEasy.createItemsToList(list, childs.list, itemData.paidAward, param)
	end

	-- 免费按钮处理
	if itemData.paidAward then
		-- 如果有付费奖励，需要特殊处理按钮绑定
		if btnGetPaid then
			-- 有专门的付费按钮，正常设置免费按钮
			local freeBtnText = "免费领取"
			self:setupButton(childs.btnGet, itemData.freeGetType, "textGet", freeBtnText, itemData.id, REWARD_TYPE.FREE)
		else
			-- 没有专门的付费按钮，使用组合按钮
			local freeBtnText = "免费领取"
			self:setupButton(childs.btnGet, itemData.freeGetType, "textGet", freeBtnText, itemData.id, REWARD_TYPE.FREE, true) -- 跳过绑定

			-- 在按钮上显示付费信息
			local originalText = childs.btnGet:get("textGet"):getString()
			local combinedText = originalText .. string.format("\n(长按购买 %d钻)", itemData.paidCost or 0)
			childs.btnGet:get("textGet"):setString(combinedText)

			-- 添加组合事件处理
			bind.touch(self, childs.btnGet, {
				methods = {
					ended = functools.partial(self.sendGetAward, self, itemData.id, REWARD_TYPE.FREE),
					longPress = functools.partial(self.sendGetAward, self, itemData.paidId, REWARD_TYPE.PAID)
				}
			})
		end
	else
		-- 只有免费奖励时使用原来的文本
		self:setupButton(childs.btnGet, itemData.freeGetType, "textGet", gLanguageCsv.spaceReceive, itemData.id, REWARD_TYPE.FREE)
	end

	-- 付费按钮处理（如果存在专门的付费按钮）
	if btnGetPaid and itemData.paidAward then
		local paidBtnText = string.format("购买 %d钻", itemData.paidCost or 0)
		self:setupButton(btnGetPaid, itemData.paidGetType, "textGetPaid", paidBtnText, itemData.paidId, REWARD_TYPE.PAID)
	end

	-- 背景处理
	if itemData.freeGetType == GET_TYPE.CAN_GOTTEN or itemData.paidGetType == GET_TYPE.CAN_GOTTEN then
		if self.springFestival then
			childs.imgBg:texture("activity/spring_festival/panel_qr1.png")
		else
			childs.imgBg:texture("activity/seven_day_login/panel_qr1.png")
		end
	else
		if self.springFestival then
			childs.imgBg:texture("activity/spring_festival/panel_qr0.png")
		else
			childs.imgBg:texture("activity/seven_day_login/panel_qr0.png")
		end
	end

	local centerPos = cc.p(node:get("imgDayBg"):xy())
	adapt.oneLineCenterPos(centerPos, {node:get("textDi"), node:get("imgDayBg"), node:get("textTian")}, cc.p(5, 0))
	childs.textDay:x(node:get("imgDayBg"):x())
end

-- 设置按钮状态的辅助函数
function ActivitySevenDayLoginDialog:setupButton(button, getType, textNodeName, defaultText, rewardId, rewardType, skipBinding)
	if getType == GET_TYPE.CAN_NOT_GOTTEN then
		-- 未达成
		local text = (rewardType == REWARD_TYPE.FREE) and gLanguageCsv.haveNotLogin or "未解锁"
		adapt.setTextScaleWithWidth(button:get(textNodeName), text, 250)
		button:setEnabled(false)
		if self.springFestival then
			button:get(textNodeName):setTextColor(cc.c4b(156, 33, 51, 255))
		else
			button:get(textNodeName):setTextColor(cc.c4b(196, 92, 82, 255))
		end
	elseif getType == GET_TYPE.CAN_GOTTEN then
		-- 可领取
		adapt.setTextScaleWithWidth(button:get(textNodeName), defaultText, 250)
		button:get(textNodeName):setTextColor(cc.c4b(102, 36, 47, 255))
		button:setEnabled(true)
	elseif getType == GET_TYPE.GOTTEN then
		-- 已领取
		local text = (rewardType == REWARD_TYPE.FREE) and gLanguageCsv.received or "已购买"
		button:get(textNodeName):setString(text)
		if self.springFestival then
			button:get(textNodeName):setTextColor(cc.c4b(156, 33, 51, 255))
		else
			button:get(textNodeName):setTextColor(cc.c4b(196, 92, 82, 255))
		end
		button:setEnabled(false)
	end

	-- 只有在没有跳过绑定的情况下才绑定事件
	if not skipBinding then
		bind.touch(self, button, {methods = {ended = functools.partial(self.sendGetAward, self, rewardId, rewardType)}})
	end
end
-- 发送领取
function ActivitySevenDayLoginDialog:sendGetAward(id, rewardType)
	if rewardType == REWARD_TYPE.PAID then
		-- 付费奖励需要先检查钻石是否足够，然后扣除钻石
		local itemData = nil
		for k, v in pairs(self.itemsData:read()) do
			if v.paidId == id then
				itemData = v
				break
			end
		end

		if itemData and itemData.paidCost then
			local currentRmb = gGameModel.role:read("rmb") or 0
			if currentRmb < itemData.paidCost then
				uiEasy.showDialog("rmb") -- 显示钻石不足对话框
				return
			end

			-- 显示确认购买对话框
			local content = string.format("是否花费 %d 钻石购买该礼包？", itemData.paidCost)
			gGameUI:showDialog({
				title = "购买确认",
				content = content,
				btnType = 2,
				cb = function()
					-- 发送购买付费奖励请求，使用特殊的负数ID来标识付费奖励
					gGameApp:requestServer("/game/yy/award/get", function(tb)
						gGameUI:showGainDisplay(tb)
					end, self.activityId, id)
				end
			})
		end
	else
		-- 免费奖励
		gGameApp:requestServer("/game/yy/award/get", function(tb)
			gGameUI:showGainDisplay(tb)
		end, self.activityId, id)
	end
end


return ActivitySevenDayLoginDialog










