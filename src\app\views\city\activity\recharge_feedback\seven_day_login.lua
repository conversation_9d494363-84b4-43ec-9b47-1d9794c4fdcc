
local GET_TYPE = {
	GOTTEN = 0, 	-- Claimed
	CAN_GOTTEN = 1, -- Can claim
	CAN_NOT_GOTTEN = 2, -- Not completed
}

local REWARD_TYPE = {
	FREE = 1,	-- Free reward
	PAID = 2,	-- Paid reward
}

local ActivitySevenDayLoginDialog = class("ActivitySevenDayLoginDialog", Dialog)

ActivitySevenDayLoginDialog.RESOURCE_BINDING = {
	["btnClose"] = {
		varname = "btnClose",
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onClose")}
		},
	},
	["textCountDown"] = {
		varname = "textCountDown",
		binds = {
			{
				event = "effect",
				data = {outline = {color = cc.c4b(132, 29, 31, 255), size = 3}},
			},
		}
	},
	["textCountDown1"] = "textCountDown1",
	["item"] = "item",
	["iconItem"] = "iconItem",
	["list"] = {
		varname = "list",
		binds = {
			event = "extend",
			class = "listview",
			props = {
				data = bindHelper.self("itemsData"),
				item = bindHelper.self("item"),
				itemAction = {isAction = true},
				onItem = function(list, node, k, v)
					list.initItem(node, k, v)
				end,
				asyncPreload = 4,
			},
			handlers = {
				initItem = bindHelper.self("initItem"),
			},
		},
	},
}

function ActivitySevenDayLoginDialog:onCreate( activityId )
	Dialog.onCreate(self,{blackType = 1})
	self.activityId = activityId
	self:initModel()
	self:initData()
	self:initCountDown()
	gGameModel.currday_dispatch:getIdlerOrigin("newPlayerWeffare"):set(true)
end

-- Initialize model
function ActivitySevenDayLoginDialog:initModel()
	self.yyhuodongs = gGameModel.role:getIdler("yyhuodongs")
	self.itemsData = idlertable.new({}) -- Store rewards and claim status
	self.date = idler.new("")  -- Activity time
end
-- Initialize interface
function ActivitySevenDayLoginDialog:initData()
	-- Daily reward data
	local loginwealData = {}
	local yyCfg = csv.yunying.yyhuodong[self.activityId]
	local huodongID = yyCfg.huodongID
	for k, v in csvPairs(csv.yunying.loginweal) do
		if v.huodongID == huodongID then
			loginwealData[v.daySum] = {
				award = v.award,
				paidAward = v.paidAward,
				paidCost = v.paidCost,
				id = k
			}
		end
	end
	idlereasy.when(self.yyhuodongs,function(_, yyhuodong)
		local yydata = yyhuodong[self.activityId]
		local itemsData = {}
		for daySum, wealData in pairs(loginwealData) do
			-- Free reward status
			local freeGetType = GET_TYPE.CAN_NOT_GOTTEN
			if yydata.stamps[wealData.id] ~= nil then
				freeGetType = yydata.stamps[wealData.id]
			end

			-- Paid reward status (check paid_stamps field, only available after free reward is claimed)
			local paidId = -wealData.id
			local paidGetType = GET_TYPE.CAN_NOT_GOTTEN
			local paidStamps = yydata.paid_stamps or {}

			-- Paid rewards can only be purchased after free reward has been claimed
			if freeGetType == GET_TYPE.GOTTEN then
				if paidStamps[wealData.id] == 1 then
					paidGetType = GET_TYPE.GOTTEN  -- Already purchased
				else
					paidGetType = GET_TYPE.CAN_GOTTEN  -- Can purchase (only after free reward claimed)
				end
			else
				-- Free reward not claimed yet, paid reward cannot be purchased
				paidGetType = GET_TYPE.CAN_NOT_GOTTEN
			end

			-- Calculate current price and countdown for paid rewards
			local currentPaidCost = wealData.paidCost
			local isPriceIncreased = false
			local timeUntilPriceIncrease = 0

			if wealData.paidCost and wealData.paidCost > 0 then
				local freeClaimTimes = yydata.free_claim_times or {}
				local claimTime = freeClaimTimes[wealData.id]

				if claimTime then
					-- Free reward claimed, check if 24 hours have passed
					local currentTime = time.getTime()
					local timeSinceClaim = currentTime - claimTime

					if timeSinceClaim > 24 * 3600 then
						-- Over 24 hours, price increases to 1.5x
						currentPaidCost = math.floor(wealData.paidCost * 1.5)
						isPriceIncreased = true
					else
						-- Still within 24 hours, calculate remaining time
						timeUntilPriceIncrease = (24 * 3600) - timeSinceClaim
					end
				end
				-- If free reward not claimed, no countdown shown, keep original price
			end

			itemsData[daySum] = {
				award = wealData.award,
				paidAward = wealData.paidAward,
				paidCost = wealData.paidCost,
				currentPaidCost = currentPaidCost,
				isPriceIncreased = isPriceIncreased,
				timeUntilPriceIncrease = timeUntilPriceIncrease,
				id = wealData.id,
				paidId = paidId,
				freeGetType = freeGetType,
				paidGetType = paidGetType
			}
		end
		dataEasy.tryCallFunc(self.list, "updatePreloadCenterIndex")
		self.itemsData:set(itemsData)
	end)
	-- Activity date
	local _, beginMonth, beginDay = time.getYearMonthDay(yyCfg.beginDate)
	local _, endMonth, endDay = time.getYearMonthDay(yyCfg.endDate)
	self.date:set(beginMonth.."."..beginDay.."-"..endMonth.."."..endDay)

	-- Spring Festival activity special handling TODO handle special cases in inherited view
	if self.springFestival then
		local pnode = self:getResourceNode()
		widget.addAnimationByKey(pnode, "login_gift_spring_festival/chunjieqiridenglu.skel","bg", "effect_h_loop", 0)
		:xy(pnode:width()/2, pnode:height()/2 + 50)
		:anchorPoint(cc.p(0.5,0.5))
		:scale(2)

		widget.addAnimationByKey(pnode, "login_gift_spring_festival/chunjieqiridenglu.skel", "detail","effect_q_loop", 4)
		:xy(pnode:width()/2, pnode:height()/2 + 50)
		:anchorPoint(cc.p(0.5,0.5))
		:scale(2)
		-- self.list:y(100)
	end
end

-- Countdown
function ActivitySevenDayLoginDialog:initCountDown()
	local textTime = self.textCountDown
	local yyCfg = csv.yunying.yyhuodong[self.activityId]
	local hour, min = time.getHourAndMin(yyCfg.endTime)
	local endTime = time.getNumTimestamp(yyCfg.endDate,hour,min)
	local function setLabel()
		local remainTime = time.getCutDown(endTime - time.getTime())
		textTime:text(remainTime.str)
		if self.textCountDown1 then
			adapt.oneLinePos(self.textCountDown1,textTime, cc.p(5,0))
		end
		if endTime - time.getTime() <= 0 then
			textTime:text(gLanguageCsv.activityOver)
			self:unSchedule(1)
			return false
		end
		return true
	end
	self:enableSchedule()
	self:schedule(function(dt)
		if not setLabel() then
			return false
		end
	end, 1, 0, 1)
end

-- Initialize list item
function ActivitySevenDayLoginDialog:initItem(list, node, k, itemData)
	local childs = node:multiget("textDay", "btnGet", "btnGetPaid", "list", "listPaid", "imgBg", "imgDayBg", "countDownPaid")

	-- Day number
	local dayNum = childs.textDay:setString(string.format(gLanguageCsv.loginGiftDay, k))

	-- Free reward
	local awards = itemData.award
	local param = {}
	if self.springFestival then
		param = {
			onNode = function(panel, v )
				if v.key ~= "card" then
					panel:get("box"):texture("activity/spring_festival/box_cjhd.png")
					panel:get("imgFG"):hide()
					text.addEffect(panel:get("num"), {outline={color=cc.c4b(204, 61, 73,255)}})
				else
					panel.panel:get("imgBG"):texture("activity/spring_festival/box_cjhd.png")
					panel.panel:get("imgFG"):hide()
				end
			end
		}
	end
	uiEasy.createItemsToList(list, childs.list, awards, param)

	-- Paid reward
	if itemData.paidAward then
		uiEasy.createItemsToList(list, childs.listPaid, itemData.paidAward, param)
	end

	-- Free button handling
	self:setupButton(childs.btnGet, itemData.freeGetType, "textGet", gLanguageCsv.spaceReceive, itemData.id, REWARD_TYPE.FREE)

	-- Paid button handling
	if itemData.paidAward then
		-- Display current price (may have increased)
		local currentCost = itemData.currentPaidCost or itemData.paidCost
		local paidBtnText = string.format(gLanguageCsv.loginGiftBuyDiamond, currentCost)

		-- If price has increased, show original price and increase notice
		if itemData.isPriceIncreased then
			paidBtnText = string.format(gLanguageCsv.loginGiftBuyDiamond .. "(Original:%d)", currentCost, itemData.paidCost)
		end

		self:setupButton(childs.btnGetPaid, itemData.paidGetType, "textGet", paidBtnText, itemData.paidId, REWARD_TYPE.PAID)

		-- Countdown display logic: only show 24-hour countdown after free reward is claimed and paid reward not purchased
		if itemData.freeGetType == GET_TYPE.GOTTEN and itemData.paidGetType ~= GET_TYPE.GOTTEN and itemData.timeUntilPriceIncrease > 0 and not itemData.isPriceIncreased then
			self:setupPaidCountdown(childs.countDownPaid, itemData.timeUntilPriceIncrease, k)
			childs.countDownPaid:setVisible(true)
		else
			childs.countDownPaid:setVisible(false)
		end
	else
		-- If no paid reward, hide paid button and countdown
		childs.btnGetPaid:setVisible(false)
		childs.countDownPaid:setVisible(false)
	end

	-- Background handling
	if itemData.freeGetType == GET_TYPE.CAN_GOTTEN or itemData.paidGetType == GET_TYPE.CAN_GOTTEN then
		if self.springFestival then
			childs.imgBg:texture("activity/spring_festival/panel_qr1.png")
		else
			childs.imgBg:texture("activity/seven_day_login/panel_qr1.png")
		end
	else
		if self.springFestival then
			childs.imgBg:texture("activity/spring_festival/panel_qr0.png")
		else
			childs.imgBg:texture("activity/seven_day_login/panel_qr0.png")
		end
	end

	local centerPos = cc.p(node:get("imgDayBg"):xy())
	adapt.oneLineCenterPos(centerPos, {node:get("textDi"), node:get("imgDayBg"), node:get("textTian")}, cc.p(5, 0))
	childs.textDay:x(node:get("imgDayBg"):x())
end

-- Helper function to set button state
function ActivitySevenDayLoginDialog:setupButton(button, getType, textNodeName, defaultText, rewardId, rewardType)
	if getType == GET_TYPE.CAN_NOT_GOTTEN then
		-- Not achieved
		if rewardType == REWARD_TYPE.FREE then
			local text = gLanguageCsv.loginGiftNotCompleted
			adapt.setTextScaleWithWidth(button:get(textNodeName), text, 250)
		else
			-- Paid rewards cannot be purchased when corresponding day is not achieved
			local text = gLanguageCsv.loginGiftNotCompleted
			adapt.setTextScaleWithWidth(button:get(textNodeName), text, 250)
		end
		button:setEnabled(false)
		if self.springFestival then
			button:get(textNodeName):setTextColor(cc.c4b(156, 33, 51, 255))
		else
			button:get(textNodeName):setTextColor(cc.c4b(196, 92, 82, 255))
		end
	elseif getType == GET_TYPE.CAN_GOTTEN then
		-- Can claim
		adapt.setTextScaleWithWidth(button:get(textNodeName), defaultText, 250)
		button:get(textNodeName):setTextColor(cc.c4b(102, 36, 47, 255))
		button:setEnabled(true)
	elseif getType == GET_TYPE.GOTTEN then
		-- Already claimed
		local text = (rewardType == REWARD_TYPE.FREE) and gLanguageCsv.loginGiftClaimed or gLanguageCsv.loginGiftPurchased
		button:get(textNodeName):setString(text)
		if self.springFestival then
			button:get(textNodeName):setTextColor(cc.c4b(156, 33, 51, 255))
		else
			button:get(textNodeName):setTextColor(cc.c4b(196, 92, 82, 255))
		end
		button:setEnabled(false)
	end

	bind.touch(self, button, {methods = {ended = functools.partial(self.sendGetAward, self, rewardId, rewardType)}})
end
-- Send claim
function ActivitySevenDayLoginDialog:sendGetAward(id, rewardType)
	if rewardType == REWARD_TYPE.PAID then
		-- Paid rewards need to check if diamonds are sufficient first, then deduct diamonds
		local itemData = nil
		for k, v in pairs(self.itemsData:read()) do
			if v.paidId == id then
				itemData = v
				break
			end
		end

		if itemData and itemData.paidCost then
			-- Use current price (may have increased)
			local currentCost = itemData.currentPaidCost or itemData.paidCost
			local currentRmb = gGameModel.role:read("rmb") or 0
			if currentRmb < currentCost then
				uiEasy.showDialog("rmb") -- Show insufficient diamonds dialog
				return
			end

			-- Show purchase confirmation dialog, display current price
			local content = string.format(gLanguageCsv.loginGiftBuyDialog, currentCost)
			if itemData.isPriceIncreased then
				content = string.format(gLanguageCsv.loginGiftBuyDialog .. "\n(Original:%d, Price Increased)", currentCost, itemData.paidCost)
			end

			gGameUI:showDialog({
				title = gLanguageCsv.loginGiftBuyConfirm,
				content = content,
				btnType = 2,
				cb = function()
					-- Send purchase paid reward request, use special negative ID to identify paid reward
					gGameApp:requestServer("/game/yy/award/get", function(tb)
						gGameUI:showGainDisplay(tb)
					end, self.activityId, id)
				end
			})
		end
	else
		-- Free reward
		gGameApp:requestServer("/game/yy/award/get", function(tb)
			gGameUI:showGainDisplay(tb)
		end, self.activityId, id)
	end
end

-- Set up paid reward countdown
function ActivitySevenDayLoginDialog:setupPaidCountdown(countdownLabel, timeRemaining, dayIndex)
	-- Start countdown
	local countdownTag = 1000 + dayIndex  -- Use unique tag
	self:unSchedule(countdownTag)

	local remainingTime = timeRemaining
	local function updateCountdown()
		if remainingTime <= 0 then
			countdownLabel:setString(gLanguageCsv.loginGiftPriceIncreased)
			self:unSchedule(countdownTag)
			return false
		end

		local hours = math.floor(remainingTime / 3600)
		local minutes = math.floor((remainingTime % 3600) / 60)
		local seconds = remainingTime % 60

		local timeStr = string.format("%02d:%02d:%02d", hours, minutes, seconds)
		countdownLabel:setString(gLanguageCsv.loginGiftPriceIncrease .. "\n" .. timeStr)

		remainingTime = remainingTime - 1
		return true
	end

	-- Update immediately once
	updateCountdown()

	-- Start timer
	self:enableSchedule()
	self:schedule(function(dt)
		return updateCountdown()
	end, 1, 0, countdownTag)
end

return ActivitySevenDayLoginDialog










